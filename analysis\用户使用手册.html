<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户使用手册</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 10px;
            font-size: 2.5rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .subtitle {
            text-align: center;
            color: #7f8c8d;
            margin-bottom: 30px;
            font-size: 1.1rem;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }
        h3 {
            color: #2980b9;
            margin-top: 20px;
        }
        .feature-card {
            background: #f8f9fa;
            padding: 20px;
            margin: 15px 0;
            border-radius: 10px;
            border-left: 4px solid #3498db;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .dashboard { border-left-color: #28a745; }
        .finance { border-left-color: #ffc107; }
        .project { border-left-color: #dc3545; }
        .work { border-left-color: #6f42c1; }
        .screenshot {
            background: #e9ecef;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            margin: 15px 0;
            border: 2px dashed #adb5bd;
        }
        .step-list {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .step-list ol {
            margin: 0;
            padding-left: 20px;
        }
        .tip-box {
            background: #e7f3ff;
            border: 1px solid #b8daff;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .table th, .table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
            font-size: 14px;
        }
        .table th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            font-weight: 600;
        }
        .nav-menu {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .nav-menu ul {
            list-style-type: none;
            padding: 0;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }
        .nav-menu li {
            background: white;
            padding: 10px;
            border-radius: 5px;
            border-left: 3px solid #3498db;
        }
        .nav-menu a {
            text-decoration: none;
            color: #2c3e50;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📖 用户使用手册</h1>
        <p class="subtitle">系统功能使用指南和操作说明</p>
        
        <h2>🏠 系统概览</h2>
        
        <div class="feature-card">
            <h3>系统简介</h3>
            <p>本系统是一个综合性的工程项目管理和经营分析平台，主要用于：</p>
            <ul>
                <li><strong>项目管理：</strong>工程项目的全生命周期管理</li>
                <li><strong>财务分析：</strong>合同收费、成本控制、经营统计</li>
                <li><strong>工作监控：</strong>员工考勤、工作记录、进度跟踪</li>
                <li><strong>数据分析：</strong>多维度数据分析和可视化展示</li>
            </ul>
        </div>

        <div class="nav-menu">
            <h3>🧭 功能导航</h3>
            <ul>
                <li><a href="#dashboard">📊 经营动态监控</a></li>
                <li><a href="#projects">🏗️ 项目数据汇总</a></li>
                <li><a href="#finance">💰 财务统计分析</a></li>
                <li><a href="#costs">📈 成本核算分析</a></li>
                <li><a href="#progress">⚡ 进度工作汇总</a></li>
                <li><a href="#attendance">👥 考勤监测系统</a></li>
                <li><a href="#records">📋 工作台账管理</a></li>
                <li><a href="#diagnosis">🔧 系统诊断工具</a></li>
            </ul>
        </div>

        <h2 id="dashboard">📊 经营动态监控 (jydt.php)</h2>

        <div class="feature-card dashboard">
            <h3>功能说明</h3>
            <p>实时监控公司经营状况，提供关键业务指标的动态展示。</p>
            
            <div class="screenshot">
                <p>📱 页面截图位置</p>
                <p>显示统计卡片、图表和数据表格</p>
            </div>
            
            <h4>主要功能</h4>
            <ul>
                <li>合同统计：显示合同总数、总金额、平均金额</li>
                <li>员工统计：在职员工数量、部门分布</li>
                <li>最新合同：最近签订的合同列表</li>
                <li>趋势分析：合同签订趋势图表</li>
            </ul>
            
            <div class="step-list">
                <h4>操作步骤</h4>
                <ol>
                    <li>访问经营动态页面</li>
                    <li>查看页面顶部的统计卡片</li>
                    <li>选择日期范围（可选）</li>
                    <li>点击"查询"按钮更新数据</li>
                    <li>查看图表和表格中的详细信息</li>
                </ol>
            </div>
            
            <div class="tip-box">
                <strong>💡 使用技巧：</strong>
                <ul>
                    <li>默认显示当月数据，可以修改日期范围查看历史数据</li>
                    <li>统计卡片会根据筛选条件实时更新</li>
                    <li>点击表格标题可以进行排序</li>
                </ul>
            </div>
        </div>

        <h2 id="projects">🏗️ 项目数据汇总 (gsxmsjhz.php)</h2>

        <div class="feature-card project">
            <h3>功能说明</h3>
            <p>全面展示公司所有项目的数据汇总，包括项目状态、合同情况、成本分析等。</p>
            
            <h4>数据展示</h4>
            <table class="table">
                <thead>
                    <tr>
                        <th>数据类型</th>
                        <th>显示内容</th>
                        <th>更新频率</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>项目统计</td>
                        <td>项目总数、在建项目、新开工项目</td>
                        <td>实时</td>
                    </tr>
                    <tr>
                        <td>合同统计</td>
                        <td>合同总额、平均合同金额</td>
                        <td>实时</td>
                    </tr>
                    <tr>
                        <td>成本统计</td>
                        <td>总成本、平均成本、成本控制率</td>
                        <td>实时</td>
                    </tr>
                    <tr>
                        <td>项目列表</td>
                        <td>项目详细信息表格</td>
                        <td>实时</td>
                    </tr>
                </tbody>
            </table>
            
            <div class="step-list">
                <h4>操作步骤</h4>
                <ol>
                    <li>进入项目数据汇总页面</li>
                    <li>设置查询条件（日期范围、项目状态等）</li>
                    <li>点击"查询"按钮</li>
                    <li>查看统计卡片中的汇总数据</li>
                    <li>分析项目状态分布图表</li>
                    <li>查看项目明细表格</li>
                    <li>可以点击具体项目查看详情</li>
                </ol>
            </div>
        </div>

        <h2 id="finance">💰 财务统计分析 (jytj.php)</h2>

        <div class="feature-card finance">
            <h3>功能说明</h3>
            <p>提供详细的财务数据分析，包括收入、支出、利润等关键财务指标。</p>
            
            <h4>分析维度</h4>
            <ul>
                <li><strong>时间维度：</strong>按年、季度、月份进行分析</li>
                <li><strong>项目维度：</strong>按项目类型、部门进行分析</li>
                <li><strong>收支维度：</strong>收入vs支出对比分析</li>
                <li><strong>趋势维度：</strong>历史趋势和预测分析</li>
            </ul>
            
            <div class="step-list">
                <h4>使用方法</h4>
                <ol>
                    <li>选择分析时间范围</li>
                    <li>选择分析维度（年度/季度/月度）</li>
                    <li>点击"生成报表"</li>
                    <li>查看财务指标卡片</li>
                    <li>分析收支趋势图表</li>
                    <li>查看详细财务明细表</li>
                </ol>
            </div>
            
            <div class="warning-box">
                <strong>⚠️ 注意事项：</strong>
                <ul>
                    <li>财务数据涉及敏感信息，请注意保密</li>
                    <li>大时间范围查询可能需要较长加载时间</li>
                    <li>建议定期导出数据进行备份</li>
                </ul>
            </div>
        </div>

        <h2 id="costs">📈 成本核算分析 (xmcbhs.php)</h2>

        <div class="feature-card project">
            <h3>功能说明</h3>
            <p>专门用于项目成本的详细核算和分析，帮助控制项目成本。</p>
            
            <h4>核算内容</h4>
            <ul>
                <li>人工成本：员工工资、社保费用</li>
                <li>材料成本：项目材料采购费用</li>
                <li>管理费用：办公、差旅等管理费用</li>
                <li>其他费用：设备租赁、外包费用等</li>
            </ul>
            
            <div class="step-list">
                <h4>操作流程</h4>
                <ol>
                    <li>选择要分析的项目</li>
                    <li>设置分析时间范围</li>
                    <li>选择成本分类（可选）</li>
                    <li>点击"开始分析"</li>
                    <li>查看成本构成饼图</li>
                    <li>分析成本趋势曲线</li>
                    <li>查看成本明细表格</li>
                    <li>导出分析报告（可选）</li>
                </ol>
            </div>
        </div>

        <h2 id="progress">⚡ 进度工作汇总 (ssjdgzhz.php)</h2>

        <div class="feature-card work">
            <h3>功能说明</h3>
            <p>实时监控各项工作的进度情况，提供工作完成情况的汇总分析。</p>
            
            <h4>监控模块</h4>
            <table class="table">
                <thead>
                    <tr>
                        <th>模块名称</th>
                        <th>监控内容</th>
                        <th>关键指标</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>工程项目</td>
                        <td>项目进度、里程碑</td>
                        <td>完成率、延期率</td>
                    </tr>
                    <tr>
                        <td>监理日志</td>
                        <td>日常监理工作</td>
                        <td>记录完整性</td>
                    </tr>
                    <tr>
                        <td>安全检查</td>
                        <td>安全检查记录</td>
                        <td>检查覆盖率</td>
                    </tr>
                    <tr>
                        <td>质量验收</td>
                        <td>质量验收情况</td>
                        <td>合格率</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <h2 id="attendance">👥 考勤监测系统 (dkzbcx.php)</h2>

        <div class="feature-card work">
            <h3>功能说明</h3>
            <p>监测员工考勤异常情况，识别可能的代打卡行为。</p>
            
            <h4>检测规则</h4>
            <ul>
                <li>同一设备短时间内多人打卡</li>
                <li>异常时间段的打卡记录</li>
                <li>地理位置异常的打卡</li>
                <li>打卡频率异常的员工</li>
            </ul>
            
            <div class="step-list">
                <h4>使用步骤</h4>
                <ol>
                    <li>选择检查日期范围</li>
                    <li>设置检测参数（可选）</li>
                    <li>点击"开始检测"</li>
                    <li>查看异常统计</li>
                    <li>查看异常明细列表</li>
                    <li>处理异常情况</li>
                </ol>
            </div>
        </div>

        <h2 id="records">📋 工作台账管理 (gztz.php)</h2>

        <div class="feature-card work">
            <h3>功能说明</h3>
            <p>管理各类工作记录，包括监理日志、安全检查、质量验收等。</p>
            
            <h4>记录类型</h4>
            <ul>
                <li>监理日志：日常监理工作记录</li>
                <li>安全日志：安全检查和整改记录</li>
                <li>现场巡查：现场巡视检查记录</li>
                <li>旁站监理：关键工序旁站记录</li>
                <li>专项检查：专项检查记录</li>
                <li>工程验收：分部分项验收记录</li>
                <li>见证检验：材料检验见证记录</li>
                <li>会议纪要：各类会议记录</li>
            </ul>
        </div>

        <h2 id="diagnosis">🔧 系统诊断工具 (diagnose.php)</h2>

        <div class="feature-card">
            <h3>功能说明</h3>
            <p>系统健康状态检查工具，帮助管理员诊断系统问题。</p>
            
            <h4>检查项目</h4>
            <ul>
                <li>数据库连接状态</li>
                <li>文件权限检查</li>
                <li>系统配置验证</li>
                <li>性能指标监控</li>
            </ul>
        </div>

        <h2>❓ 常见问题</h2>

        <div class="feature-card">
            <h3>FAQ</h3>
            
            <h4>Q: 页面加载很慢怎么办？</h4>
            <p>A: 可能是数据量较大，建议缩小查询时间范围，或联系管理员优化数据库。</p>
            
            <h4>Q: 数据显示不准确怎么办？</h4>
            <p>A: 请检查筛选条件是否正确，如果问题持续存在，请联系技术支持。</p>
            
            <h4>Q: 如何导出数据？</h4>
            <p>A: 大部分页面支持数据导出功能，查找页面上的"导出"按钮。</p>
            
            <h4>Q: 忘记密码怎么办？</h4>
            <p>A: 请联系系统管理员重置密码。</p>
        </div>

        <h2>📞 技术支持</h2>

        <div class="tip-box">
            <h3>联系方式</h3>
            <ul>
                <li><strong>技术支持邮箱：</strong><EMAIL></li>
                <li><strong>系统管理员：</strong><EMAIL></li>
                <li><strong>紧急联系电话：</strong>400-xxx-xxxx</li>
                <li><strong>工作时间：</strong>周一至周五 9:00-18:00</li>
            </ul>
        </div>

        <div style="text-align: center; margin-top: 40px; color: #7f8c8d;">
            <p>📅 手册版本：v1.0</p>
            <p>🔄 最后更新：2025年8月7日</p>
            <p>📖 更多帮助请访问系统帮助中心</p>
        </div>
    </div>
</body>
</html>
