<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API接口设计文档</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 10px;
            font-size: 2.5rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .subtitle {
            text-align: center;
            color: #7f8c8d;
            margin-bottom: 30px;
            font-size: 1.1rem;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }
        h3 {
            color: #2980b9;
            margin-top: 20px;
        }
        .api-endpoint {
            background: #f8f9fa;
            padding: 20px;
            margin: 15px 0;
            border-radius: 10px;
            border-left: 4px solid #3498db;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .get { border-left-color: #28a745; }
        .post { border-left-color: #007bff; }
        .put { border-left-color: #ffc107; }
        .delete { border-left-color: #dc3545; }
        .method {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
            font-size: 12px;
            margin-right: 10px;
        }
        .method-get { background: #28a745; color: white; }
        .method-post { background: #007bff; color: white; }
        .method-put { background: #ffc107; color: #212529; }
        .method-delete { background: #dc3545; color: white; }
        .code-snippet {
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 10px 0;
        }
        .json-example {
            background: #1e1e1e;
            color: #d4d4d4;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .table th, .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        .table th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            font-weight: 600;
        }
        .status-code {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: bold;
            font-size: 11px;
        }
        .status-200 { background: #d4edda; color: #155724; }
        .status-400 { background: #f8d7da; color: #721c24; }
        .status-401 { background: #fff3cd; color: #856404; }
        .status-500 { background: #f5c6cb; color: #721c24; }
        .auth-info {
            background: #e7f3ff;
            border: 1px solid #b8daff;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔌 API接口设计文档</h1>
        <p class="subtitle">基于现有PHP页面功能设计的RESTful API接口规范</p>
        
        <div class="auth-info">
            <h3>🔐 认证说明</h3>
            <p>所有API接口都需要在请求头中包含认证信息：</p>
            <div class="code-snippet">
Authorization: Bearer {access_token}
Content-Type: application/json
            </div>
        </div>

        <h2>📊 项目管理相关接口</h2>

        <div class="api-endpoint get">
            <h3><span class="method method-get">GET</span>/api/projects</h3>
            <p><strong>功能：</strong>获取项目列表</p>
            <p><strong>对应页面：</strong>gsxmsjhz.php, xmcbhs.php</p>
            
            <h4>请求参数</h4>
            <table class="table">
                <thead>
                    <tr><th>参数名</th><th>类型</th><th>必填</th><th>说明</th></tr>
                </thead>
                <tbody>
                    <tr><td>status</td><td>string</td><td>否</td><td>项目状态筛选</td></tr>
                    <tr><td>start_date</td><td>string</td><td>否</td><td>开始日期 (YYYY-MM-DD)</td></tr>
                    <tr><td>end_date</td><td>string</td><td>否</td><td>结束日期 (YYYY-MM-DD)</td></tr>
                    <tr><td>page</td><td>integer</td><td>否</td><td>页码，默认1</td></tr>
                    <tr><td>limit</td><td>integer</td><td>否</td><td>每页数量，默认20</td></tr>
                </tbody>
            </table>

            <h4>响应示例</h4>
            <div class="code-snippet json-example">
{
    "success": true,
    "data": {
        "projects": [
            {
                "id": 1,
                "gcname": "某市政道路工程",
                "xmzt": "在建项目",
                "zaojia": 5000000.00,
                "qdsj": "2024-01-15",
                "bumen": "市政工程部",
                "contract_amount": 4800000.00,
                "received_amount": 2400000.00,
                "cost_amount": 1800000.00
            }
        ],
        "pagination": {
            "current_page": 1,
            "total_pages": 5,
            "total_count": 98,
            "per_page": 20
        }
    },
    "timestamp": 1691404800
}
            </div>
        </div>

        <div class="api-endpoint get">
            <h3><span class="method method-get">GET</span>/api/projects/{id}</h3>
            <p><strong>功能：</strong>获取项目详细信息</p>
            <p><strong>对应页面：</strong>xmcbhs.php, xmhtdzmx.php</p>
            
            <h4>响应示例</h4>
            <div class="code-snippet json-example">
{
    "success": true,
    "data": {
        "project": {
            "id": 1,
            "gcname": "某市政道路工程",
            "xmzt": "在建项目",
            "zaojia": 5000000.00,
            "contracts": [
                {
                    "id": 1,
                    "htmc": "监理服务合同",
                    "fwf": 480000.00,
                    "qdsj": "2024-01-15"
                }
            ],
            "costs": [
                {
                    "month": "2024-01",
                    "wccz": 150000.00,
                    "sbrq": "2024-01-31"
                }
            ],
            "payments": [
                {
                    "yjje": 120000.00,
                    "ysje": 120000.00,
                    "sksj": "2024-02-15"
                }
            ]
        }
    }
}
            </div>
        </div>

        <h2>💰 财务管理相关接口</h2>

        <div class="api-endpoint get">
            <h3><span class="method method-get">GET</span>/api/finance/statistics</h3>
            <p><strong>功能：</strong>获取财务统计数据</p>
            <p><strong>对应页面：</strong>jytj.php, dzetj.php</p>
            
            <h4>请求参数</h4>
            <table class="table">
                <thead>
                    <tr><th>参数名</th><th>类型</th><th>必填</th><th>说明</th></tr>
                </thead>
                <tbody>
                    <tr><td>start_date</td><td>string</td><td>是</td><td>开始日期</td></tr>
                    <tr><td>end_date</td><td>string</td><td>是</td><td>结束日期</td></tr>
                    <tr><td>type</td><td>string</td><td>否</td><td>统计类型：contract|payment|cost</td></tr>
                </tbody>
            </table>

            <h4>响应示例</h4>
            <div class="code-snippet json-example">
{
    "success": true,
    "data": {
        "summary": {
            "total_contract_amount": 12000000.00,
            "total_received_amount": 7200000.00,
            "total_cost_amount": 5400000.00,
            "collection_rate": 60.0,
            "profit_margin": 25.0
        },
        "monthly_trend": [
            {
                "month": "2024-01",
                "contract_amount": 2000000.00,
                "received_amount": 1200000.00,
                "cost_amount": 900000.00
            }
        ]
    }
}
            </div>
        </div>

        <div class="api-endpoint get">
            <h3><span class="method method-get">GET</span>/api/finance/payments</h3>
            <p><strong>功能：</strong>获取收款明细</p>
            <p><strong>对应页面：</strong>dzetj.php, xmhtdzmx.php</p>
            
            <h4>响应示例</h4>
            <div class="code-snippet json-example">
{
    "success": true,
    "data": {
        "payments": [
            {
                "id": 1,
                "project_id": 1,
                "project_name": "某市政道路工程",
                "yjje": 120000.00,
                "ysje": 120000.00,
                "yjsj": "2024-02-15",
                "sksj": "2024-02-15",
                "status": "已收款"
            }
        ],
        "statistics": {
            "total_expected": 2400000.00,
            "total_received": 1800000.00,
            "pending_amount": 600000.00
        }
    }
}
            </div>
        </div>

        <h2>👥 人员管理相关接口</h2>

        <div class="api-endpoint get">
            <h3><span class="method method-get">GET</span>/api/employees</h3>
            <p><strong>功能：</strong>获取员工列表</p>
            <p><strong>对应页面：</strong>jydt.php, gztz.php</p>
            
            <h4>响应示例</h4>
            <div class="code-snippet json-example">
{
    "success": true,
    "data": {
        "employees": [
            {
                "id": 1,
                "name": "张三",
                "department": "市政工程部",
                "position": "项目经理",
                "status": "在职",
                "workdate": "2023-01-15",
                "current_projects": ["某市政道路工程"]
            }
        ],
        "statistics": {
            "total_count": 68,
            "new_employees_this_month": 3,
            "departments": {
                "市政工程部": 25,
                "建筑工程部": 30,
                "装修工程部": 13
            }
        }
    }
}
            </div>
        </div>

        <div class="api-endpoint get">
            <h3><span class="method method-get">GET</span>/api/attendance/check</h3>
            <p><strong>功能：</strong>考勤异常检查</p>
            <p><strong>对应页面：</strong>dkzbcx.php</p>
            
            <h4>响应示例</h4>
            <div class="code-snippet json-example">
{
    "success": true,
    "data": {
        "anomalies": [
            {
                "employee_name": "李四",
                "check_date": "2024-08-07",
                "device_info": "设备A",
                "check_time": "08:30:00",
                "anomaly_type": "设备异常",
                "risk_level": "中"
            }
        ],
        "summary": {
            "total_checks": 156,
            "anomaly_count": 8,
            "anomaly_rate": 5.13
        }
    }
}
            </div>
        </div>

        <h2>📋 工作管理相关接口</h2>

        <div class="api-endpoint get">
            <h3><span class="method method-get">GET</span>/api/work/progress</h3>
            <p><strong>功能：</strong>获取工作进度汇总</p>
            <p><strong>对应页面：</strong>ssjdgzhz.php</p>
            
            <h4>响应示例</h4>
            <div class="code-snippet json-example">
{
    "success": true,
    "data": {
        "modules": [
            {
                "name": "工程项目",
                "total": 45,
                "completed": 28,
                "pending": 15,
                "overdue": 2,
                "completion_rate": 62.22
            },
            {
                "name": "监理日志",
                "total": 156,
                "completed": 142,
                "pending": 12,
                "overdue": 2,
                "completion_rate": 91.03
            }
        ],
        "trend": [
            {
                "date": "2024-08-01",
                "planned_work": 25,
                "actual_work": 23
            }
        ]
    }
}
            </div>
        </div>

        <div class="api-endpoint get">
            <h3><span class="method method-get">GET</span>/api/work/records</h3>
            <p><strong>功能：</strong>获取工作记录</p>
            <p><strong>对应页面：</strong>gztz.php</p>
            
            <h4>请求参数</h4>
            <table class="table">
                <thead>
                    <tr><th>参数名</th><th>类型</th><th>必填</th><th>说明</th></tr>
                </thead>
                <tbody>
                    <tr><td>date</td><td>string</td><td>是</td><td>查询日期 (YYYY-MM-DD)</td></tr>
                    <tr><td>type</td><td>string</td><td>否</td><td>工作类型筛选</td></tr>
                </tbody>
            </table>
        </div>

        <h2>📈 数据分析相关接口</h2>

        <div class="api-endpoint get">
            <h3><span class="method method-get">GET</span>/api/analytics/dashboard</h3>
            <p><strong>功能：</strong>获取仪表盘数据</p>
            <p><strong>对应页面：</strong>jydt.php, 各种汇总页面</p>
            
            <h4>响应示例</h4>
            <div class="code-snippet json-example">
{
    "success": true,
    "data": {
        "kpi": {
            "total_projects": 45,
            "active_projects": 32,
            "total_employees": 68,
            "monthly_revenue": 2400000.00
        },
        "charts": {
            "project_status_distribution": [
                {"status": "在建项目", "count": 32},
                {"status": "新开工项目", "count": 8},
                {"status": "完工未结算", "count": 5}
            ],
            "monthly_trend": [
                {"month": "2024-01", "revenue": 2000000, "cost": 1500000},
                {"month": "2024-02", "revenue": 2200000, "cost": 1650000}
            ]
        }
    }
}
            </div>
        </div>

        <h2>🔧 通用接口规范</h2>

        <h3>状态码说明</h3>
        <table class="table">
            <thead>
                <tr><th>状态码</th><th>说明</th><th>示例场景</th></tr>
            </thead>
            <tbody>
                <tr><td><span class="status-code status-200">200</span></td><td>请求成功</td><td>正常返回数据</td></tr>
                <tr><td><span class="status-code status-400">400</span></td><td>请求参数错误</td><td>日期格式不正确</td></tr>
                <tr><td><span class="status-code status-401">401</span></td><td>未授权</td><td>token无效或过期</td></tr>
                <tr><td><span class="status-code status-500">500</span></td><td>服务器错误</td><td>数据库连接失败</td></tr>
            </tbody>
        </table>

        <h3>错误响应格式</h3>
        <div class="code-snippet json-example">
{
    "success": false,
    "error": {
        "code": "INVALID_DATE_FORMAT",
        "message": "日期格式不正确，请使用 YYYY-MM-DD 格式",
        "details": {
            "field": "start_date",
            "received": "2024/01/01",
            "expected": "2024-01-01"
        }
    },
    "timestamp": 1691404800
}
        </div>

        <h3>分页响应格式</h3>
        <div class="code-snippet json-example">
{
    "success": true,
    "data": {
        "items": [...],
        "pagination": {
            "current_page": 1,
            "total_pages": 10,
            "total_count": 200,
            "per_page": 20,
            "has_next": true,
            "has_prev": false
        }
    }
}
        </div>

        <div style="text-align: center; margin-top: 40px; color: #7f8c8d;">
            <p>📅 文档版本：v1.0</p>
            <p>🔄 最后更新：2025年8月7日</p>
            <p>📧 如有疑问，请联系开发团队</p>
        </div>
    </div>
</body>
</html>
