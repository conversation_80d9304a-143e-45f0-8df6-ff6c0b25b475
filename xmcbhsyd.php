<?php
// 开启错误报告以便调试
error_reporting(E_ALL);
ini_set('display_errors', 1);

include '../config.php';

// 检查数据库连接
if (!$link) {
    die("数据库连接失败: " . mysqli_connect_error());
}

// 定义必要的函数，避免页面空白
if (!function_exists('getconfig')) {
    function getconfig($key, $default = '') {
        $configs = array('apptheme' => '#1389D3');
        return isset($configs[$key]) ? $configs[$key] : $default;
    }
}

if (!function_exists('c')) {
    function c($name) {
        if ($name == 'image') {
            return new SimpleImageHelper();
        }
        return null;
    }
}

class SimpleImageHelper {
    public function colorTorgb($color) {
        if (!empty($color) && (strlen($color) == 7)) {
            $r = hexdec(substr($color, 1, 2));
            $g = hexdec(substr($color, 3, 2));
            $b = hexdec(substr($color, 5));
        } else {
            $r = $g = $b = 0;
        }
        return array($r, $g, $b);
    }
}

$gcid = isset($_POST['gcid']) ? $_POST['gcid'] : '';
$selectedMonth = isset($_POST['month-select']) ? $_POST['month-select'] : date('Y-m');

// 根据选择的月份计算开始和结束日期
$startDate = $selectedMonth . '-01';
$endDate = date('Y-m-t', strtotime($selectedMonth . '-01'));

// 初始化月度数据
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // 格式化日期用于显示
    $displayStart = date('Y年m月d日', strtotime($startDate));
    $displayEnd = date('Y年m月d日', strtotime($endDate));
    $daysDiff = (strtotime($endDate) - strtotime($startDate)) / (60 * 60 * 24) + 1;

    // 添加本月信息
    $currentMonth = date('Y年m月', strtotime($selectedMonth . '-01'));
    $monthDays = date('t', strtotime($selectedMonth . '-01'));
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目成本核算（月度） - 公司数据总览系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/boxicons@2.0.7/css/boxicons.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="styles/main.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        /* 页面特定样式 - 与xmcbhs.php风格一致 */
        .chart-container {
            height: 300px;
            position: relative;
            margin: 1rem 0;
        }

        /* 成本趋势指示器 */
        .cost-trend-up {
            color: #dc3545;
        }

        .cost-trend-down {
            color: #28a745;
        }

        .cost-trend-stable {
            color: #007bff;
        }

        /* 图表容器样式 */
        .monthly-cost-chart,
        .cost-distribution-chart,
        .cost-trend-chart,
        .cost-control-chart,
        .cost-ratio-chart,
        .cost-summary-chart {
            height: 250px;
            position: relative;
        }

        /* 优化的筛选器样式 - 同行布局 */
        .filter-row-container {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border: 2px solid #dee2e6;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 25px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
            position: relative;
            overflow: hidden;
        }

        .filter-row-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(135deg, #667eea, #764ba2);
        }

        .filter-inline-group {
            display: flex;
            align-items: center;
            gap: 20px;
            flex-wrap: wrap;
        }

        .filter-item-inline {
            display: flex;
            align-items: center;
            gap: 10px;
            white-space: nowrap;
        }

        .filter-item-inline label {
            color: #495057;
            font-weight: 600;
            font-size: 14px;
            margin: 0;
            white-space: nowrap;
            min-width: fit-content;
        }

        .filter-item-inline .form-select,
        .filter-item-inline .form-control {
            padding: 8px 12px;
            border: 2px solid #ced4da;
            border-radius: 8px;
            font-family: inherit;
            transition: all 0.3s ease;
            background: white;
            color: #495057;
            font-weight: 500;
            min-width: 140px;
            font-size: 14px;
        }

        .filter-item-inline .form-select:focus,
        .filter-item-inline .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            outline: none;
        }

        .filter-item-inline .form-select:hover,
        .filter-item-inline .form-control:hover {
            border-color: #667eea;
        }

        .filter-actions {
            display: flex;
            gap: 12px;
            align-items: center;
            margin-left: auto;
        }

        .filter-actions .btn {
            padding: 8px 16px;
            font-weight: 600;
            border-radius: 8px;
            transition: all 0.3s ease;
            white-space: nowrap;
            font-size: 14px;
        }

        .filter-actions .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .filter-actions .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
        }

        .filter-actions .btn-primary:hover {
            background: linear-gradient(135deg, #764ba2, #667eea);
        }

        /* 导航标签样式 */
        .nav-tabs .nav-link {
            color: #495057;
            border: none;
            border-radius: 8px 8px 0 0;
            transition: all 0.3s ease;
        }

        .nav-tabs .nav-link.active {
            font-weight: 600;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .nav-tabs .nav-link:hover {
            border-color: transparent;
            background-color: rgba(102, 126, 234, 0.1);
        }

        .tab-content {
            padding: 1rem 0;
        }

        /* 员工卡片样式 */
        .employee-card {
            border: 1px solid #e0e0e0;
            border-radius: 12px;
            padding: 1rem;
            background-color: #fff;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }

        .employee-card:hover {
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }

        .employee-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #667eea;
        }

        .detail-row {
            margin-bottom: 0.5rem;
        }

        .detail-label {
            font-weight: 600;
            color: #6c757d;
        }

        /* 表格样式 */
        .table th {
            background-color: #f8f9fa;
            font-size: 0.875rem;
            padding: 0.75rem 0.5rem;
            white-space: nowrap;
        }

        .table td {
            padding: 0.75rem 0.5rem;
            font-size: 0.875rem;
            vertical-align: middle;
        }

        .table-hover tbody tr:hover {
            background-color: rgba(0, 123, 255, 0.05);
        }

        /* 趋势分析项样式 */
        .trend-analysis-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            height: 100%;
            border-left: 4px solid #667eea;
            transition: all 0.3s ease;
        }

        .trend-analysis-item:hover {
            background: #e9ecef;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .trend-analysis-item h6 {
            margin-bottom: 10px;
            font-weight: 600;
        }

        .trend-analysis-item p {
            font-size: 14px;
            line-height: 1.4;
        }

        /* 响应式调整 */
        @media (max-width: 992px) {
            .filter-inline-group {
                flex-direction: column;
                align-items: stretch;
                gap: 20px;
            }

            .filter-item-inline {
                justify-content: space-between;
                width: 100%;
            }

            .filter-item-inline .form-select,
            .filter-item-inline .form-control {
                min-width: 180px;
                flex: 1;
                max-width: 250px;
            }

            .filter-actions {
                justify-content: center;
                margin-left: 0;
                width: 100%;
            }

            .filter-actions .btn {
                flex: 1;
                max-width: 150px;
            }
        }

        @media (max-width: 576px) {
            .filter-row-container {
                padding: 15px;
            }

            .filter-item-inline {
                flex-direction: column;
                align-items: stretch;
                gap: 8px;
            }

            .filter-item-inline label {
                text-align: center;
            }

            .filter-item-inline .form-select,
            .filter-item-inline .form-control {
                min-width: auto;
                width: 100%;
                max-width: none;
            }

            .filter-actions {
                flex-direction: column;
                gap: 10px;
            }

            .filter-actions .btn {
                max-width: none;
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bx bx-calculator me-2"></i>
                项目成本核算（月度）
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text text-white">
                    <i class="bx bx-time me-1"></i>
                    最后更新: <span id="last-update-time"><?php echo date('Y-m-d H:i:s'); ?></span>
                </span>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- 优化的筛选器容器 - 同行布局 -->
        <form method="post" action="">
            <div class="filter-row-container">
                <div class="filter-inline-group">
                    <div class="filter-item-inline">
                        <label for="project-select">
                            <i class="fas fa-project-diagram me-2"></i>
                            项目:
                        </label>
                        <select id="project-select" class="form-select" name="gcid">
                            <option value="">全部项目</option>
                            <?php
                            $sql="SELECT * FROM `tuqoa_gcproject` WHERE `xmzt` not in ('完工项目','完工已结算','合同终止') order by id desc";
                            $result = mysqli_query($link, $sql);
                            if ($result) {
                                while ($row = mysqli_fetch_assoc($result)) {
                                    $selected = ($gcid == $row["id"]) ? 'selected' : '';
                                    echo '<option value="'.$row["id"].'" '.$selected.'>'.$row["gcname"].'</option>';
                                }
                            }
                            ?>
                        </select>
                    </div>

                    <div class="filter-item-inline">
                        <label for="month-select">
                            <i class="fas fa-calendar-alt me-2"></i>
                            月份:
                        </label>
                        <select name="month-select" id="month-select" class="form-select">
                            <?php
                            // 生成最近12个月的选项
                            for ($i = 0; $i < 12; $i++) {
                                $month = date('Y-m', strtotime("-$i months"));
                                $monthLabel = date('Y年n月', strtotime("-$i months"));
                                $selected = ($selectedMonth == $month) ? 'selected' : '';
                                echo '<option value="'.$month.'" '.$selected.'>'.$monthLabel.'</option>';
                            }
                            ?>
                        </select>
                    </div>

                    <div class="filter-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>
                            查询数据
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="resetFilters()">
                            <i class="fas fa-undo me-2"></i>
                            重置
                        </button>
                    </div>
                </div>
            </div>
        </form>
            <!-- 月度统计卡片 -->
            <div class="row">
                <?php
                // 添加项目和月份筛选条件
                $project_filter = '';
                if (!empty($gcid)) {
                    $project_filter = " AND projectid = '$gcid'";
                }

                $month_filter = '';
                if (!empty($selectedMonth)) {
                    $month_filter = " AND DATE_FORMAT(sbrq, '%Y-%m') = '$selectedMonth'";
                }

                // 查询当月实际成本
                $sql="SELECT COALESCE(SUM(wccz), 0) as monthly_cost FROM `tuqoa_xmcztjb` WHERE 1=1 $project_filter $month_filter";
                $result = mysqli_query($link, $sql);
                $monthly_cost = 0;
                if ($result) {
                    $row = mysqli_fetch_assoc($result);
                    $monthly_cost = round($row["monthly_cost"], 2);
                }
                ?>
                <div class="col-md-3">
                    <div class="card stat-card stat-card-warning">
                        <div class="card-body">
                            <i class="fas fa-calculator stat-icon"></i>
                            <h5 class="card-title">当月实际成本</h5>
                            <h2 class="card-text">¥<?php echo number_format($monthly_cost, 2); ?>万</h2>
                            <p class="stat-info"><?php echo date('Y年n月', strtotime($selectedMonth)); ?></p>
                        </div>
                    </div>
                </div>

                <?php
                // 查询当月收入
                $income_filter = '';
                if (!empty($gcid)) {
                    $income_filter = " AND projectid = '$gcid'";
                }

                $month_income_filter = '';
                if (!empty($selectedMonth)) {
                    $month_income_filter = " AND DATE_FORMAT(qdsj, '%Y-%m') = '$selectedMonth'";
                }

                $sql="SELECT COALESCE(SUM(fwf), 0) as monthly_income FROM `tuqoa_htgl` WHERE 1=1 $income_filter $month_income_filter";
                $result = mysqli_query($link, $sql);
                $monthly_income = 0;
                if ($result) {
                    $row = mysqli_fetch_assoc($result);
                    $monthly_income = round($row["monthly_income"], 2);
                }
                ?>
                <div class="col-md-3">
                    <div class="card stat-card stat-card-success">
                        <div class="card-body">
                            <i class="fas fa-money-bill-wave stat-icon"></i>
                            <h5 class="card-title">当月收入</h5>
                            <h2 class="card-text">¥<?php echo number_format($monthly_income, 2); ?>万</h2>
                            <p class="stat-info"><?php echo date('Y年n月', strtotime($selectedMonth)); ?></p>
                        </div>
                    </div>
                </div>
                <?php
                // 计算当月盈亏
                $monthly_profit = $monthly_income - $monthly_cost;
                $profit_class = $monthly_profit >= 0 ? 'text-success' : 'text-danger';
                $profit_text = $monthly_profit >= 0 ? '盈利' : '亏损';
                ?>
                <div class="col-md-3">
                    <div class="card stat-card <?php echo $monthly_profit >= 0 ? 'stat-card-success' : 'stat-card-danger'; ?>">
                        <div class="card-body">
                            <i class="fas fa-chart-line stat-icon"></i>
                            <h5 class="card-title">当月盈亏</h5>
                            <h2 class="card-text">¥<?php echo number_format(abs($monthly_profit), 2); ?>万</h2>
                            <p class="stat-info"><?php echo $profit_text; ?></p>
                        </div>
                    </div>
                </div>

                <?php
                // 计算当月成本控制率
                $monthly_control_rate = ($monthly_income > 0) ? round(($monthly_cost / $monthly_income) * 100, 1) : 0;
                $control_class = $monthly_control_rate <= 80 ? 'text-success' : ($monthly_control_rate <= 90 ? 'text-warning' : 'text-danger');
                ?>
                <div class="col-md-3">
                    <div class="card stat-card <?php echo $monthly_control_rate <= 80 ? 'stat-card-success' : ($monthly_control_rate <= 90 ? 'stat-card-warning' : 'stat-card-danger'); ?>">
                        <div class="card-body">
                            <i class="fas fa-tachometer-alt stat-icon"></i>
                            <h5 class="card-title">当月成本控制率</h5>
                            <h2 class="card-text"><?php echo $monthly_control_rate; ?>%</h2>
                            <p class="stat-info"><?php echo $monthly_control_rate <= 80 ? '控制良好' : ($monthly_control_rate <= 90 ? '需要关注' : '超支预警'); ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <?php
                $monthlyCostLabels = [];
                $monthlyCostData = [];

                // 构建安全的SQL查询
                $where_conditions = [];
                if (!empty($gcid)) {
                    $where_conditions[] = "projectid = '" . mysqli_real_escape_string($link, $gcid) . "'";
                }
                $where_conditions[] = "sbrq BETWEEN '" . mysqli_real_escape_string($link, $startDate) . "' AND '" . mysqli_real_escape_string($link, $endDate) . "'";

                $sql = "SELECT DATE_FORMAT(sbrq, '%Y-%m') as month, SUM(wccz) as total
                        FROM tuqoa_xmcztjb
                        WHERE " . implode(' AND ', $where_conditions) . "
                        GROUP BY DATE_FORMAT(sbrq, '%Y-%m')
                        ORDER BY month";

                $result = mysqli_query($link, $sql);
                if ($result) {
                    while ($row = mysqli_fetch_assoc($result)) {
                        $monthlyCostLabels[] = date('m月', strtotime($row['month']));
                        $monthlyCostData[] = (float)$row['total'];
                    }
                } else {
                    // 查询失败时的错误处理
                    echo "<!-- SQL错误: " . mysqli_error($link) . " -->";
                }

                $monthlyCostLabelsJSON = json_encode($monthlyCostLabels);
                $monthlyCostDataJSON = json_encode($monthlyCostData);
                
                ?>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">月度成本趋势</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="monthlyCostChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">月度收入vs成本对比</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="monthlyIncomeVsCostChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 月度成本明细表 -->
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">月度成本明细表</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>项目名称</th>
                                            <th>上报日期</th>
                                            <th>完成产值(万)</th>
                                            <th>完成率(%)</th>
                                            <th>本年累计(万)</th>
                                            <th>状态</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        // 添加月度筛选条件
                                        $month_detail_filter = '';
                                        if (!empty($selectedMonth)) {
                                            $month_detail_filter = " AND DATE_FORMAT(sbrq, '%Y-%m') = '$selectedMonth'";
                                        }

                                        $project_detail_filter = '';
                                        if (!empty($gcid)) {
                                            $project_detail_filter = " AND projectid = '$gcid'";
                                        }

                                        $sql="SELECT * FROM `tuqoa_xmcztjb` WHERE 1=1 $project_detail_filter $month_detail_filter ORDER BY sbrq DESC LIMIT 10";
                                        $result = mysqli_query($link, $sql);
                                        if ($result) {
                                            while ($row = mysqli_fetch_assoc($result)) {
                                                $status = "正常";
                                                $statusClass = "status-normal";
                                                if ($row["wcl"] < 50) {
                                                    $status = "延期";
                                                    $statusClass = "status-danger";
                                                } elseif ($row["wcl"] < 80) {
                                                    $status = "预警";
                                                    $statusClass = "status-warning";
                                                }
                                        ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($row["project"]); ?></td>
                                            <td><?php echo $row["sbrq"]; ?></td>
                                            <td><?php echo number_format($row["wccz"], 2); ?></td>
                                            <td><?php echo $row["wcl"]; ?>%</td>
                                            <td><?php echo number_format($row["bnljcz"], 2); ?></td>
                                            <td><span class="status-badge <?php echo $statusClass; ?>"><?php echo $status; ?></span></td>
                                        </tr>
                                        <?php
                                            }
                                        } else {
                                            echo "<tr><td colspan='6'>暂无数据</td></tr>";
                                        }
                                        ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 月度分析图表 -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">月度成本控制率趋势</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="monthlyControlRateChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">月度项目进度分布</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="monthlyProgressChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">成本趋势分析</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="costTrendChart"></canvas>
                            </div>

                            <!-- 成本趋势分析说明 -->
                            <div class="mt-4">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="trend-analysis-item">
                                            <h6 class="text-primary">
                                                <i class="fas fa-chart-line me-2"></i>
                                                趋势分析
                                            </h6>
                                            <p class="mb-0">
                                                <?php
                                                if (count($monthlyCostData) >= 2) {
                                                    $firstCost = $monthlyCostData[0];
                                                    $lastCost = end($monthlyCostData);

                                                    if ($firstCost > 0) {
                                                        $trendPercent = (($lastCost - $firstCost) / $firstCost) * 100;

                                                        if ($trendPercent > 5) {
                                                            echo '<span class="cost-trend-up"><i class="fas fa-arrow-up me-1"></i>成本呈上升趋势 (+' . number_format($trendPercent, 1) . '%)</span>';
                                                        } elseif ($trendPercent < -5) {
                                                            echo '<span class="cost-trend-down"><i class="fas fa-arrow-down me-1"></i>成本呈下降趋势 (' . number_format($trendPercent, 1) . '%)</span>';
                                                        } else {
                                                            echo '<span class="cost-trend-stable"><i class="fas fa-minus me-1"></i>成本相对稳定 (' . number_format($trendPercent, 1) . '%)</span>';
                                                        }
                                                    } else {
                                                        echo '<span class="text-muted">基准数据为零，无法计算趋势</span>';
                                                    }
                                                } else {
                                                    echo '<span class="text-muted">数据不足，无法分析趋势</span>';
                                                }
                                                ?>
                                            </p>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="trend-analysis-item">
                                            <h6 class="text-success">
                                                <i class="fas fa-calculator me-2"></i>
                                                平均成本
                                            </h6>
                                            <p class="mb-0">
                                                <?php
                                                if (count($monthlyCostData) > 0) {
                                                    echo '¥' . number_format(array_sum($monthlyCostData) / count($monthlyCostData), 2) . '万';
                                                } else {
                                                    echo '<span class="text-muted">暂无数据</span>';
                                                }
                                                ?>
                                                <small class="text-muted d-block">月度平均成本</small>
                                            </p>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="trend-analysis-item">
                                            <h6 class="text-warning">
                                                <i class="fas fa-exclamation-triangle me-2"></i>
                                                成本波动
                                            </h6>
                                            <p class="mb-0">
                                                <?php
                                                if (count($monthlyCostData) > 0) {
                                                    $maxCost = max($monthlyCostData);
                                                    $minCost = min($monthlyCostData);

                                                    if ($minCost > 0) {
                                                        $volatility = (($maxCost - $minCost) / $minCost) * 100;
                                                        echo number_format($volatility, 1) . '%';
                                                    } else {
                                                        echo '无法计算';
                                                    }
                                                    echo '<small class="text-muted d-block">最大波动幅度</small>';
                                                } else {
                                                    echo '<span class="text-muted">无数据</span>';
                                                }
                                                ?>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <?php
            // 生成月度分析图表数据
            $monthlyLabels = [];
            $monthlyCostData = [];
            $monthlyIncomeData = [];
            $monthlyControlRateData = [];

            // 生成最近12个月的数据
            for ($i = 11; $i >= 0; $i--) {
                $month = date('Y-m', strtotime("-$i months"));
                $monthLabel = date('n', strtotime("-$i months")) . '月';
                $monthlyLabels[] = $monthLabel;

                // 查询该月成本
                $cost_filter = '';
                if (!empty($gcid)) {
                    $cost_filter = " AND projectid = '$gcid'";
                }
                $sql_cost = "SELECT COALESCE(SUM(wccz), 0) as monthly_cost FROM tuqoa_xmcztjb WHERE DATE_FORMAT(sbrq, '%Y-%m') = '$month' $cost_filter";
                $result_cost = mysqli_query($link, $sql_cost);
                $monthly_cost = 0;
                if ($result_cost) {
                    $row_cost = mysqli_fetch_assoc($result_cost);
                    $monthly_cost = (float)$row_cost['monthly_cost'];
                }
                $monthlyCostData[] = $monthly_cost;

                // 查询该月收入
                $income_filter = '';
                if (!empty($gcid)) {
                    $income_filter = " AND projectid = '$gcid'";
                }
                $sql_income = "SELECT COALESCE(SUM(fwf), 0) as monthly_income FROM tuqoa_htgl WHERE DATE_FORMAT(qdsj, '%Y-%m') = '$month' $income_filter";
                $result_income = mysqli_query($link, $sql_income);
                $monthly_income = 0;
                if ($result_income) {
                    $row_income = mysqli_fetch_assoc($result_income);
                    $monthly_income = (float)$row_income['monthly_income'];
                }
                $monthlyIncomeData[] = $monthly_income;

                // 计算成本控制率
                $control_rate = ($monthly_income > 0) ? round(($monthly_cost / $monthly_income) * 100, 1) : 0;
                $monthlyControlRateData[] = $control_rate;
            }

            // 项目进度分布数据
            $progressLabels = [];
            $progressData = [];
            $progress_filter = '';
            if (!empty($gcid)) {
                $progress_filter = " AND projectid = '$gcid'";
            }
            $month_progress_filter = '';
            if (!empty($selectedMonth)) {
                $month_progress_filter = " AND DATE_FORMAT(sbrq, '%Y-%m') = '$selectedMonth'";
            }

            // 按进度范围分组
            $sql_progress = "SELECT
                CASE
                    WHEN gcjdjd < 25 THEN '0-25%'
                    WHEN gcjdjd < 50 THEN '25-50%'
                    WHEN gcjdjd < 75 THEN '50-75%'
                    WHEN gcjdjd < 100 THEN '75-100%'
                    ELSE '已完成'
                END as progress_range,
                COUNT(*) as count
                FROM tuqoa_xmcztjb
                WHERE 1=1 $progress_filter $month_progress_filter
                GROUP BY progress_range";
            $result_progress = mysqli_query($link, $sql_progress);
            if ($result_progress) {
                while ($row_progress = mysqli_fetch_assoc($result_progress)) {
                    $progressLabels[] = $row_progress['progress_range'];
                    $progressData[] = (int)$row_progress['count'];
                }
            }

            // 如果没有进度数据，使用默认数据
            if (empty($progressData)) {
                $progressLabels = ['0-25%', '25-50%', '50-75%', '75-100%', '已完成'];
                $progressData = [2, 5, 8, 12, 3];
            }
            ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 调试信息
            console.log('月度标签:', <?php echo json_encode($monthlyLabels); ?>);
            console.log('成本数据:', <?php echo json_encode($monthlyCostData); ?>);
            console.log('收入数据:', <?php echo json_encode($monthlyIncomeData); ?>);
            console.log('控制率数据:', <?php echo json_encode($monthlyControlRateData); ?>);
            console.log('进度标签:', <?php echo json_encode($progressLabels); ?>);
            console.log('进度数据:', <?php echo json_encode($progressData); ?>);

            // 检查Chart.js是否加载
            if (typeof Chart === 'undefined') {
                console.error('Chart.js未加载！');
                return;
            }

            // 初始化所有图表
            try {
                initCharts();
                console.log('图表初始化成功');
            } catch (error) {
                console.error('图表初始化失败:', error);
            }
        });

        // 初始化图表函数
        function initCharts() {
            // 月度成本趋势图表
            const monthlyCostElement = document.getElementById('monthlyCostChart');
            if (!monthlyCostElement) {
                console.error('找不到monthlyCostChart元素');
                return;
            }
            const monthlyCostCtx = monthlyCostElement.getContext('2d');
            new Chart(monthlyCostCtx, {
                type: 'line',
                data: {
                    labels: <?php echo json_encode($monthlyLabels); ?>,
                    datasets: [
                        {
                            label: '月度成本',
                            data: <?php echo json_encode($monthlyCostData); ?>,
                            borderColor: '#f44336',
                            backgroundColor: 'rgba(244, 67, 54, 0.1)',
                            fill: true,
                            tension: 0.4,
                            pointBackgroundColor: '#f44336',
                            pointBorderColor: '#fff',
                            pointBorderWidth: 2,
                            pointRadius: 5
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '成本 (万元)'
                            },
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            }
                        },
                        x: {
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top'
                        }
                    }
                }
            });

            // 月度收入vs成本对比图表
            const monthlyIncomeVsCostElement = document.getElementById('monthlyIncomeVsCostChart');
            if (!monthlyIncomeVsCostElement) {
                console.error('找不到monthlyIncomeVsCostChart元素');
                return;
            }
            const monthlyIncomeVsCostCtx = monthlyIncomeVsCostElement.getContext('2d');
            new Chart(monthlyIncomeVsCostCtx, {
                type: 'bar',
                data: {
                    labels: <?php echo json_encode($monthlyLabels); ?>,
                    datasets: [
                        {
                            label: '月度收入',
                            data: <?php echo json_encode($monthlyIncomeData); ?>,
                            backgroundColor: '#4caf50',
                            borderColor: '#388e3c',
                            borderWidth: 1,
                            borderRadius: 4
                        },
                        {
                            label: '月度成本',
                            data: <?php echo json_encode($monthlyCostData); ?>,
                            backgroundColor: '#f44336',
                            borderColor: '#d32f2f',
                            borderWidth: 1,
                            borderRadius: 4
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '金额 (万元)'
                            },
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top'
                        }
                    }
                }
            });

            // 月度成本控制率趋势图表
            const monthlyControlRateElement = document.getElementById('monthlyControlRateChart');
            if (!monthlyControlRateElement) {
                console.error('找不到monthlyControlRateChart元素');
                return;
            }
            const monthlyControlRateCtx = monthlyControlRateElement.getContext('2d');
            new Chart(monthlyControlRateCtx, {
                type: 'line',
                data: {
                    labels: <?php echo json_encode($monthlyLabels); ?>,
                    datasets: [{
                        label: '成本控制率',
                        data: <?php echo json_encode($monthlyControlRateData); ?>,
                        borderColor: '#4caf50',
                        backgroundColor: 'rgba(76, 175, 80, 0.1)',
                        fill: true,
                        tension: 0.4,
                        pointBackgroundColor: '#4caf50',
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2,
                        pointRadius: 5
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 120,
                            title: {
                                display: true,
                                text: '控制率 (%)'
                            },
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            }
                        },
                        x: {
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top'
                        }
                    }
                }
            });

            // 月度项目进度分布图表
            const monthlyProgressElement = document.getElementById('monthlyProgressChart');
            if (!monthlyProgressElement) {
                console.error('找不到monthlyProgressChart元素');
                return;
            }
            const monthlyProgressCtx = monthlyProgressElement.getContext('2d');
            new Chart(monthlyProgressCtx, {
                type: 'doughnut',
                data: {
                    labels: <?php echo json_encode($progressLabels); ?>,
                    datasets: [{
                        data: <?php echo json_encode($progressData); ?>,
                        backgroundColor: [
                            '#f44336', // 0-25% - 红色
                            '#ff9800', // 25-50% - 橙色
                            '#2196f3', // 50-75% - 蓝色
                            '#4caf50', // 75-100% - 绿色
                            '#9c27b0'  // 已完成 - 紫色
                        ],
                        borderColor: '#fff',
                        borderWidth: 3
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 15,
                                usePointStyle: true,
                                font: {
                                    size: 12
                                }
                            }
                        }
                    }
                }
            });

            // 成本趋势分析图表
            const costTrendElement = document.getElementById('costTrendChart');
            if (!costTrendElement) {
                console.error('找不到costTrendChart元素');
                return;
            }
            const costTrendCtx = costTrendElement.getContext('2d');

            // 生成成本趋势数据（基于现有月度数据计算趋势）
            const costTrendData = <?php echo json_encode($monthlyCostData); ?>;
            const trendLabels = <?php echo json_encode($monthlyLabels); ?>;

            // 计算趋势线数据
            const trendLineData = [];
            if (costTrendData.length > 1) {
                const firstValue = costTrendData[0];
                const lastValue = costTrendData[costTrendData.length - 1];
                const increment = (lastValue - firstValue) / (costTrendData.length - 1);

                for (let i = 0; i < costTrendData.length; i++) {
                    trendLineData.push(firstValue + increment * i);
                }
            }

            new Chart(costTrendCtx, {
                type: 'line',
                data: {
                    labels: trendLabels,
                    datasets: [
                        {
                            label: '实际成本',
                            data: costTrendData,
                            borderColor: '#dc3545',
                            backgroundColor: 'rgba(220, 53, 69, 0.1)',
                            fill: true,
                            tension: 0.4,
                            pointBackgroundColor: '#dc3545',
                            pointBorderColor: '#fff',
                            pointBorderWidth: 2,
                            pointRadius: 5
                        },
                        {
                            label: '趋势线',
                            data: trendLineData,
                            borderColor: '#007bff',
                            backgroundColor: 'transparent',
                            borderDash: [5, 5],
                            fill: false,
                            tension: 0,
                            pointRadius: 0
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: '成本趋势分析',
                            font: {
                                size: 16,
                                weight: 'bold'
                            },
                            padding: 20
                        },
                        legend: {
                            position: 'top',
                            labels: {
                                padding: 20,
                                usePointStyle: true,
                                font: {
                                    size: 12
                                }
                            }
                        },
                        tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleColor: '#fff',
                            bodyColor: '#fff',
                            borderColor: '#ddd',
                            borderWidth: 1,
                            callbacks: {
                                label: function(context) {
                                    return context.dataset.label + ': ¥' + context.parsed.y.toFixed(2) + '万';
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            display: true,
                            title: {
                                display: true,
                                text: '月份',
                                font: {
                                    size: 14,
                                    weight: 'bold'
                                }
                            },
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.1)'
                            }
                        },
                        y: {
                            display: true,
                            title: {
                                display: true,
                                text: '成本金额（万元）',
                                font: {
                                    size: 14,
                                    weight: 'bold'
                                }
                            },
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.1)'
                            },
                            ticks: {
                                callback: function(value) {
                                    return '¥' + value.toFixed(1) + '万';
                                }
                            }
                        }
                    }
                }
            });
        }

        // 重置筛选器函数
        function resetFilters() {
            document.getElementById('project-select').value = '';
            document.getElementById('month-select').value = '<?php echo date('Y-m'); ?>';

            // 提交表单以重置数据
            document.querySelector('form').submit();
        }

        // 更新最后更新时间函数
        function updateTime() {
            const now = new Date();
            const timeString = now.getFullYear() + '-' +
                String(now.getMonth() + 1).padStart(2, '0') + '-' +
                String(now.getDate()).padStart(2, '0') + ' ' +
                String(now.getHours()).padStart(2, '0') + ':' +
                String(now.getMinutes()).padStart(2, '0') + ':' +
                String(now.getSeconds()).padStart(2, '0');

            const timeElement = document.getElementById('last-update-time');
            if (timeElement) {
                timeElement.textContent = timeString;
            }
        }

        // 立即更新一次时间
        updateTime();

        // 每30秒更新一次时间
        setInterval(updateTime, 30000);
    </script>
<?php mysqli_close($link); ?>
</body>
</html>