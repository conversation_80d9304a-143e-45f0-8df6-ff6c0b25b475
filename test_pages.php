<?php
include '../config.php';
error_reporting(E_ALL);
ini_set('display_errors', 1);
session_start();

// 设置测试用的session变量
$_SESSION['xinhu_adminid'] = 1;
$_SESSION['xinhu_projectid'] = 1;
$_SESSION['xinhu_project'] = '测试项目';

// 获取一个有效的项目ID进行测试
$sql = "SELECT id, gcname FROM tuqoa_gcproject LIMIT 1";
$result = mysqli_query($link, $sql);
$testProject = mysqli_fetch_assoc($result);
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>页面功能测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .test-card {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-link {
            display: inline-block;
            margin: 10px;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .test-link:hover {
            background: #0056b3;
            color: white;
            text-decoration: none;
        }
        .status-ok {
            color: #28a745;
            font-weight: bold;
        }
        .status-error {
            color: #dc3545;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>页面功能测试</h1>
        
        <div class="test-card">
            <h3>数据库连接测试</h3>
            <?php if ($link): ?>
                <p class="status-ok">✓ 数据库连接成功</p>
            <?php else: ?>
                <p class="status-error">✗ 数据库连接失败</p>
            <?php endif; ?>
        </div>
        
        <div class="test-card">
            <h3>项目数据测试</h3>
            <?php if ($testProject): ?>
                <p class="status-ok">✓ 找到测试项目: <?php echo htmlspecialchars($testProject['gcname']); ?> (ID: <?php echo $testProject['id']; ?>)</p>
            <?php else: ?>
                <p class="status-error">✗ 未找到测试项目</p>
            <?php endif; ?>
        </div>
        
        <div class="test-card">
            <h3>页面链接测试</h3>
            <?php if ($testProject): ?>
                <a href="myxmfymx.php?projectid=<?php echo $testProject['id']; ?>" class="test-link" target="_blank">
                    测试项目费用明细页面
                </a>
                <a href="myxmcbmx.php?projectid=<?php echo $testProject['id']; ?>" class="test-link" target="_blank">
                    测试项目成本明细页面
                </a>
            <?php else: ?>
                <p class="status-error">无法生成测试链接，缺少测试项目</p>
            <?php endif; ?>
        </div>
        
        <div class="test-card">
            <h3>数据表检查</h3>
            <?php
            $tables = [
                'tuqoa_gcproject' => '工程项目表',
                'tuqoa_rydp' => '人员配置表',
                'tuqoa_hrsalary' => '人力资源工资表',
                'tuqoa_xmsjbxmx' => '项目社保明细表',
                'tuqoa_xmhstjzl' => '项目核算统计资料表',
                'tuqoa_htsf' => '合同收费表'
            ];
            
            foreach ($tables as $table => $description) {
                $checkSql = "SHOW TABLES LIKE '$table'";
                $checkResult = mysqli_query($link, $checkSql);
                if ($checkResult && mysqli_num_rows($checkResult) > 0) {
                    echo "<p class=\"status-ok\">✓ $description ($table) 存在</p>";
                } else {
                    echo "<p class=\"status-error\">✗ $description ($table) 不存在</p>";
                }
            }
            ?>
        </div>
        
        <div class="test-card">
            <h3>Session 变量检查</h3>
            <p><strong>用户ID:</strong> <?php echo $_SESSION['xinhu_adminid'] ?? '未设置'; ?></p>
            <p><strong>项目ID:</strong> <?php echo $_SESSION['xinhu_projectid'] ?? '未设置'; ?></p>
            <p><strong>项目名称:</strong> <?php echo $_SESSION['xinhu_project'] ?? '未设置'; ?></p>
        </div>
        
        <div class="test-card">
            <h3>修复内容总结</h3>
            <h5>myxmfymx.php 修复内容：</h5>
            <ul>
                <li>✓ 添加了数据库连接和参数验证</li>
                <li>✓ 修复了SQL查询中的语法错误</li>
                <li>✓ 修复了表格列数不匹配问题</li>
                <li>✓ 添加了数字格式化显示</li>
                <li>✓ 修复了变量初始化问题</li>
                <li>✓ 添加了项目ID隐藏字段</li>
            </ul>
            
            <h5>myxmcbmx.php 修复内容：</h5>
            <ul>
                <li>✓ 添加了参数验证和项目存在性检查</li>
                <li>✓ 修复了变量初始化问题</li>
                <li>✓ 添加了月份选择功能</li>
                <li>✓ 修复了表单字段名称</li>
                <li>✓ 添加了错误处理</li>
            </ul>
        </div>
        
        <div class="test-card">
            <h3>使用说明</h3>
            <ol>
                <li>确保数据库连接正常</li>
                <li>确保相关数据表存在并有数据</li>
                <li>通过URL参数 <code>projectid</code> 指定要查看的项目</li>
                <li>在页面上选择要查询的月份</li>
                <li>点击查询按钮获取数据</li>
            </ol>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
