<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>gztz.php 数据来源分析</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }
        h3 {
            color: #2980b9;
            margin-top: 20px;
        }
        .data-source {
            background: #ecf0f1;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #3498db;
        }
        .static-data {
            background: #fff3cd;
            border-left-color: #ffc107;
        }
        .dynamic-data {
            background: #d1ecf1;
            border-left-color: #17a2b8;
        }
        .table-info {
            background: #d4edda;
            border-left-color: #28a745;
        }
        .chart-info {
            background: #f8d7da;
            border-left-color: #dc3545;
        }
        .code-snippet {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        .tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            margin-right: 5px;
        }
        .tag-static { background: #ffc107; color: #212529; }
        .tag-dynamic { background: #17a2b8; color: white; }
        .tag-table { background: #28a745; color: white; }
        .tag-chart { background: #dc3545; color: white; }
        .tag-form { background: #fd7e14; color: white; }
        .tag-multi { background: #6f42c1; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <h1>gztz.php 页面数据来源分析</h1>
        
        <h2>页面概述</h2>
        <p>gztz.php 是一个工作台账页面，用于记录和统计各类工程监理工作的日常活动，包括监理日志、安全检查、现场巡查、旁站监理等多种工作类型的详细记录和统计分析。</p>
        
        <h2>数据来源分析</h2>
        
        <h3>1. 用户输入数据</h3>
        
        <div class="data-source static-data">
            <h4><span class="tag tag-form">表单</span>日期选择</h4>
            <p><strong>数据内容：</strong>查询的具体日期</p>
            <p><strong>来源：</strong>用户通过表单输入</p>
            <div class="code-snippet">
$firstDayOfMonth = date('Y-m-d');  // 默认为当前日期
$startDate = isset($_POST['start-date']) ? $_POST['start-date'] : $firstDayOfMonth;
            </div>
            <p><strong>特点：</strong>与其他页面不同，此页面主要查询单日数据，不是日期范围</p>
        </div>
        
        <h3>2. 多表统计数据</h3>
        
        <div class="data-source dynamic-data">
            <h4><span class="tag tag-multi">多表</span>工作类型统计</h4>
            <p><strong>数据内容：</strong>11种不同工作类型的当日数量统计</p>
            <p><strong>来源：</strong>11个不同的业务表</p>
            <div class="code-snippet">
// 监理日志
SELECT count(*) sl FROM `tuqoa_jlrz` WHERE `kssj`='$startDate'

// 安全日志
SELECT count(*) sl FROM `tuqoa_aqrz` WHERE `kssj`='$startDate'

// 现场巡查
SELECT count(*) sl FROM `tuqoa_xmxjcc` WHERE `xjrq`='$startDate'

// 旁站监理
SELECT count(*) sl FROM `tuqoa_pzjl` WHERE `kssj` like '$startDate%'

// 专项检查
SELECT count(*) sl FROM `tuqoa_zxjc` WHERE `jcsj`='$startDate'

// 安全检查
SELECT count(*) sl FROM `tuqoa_aqjc` WHERE `jcsj`='$startDate'

// 工程验收
SELECT count(*) sl FROM `tuqoa_gcys` WHERE `yssj` like '$startDate%'

// 见证检验
SELECT count(*) sl FROM `tuqoa_jcys` WHERE `jcsj`='$startDate'

// 项目服务
SELECT count(*) sl FROM `tuqoa_xmfw` WHERE `fwrq`='$startDate'

// 会议纪要
SELECT count(*) sl FROM `tuqoa_hyjy` WHERE `kssj`='$startDate'

// 平行检验
SELECT count(*) sl FROM `tuqoa_pxjc` WHERE `qysj`='$startDate'
            </div>
            <p><strong>说明：</strong>每个表使用不同的时间字段，部分使用精确匹配，部分使用LIKE模糊匹配</p>
        </div>
        
        <h3>3. 详细记录数据</h3>
        
        <div class="data-source table-info">
            <h4><span class="tag tag-table">表格</span>工作明细记录</h4>
            <p><strong>数据内容：</strong>各类工作的详细记录信息</p>
            <p><strong>来源：</strong>对应的业务表</p>
            <div class="code-snippet">
// 现场巡查明细
SELECT * FROM `tuqoa_xmxjcc` WHERE `xjrq`='$startDate' order by id desc

// 见证检验明细
SELECT * FROM `tuqoa_jcys` WHERE `jcsj`='$startDate' order by id desc

// 建筑检验明细
SELECT * FROM `tuqoa_jzjy` WHERE `qysj`='$startDate' order by id desc

// 旁站监理明细
SELECT * FROM `tuqoa_pzjl` WHERE `kssj` like '$startDate%' order by id desc

// 工程验收明细
SELECT * FROM `tuqoa_gcys` WHERE `yssj` like '$startDate%' order by id desc

// 平行检验明细
SELECT * FROM `tuqoa_pxjc` WHERE `qysj` like '$startDate%' order by id desc

// 会议纪要明细
SELECT * FROM `tuqoa_hyjy` WHERE `kssj` = '$startDate' order by id desc

// 监理日志明细
SELECT * FROM `tuqoa_jlrz` WHERE `kssj` = '$startDate' order by id desc

// 安全日志明细
SELECT * FROM `tuqoa_aqrz` WHERE `kssj` = '$startDate' order by id desc
            </div>
            <p><strong>说明：</strong>按ID降序排列，显示最新的记录</p>
        </div>
        
        <h3>4. 图表数据</h3>
        
        <div class="data-source chart-info">
            <h4><span class="tag tag-chart">图表</span>工作时间分布</h4>
            <p><strong>数据内容：</strong>当日各时间段的工作分布</p>
            <p><strong>来源：</strong>多表时间字段统计</p>
            <div class="code-snippet">
// 配置表和时间字段的映射
$tables_with_time = [
    'tuqoa_jlrz' => 'kssj',
    'tuqoa_aqrz' => 'kssj', 
    'tuqoa_xmxjcc' => 'xjrq',
    'tuqoa_pzjl' => 'kssj',
    'tuqoa_zxjc' => 'jcsj',
    'tuqoa_aqjc' => 'jcsj',
    'tuqoa_gcys' => 'yssj',
    'tuqoa_jcys' => 'jcsj',
    'tuqoa_xmfw' => 'fwrq',
    'tuqoa_hyjy' => 'kssj',
    'tuqoa_pxjc' => 'qysj'
];

// 查询每个表的时间数据
foreach ($tables_with_time as $table => $time_field) {
    $sql = "SELECT $time_field FROM `$table` WHERE DATE($time_field) = '$startDate'";
}
            </div>
            <p><strong>说明：</strong>提取时间信息，按小时分组统计工作分布</p>
        </div>
        
        <div class="data-source chart-info">
            <h4><span class="tag tag-chart">图表</span>工作类型分布</h4>
            <p><strong>数据内容：</strong>各类工作的数量分布</p>
            <p><strong>来源：</strong>统计数据汇总</p>
            <div class="code-snippet">
// 工作类型和对应数量
$work_types = [
    '监理日志' => $jlrz_count,
    '安全日志' => $aqrz_count,
    '现场巡查' => $xjcc_count,
    '旁站监理' => $pzjl_count,
    '专项检查' => $zxjc_count,
    '安全检查' => $aqjc_count,
    '工程验收' => $gcys_count,
    '见证检验' => $jcys_count,
    '项目服务' => $xmfw_count,
    '会议纪要' => $hyjy_count,
    '平行检验' => $pxjc_count
];
            </div>
            <p><strong>说明：</strong>生成饼图显示各类工作的占比</p>
        </div>
        
        <div class="data-source chart-info">
            <h4><span class="tag tag-chart">图表</span>最近7天工作趋势</h4>
            <p><strong>数据内容：</strong>最近7天的工作总量趋势</p>
            <p><strong>来源：</strong>多表统计</p>
            <div class="code-snippet">
// 循环查询最近7天
for ($i = 6; $i >= 0; $i--) {
    $date = date('Y-m-d', strtotime("-$i days"));
    
    // 统计当天的工作总数
    foreach ($work_tables as $table => $time_field) {
        $sql = "SELECT COUNT(*) as count FROM `$table` WHERE DATE($time_field) = '$date'";
    }
}
            </div>
            <p><strong>说明：</strong>生成折线图显示工作量趋势</p>
        </div>
        
        <h2>数据表结构</h2>
        
        <div class="table-info">
            <h4>主要数据表及关键字段</h4>
            <ul>
                <li><strong>tuqoa_jlrz：</strong>监理日志表
                    <ul><li>kssj：开始时间</li></ul>
                </li>
                <li><strong>tuqoa_aqrz：</strong>安全日志表
                    <ul><li>kssj：开始时间</li></ul>
                </li>
                <li><strong>tuqoa_xmxjcc：</strong>现场巡查表
                    <ul><li>xjrq：巡检日期</li></ul>
                </li>
                <li><strong>tuqoa_pzjl：</strong>旁站监理表
                    <ul><li>kssj：开始时间</li></ul>
                </li>
                <li><strong>tuqoa_zxjc：</strong>专项检查表
                    <ul><li>jcsj：检查时间</li></ul>
                </li>
                <li><strong>tuqoa_aqjc：</strong>安全检查表
                    <ul><li>jcsj：检查时间</li></ul>
                </li>
                <li><strong>tuqoa_gcys：</strong>工程验收表
                    <ul><li>yssj：验收时间</li></ul>
                </li>
                <li><strong>tuqoa_jcys：</strong>见证检验表
                    <ul><li>jcsj：检查时间</li></ul>
                </li>
                <li><strong>tuqoa_xmfw：</strong>项目服务表
                    <ul><li>fwrq：服务日期</li></ul>
                </li>
                <li><strong>tuqoa_hyjy：</strong>会议纪要表
                    <ul><li>kssj：开始时间</li></ul>
                </li>
                <li><strong>tuqoa_pxjc：</strong>平行检验表
                    <ul><li>qysj：起始时间</li></ul>
                </li>
            </ul>
        </div>
        
        <h2>页面特点</h2>
        
        <h3>1. 多业务整合</h3>
        <ul>
            <li>整合11个不同的业务表</li>
            <li>涵盖监理工作的各个方面</li>
            <li>统一的查询和展示框架</li>
        </ul>
        
        <h3>2. 单日聚焦</h3>
        <ul>
            <li>主要查询单日数据，不是日期范围</li>
            <li>适合日常工作记录和检查</li>
            <li>提供当日工作的全面视图</li>
        </ul>
        
        <h3>3. 时间字段多样性</h3>
        <ul>
            <li>不同表使用不同的时间字段名</li>
            <li>部分使用精确日期匹配</li>
            <li>部分使用LIKE模糊匹配（包含时间）</li>
        </ul>
        
        <h3>4. 详细记录展示</h3>
        <ul>
            <li>不仅统计数量，还显示详细记录</li>
            <li>每类工作都有对应的明细表格</li>
            <li>按最新记录排序</li>
        </ul>
        
        <h3>5. 多维度分析</h3>
        <ul>
            <li>工作类型分布分析</li>
            <li>时间分布分析</li>
            <li>趋势分析（最近7天）</li>
        </ul>
        
        <h2>业务逻辑</h2>
        
        <div class="data-source">
            <h4>工作台账管理逻辑</h4>
            <ul>
                <li><strong>日常记录：</strong>记录各类监理工作的执行情况</li>
                <li><strong>统计分析：</strong>统计当日各类工作的数量</li>
                <li><strong>明细查看：</strong>查看具体工作的详细信息</li>
                <li><strong>趋势监控：</strong>监控工作量的变化趋势</li>
                <li><strong>时间分布：</strong>分析工作在一天中的时间分布</li>
            </ul>
        </div>
        
        <h2>技术特点</h2>
        
        <div class="data-source">
            <h4>数据处理特点</h4>
            <ul>
                <li>多表统一查询框架</li>
                <li>灵活的时间字段处理</li>
                <li>精确匹配和模糊匹配结合</li>
                <li>数据聚合和明细并存</li>
            </ul>
            
            <h4>界面特点</h4>
            <ul>
                <li>工作状态的颜色编码</li>
                <li>卡片式布局展示统计</li>
                <li>表格展示详细记录</li>
                <li>图表展示分析结果</li>
            </ul>
        </div>
        
        <h2>与其他页面的区别</h2>
        
        <div class="data-source">
            <h4>独特之处</h4>
            <ul>
                <li><strong>数据范围：</strong>单日数据，不是时间范围</li>
                <li><strong>业务领域：</strong>专注监理工作记录</li>
                <li><strong>数据源：</strong>11个业务表，数量最多</li>
                <li><strong>展示方式：</strong>统计+明细双重展示</li>
                <li><strong>时间处理：</strong>多种时间字段和匹配方式</li>
            </ul>
        </div>
        
        <h2>总结</h2>
        <p>gztz.php是一个专业的工作台账管理页面，数据来源包括：</p>
        <ul>
            <li><strong>用户输入：</strong>单日日期选择</li>
            <li><strong>多表数据：</strong>11个监理业务表的统计和明细数据</li>
            <li><strong>统计分析：</strong>工作类型、时间分布、趋势分析</li>
            <li><strong>详细记录：</strong>各类工作的具体执行记录</li>
        </ul>
        <p>该页面通过整合多个业务表，为监理工作提供了全面的日常记录和统计分析功能，是监理业务管理的重要工具。</p>
    </div>
</body>
</html>
