<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>明细页面专项分析</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }
        h3 {
            color: #2980b9;
            margin-top: 20px;
        }
        .page-analysis {
            background: #f8f9fa;
            padding: 20px;
            margin: 15px 0;
            border-radius: 8px;
            border-left: 4px solid #17a2b8;
        }
        .data-source {
            background: #e9ecef;
            padding: 10px;
            margin: 8px 0;
            border-radius: 5px;
        }
        .code-snippet {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 8px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            overflow-x: auto;
        }
        .tag {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 11px;
            font-weight: bold;
            margin-right: 3px;
        }
        .tag-dynamic { background: #17a2b8; color: white; }
        .tag-table { background: #28a745; color: white; }
        .tag-complex { background: #6f42c1; color: white; }
        .tag-calc { background: #fd7e14; color: white; }
        .summary-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .summary-table th, .summary-table td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: left;
        }
        .summary-table th {
            background-color: #f8f9fa;
        }
        .feature-box {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>明细页面专项分析</h1>
        
        <div class="feature-box">
            <h3>📋 明细页面特点</h3>
            <p>明细页面是系统中提供详细数据展示的重要组成部分，主要特点包括：</p>
            <ul>
                <li>提供项目级别的详细数据分析</li>
                <li>支持时间范围筛选和项目筛选</li>
                <li>涉及多表关联和复杂计算</li>
                <li>为管理决策提供详细的数据支撑</li>
            </ul>
        </div>

        <h2>重点明细页面分析</h2>

        <div class="page-analysis">
            <h3>myxmfymx.php - 某月项目费用明细</h3>
            
            <h4>页面功能</h4>
            <p>提供指定月份范围内项目的详细费用分析，包括人员工资、社保费用、管理费用等多个维度的费用明细。</p>
            
            <h4>主要数据源</h4>
            <div class="data-source">
                <h5><span class="tag tag-dynamic">动态</span>项目基础信息</h5>
                <div class="code-snippet">
SELECT * FROM `tuqoa_gcproject` WHERE id=$projectid
                </div>
                <p>获取指定项目的基本信息</p>
            </div>
            
            <div class="data-source">
                <h5><span class="tag tag-complex">复杂</span>人员工资费用</h5>
                <div class="code-snippet">
// 项目人员
SELECT * FROM `tuqoa_rydp` 
WHERE `drxmid`=$projectId and `sfqz`='全职' and `state`='在职'

// 人员工资
SELECT * FROM `tuqoa_hrsalary` 
WHERE `uname`='$employeeName' and `month`>='$startDate' and `month`<='$endDate'
                </div>
                <p>通过人员配置表关联工资表，计算项目人员的工资费用</p>
            </div>
            
            <div class="data-source">
                <h5><span class="tag tag-dynamic">动态</span>社保费用</h5>
                <div class="code-snippet">
SELECT ifnull(sum(ylxj),0) ylhj,ifnull(sum(yiliaobxxj),0) yiliaobxxjhj 
FROM `tuqoa_xmsjbxmx` 
WHERE `projectid`=$projectId and `ys`>='$startDate' and `ys`<='$endDate'
                </div>
                <p>统计项目的养老保险和医疗保险费用</p>
            </div>
            
            <div class="data-source">
                <h5><span class="tag tag-dynamic">动态</span>管理费用</h5>
                <div class="code-snippet">
SELECT * FROM `tuqoa_xmhstjzl` 
WHERE `projectid`=$projectId and `sbrq`>='$startDate' and `sbrq`<='$endDate'
                </div>
                <p>查询项目的管理费、办公用品、税金等费用</p>
            </div>
            
            <h4>特点</h4>
            <ul>
                <li>支持月份范围查询</li>
                <li>多维度费用分析</li>
                <li>人员费用详细计算</li>
                <li>社保费用专项统计</li>
            </ul>
        </div>

        <div class="page-analysis">
            <h3>xmfymx.php - 项目费用明细</h3>
            
            <h4>页面功能</h4>
            <p>提供所有活跃项目的费用明细分析，是myxmfymx.php的扩展版本，支持多项目对比分析。</p>
            
            <h4>主要数据源</h4>
            <div class="data-source">
                <h5><span class="tag tag-dynamic">动态</span>活跃项目列表</h5>
                <div class="code-snippet">
SELECT * FROM `tuqoa_gcproject` 
WHERE xmzt in ('新开工项目','在建项目','完工未结算') 
order by id desc
                </div>
                <p>获取所有未完工的活跃项目</p>
            </div>
            
            <div class="data-source">
                <h5><span class="tag tag-complex">复杂</span>批量费用计算</h5>
                <div class="code-snippet">
// 对每个项目执行相同的费用计算逻辑
foreach ($projects as $project) {
    // 人员工资计算
    // 社保费用计算  
    // 管理费用计算
    // 汇总统计
}
                </div>
                <p>为每个项目执行完整的费用分析</p>
            </div>
            
            <div class="data-source">
                <h5><span class="tag tag-calc">计算</span>安全处理</h5>
                <div class="code-snippet">
$sql2="SELECT * FROM `tuqoa_hrsalary` 
       WHERE `uname`='".mysqli_real_escape_string($link, $row1["dpryxm"])."' 
       and `month`>='$startDate' and `month`<='$endDate'";
                </div>
                <p>使用mysqli_real_escape_string防止SQL注入</p>
            </div>
            
            <h4>特点</h4>
            <ul>
                <li>多项目批量分析</li>
                <li>安全的SQL查询</li>
                <li>完整的费用体系</li>
                <li>支持项目对比</li>
            </ul>
        </div>

        <div class="page-analysis">
            <h3>ndyjsflb.php - 年度预决算分类表</h3>
            
            <h4>页面功能</h4>
            <p>提供年度预算与决算的对比分析，是财务管理的重要工具，用于监控预算执行情况。</p>
            
            <h4>主要数据源</h4>
            <div class="data-source">
                <h5><span class="tag tag-dynamic">动态</span>预决算汇总</h5>
                <div class="code-snippet">
SELECT ifnull(sum(yjje),0) as yjjehj,ifnull(sum(ysje),0) as ysjehj 
FROM `tuqoa_htsf` 
WHERE `yjsj` like '$startDate%'
                </div>
                <p>统计年度预计金额和已收金额</p>
            </div>
            
            <div class="data-source">
                <h5><span class="tag tag-complex">复杂</span>预决算明细</h5>
                <div class="code-snippet">
SELECT *,(select fwf from tuqoa_htgl where tuqoa_htgl.htmc=tuqoa_htsf.htmc LIMIT 1) fwf 
FROM `tuqoa_htsf` 
WHERE `yjsj` like '$startDate%'
                </div>
                <p>关联合同表获取服务费信息，提供完整的预决算明细</p>
            </div>
            
            <div class="data-source">
                <h5><span class="tag tag-calc">计算</span>执行率计算</h5>
                <div class="code-snippet">
$collection_rate = ($yjjehj > 0) ? round(($ysjehj / $yjjehj) * 100, 2) : 0;
                </div>
                <p>计算预算执行率（回款率）</p>
            </div>
            
            <h4>特点</h4>
            <ul>
                <li>年度维度分析</li>
                <li>预决算对比</li>
                <li>执行率监控</li>
                <li>子查询优化</li>
            </ul>
        </div>

        <h2>明细页面数据表使用分析</h2>
        
        <table class="summary-table">
            <thead>
                <tr>
                    <th>数据表</th>
                    <th>主要用途</th>
                    <th>使用页面</th>
                    <th>查询特点</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>tuqoa_gcproject</td>
                    <td>项目基础信息</td>
                    <td>所有明细页面</td>
                    <td>基础查询</td>
                </tr>
                <tr>
                    <td>tuqoa_rydp</td>
                    <td>人员配置</td>
                    <td>myxmfymx.php, xmfymx.php</td>
                    <td>关联查询</td>
                </tr>
                <tr>
                    <td>tuqoa_hrsalary</td>
                    <td>工资数据</td>
                    <td>myxmfymx.php, xmfymx.php</td>
                    <td>时间范围查询</td>
                </tr>
                <tr>
                    <td>tuqoa_xmsjbxmx</td>
                    <td>社保明细</td>
                    <td>myxmfymx.php, xmfymx.php</td>
                    <td>聚合查询</td>
                </tr>
                <tr>
                    <td>tuqoa_xmhstjzl</td>
                    <td>管理费用</td>
                    <td>myxmfymx.php, xmfymx.php</td>
                    <td>明细查询</td>
                </tr>
                <tr>
                    <td>tuqoa_htsf</td>
                    <td>收费数据</td>
                    <td>ndyjsflb.php</td>
                    <td>子查询关联</td>
                </tr>
                <tr>
                    <td>tuqoa_htgl</td>
                    <td>合同数据</td>
                    <td>ndyjsflb.php</td>
                    <td>子查询关联</td>
                </tr>
            </tbody>
        </table>

        <h2>技术特点总结</h2>
        
        <div class="data-source">
            <h4>查询模式</h4>
            <ul>
                <li><strong>多层嵌套查询：</strong>项目→人员→工资的三层关联</li>
                <li><strong>时间范围筛选：</strong>支持月份和日期范围查询</li>
                <li><strong>聚合计算：</strong>大量使用SUM、IFNULL等函数</li>
                <li><strong>子查询优化：</strong>使用子查询关联不同表的数据</li>
            </ul>
        </div>
        
        <div class="data-source">
            <h4>数据处理</h4>
            <ul>
                <li><strong>费用分类：</strong>工资、社保、管理费等分类统计</li>
                <li><strong>安全处理：</strong>SQL注入防护和错误处理</li>
                <li><strong>空值处理：</strong>使用IFNULL确保数据完整性</li>
                <li><strong>精度控制：</strong>财务数据的精确计算</li>
            </ul>
        </div>

        <h2>业务价值</h2>
        
        <div class="feature-box">
            <h3>💼 管理价值</h3>
            <ul>
                <li><strong>成本控制：</strong>详细的项目费用分析帮助控制成本</li>
                <li><strong>预算管理：</strong>预决算对比支持预算执行监控</li>
                <li><strong>人员管理：</strong>人员费用分析支持人力资源决策</li>
                <li><strong>财务监控：</strong>多维度费用分析支持财务管理</li>
            </ul>
        </div>

        <h2>优化建议</h2>
        
        <div class="data-source">
            <h4>性能优化</h4>
            <ul>
                <li>优化多层嵌套查询，考虑使用JOIN替代</li>
                <li>为常用查询字段添加数据库索引</li>
                <li>实现查询结果缓存机制</li>
                <li>分页处理大量数据</li>
            </ul>
        </div>
        
        <div class="data-source">
            <h4>功能增强</h4>
            <ul>
                <li>添加数据导出功能（Excel、PDF）</li>
                <li>增加更多筛选条件</li>
                <li>实现数据对比分析</li>
                <li>添加图表可视化</li>
            </ul>
        </div>

        <h2>总结</h2>
        <p>明细页面是系统中数据分析的重要组成部分，具有以下特点：</p>
        <ul>
            <li><strong>数据详细：</strong>提供项目级别的详细数据分析</li>
            <li><strong>查询复杂：</strong>涉及多表关联和复杂计算</li>
            <li><strong>功能专业：</strong>针对特定业务需求设计</li>
            <li><strong>价值重要：</strong>为管理决策提供重要数据支撑</li>
        </ul>
        <p>这些页面为项目管理、成本控制、财务分析等提供了详细的数据基础，是系统的重要价值体现。</p>
    </div>
</body>
</html>
