<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库优化建议</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 10px;
            font-size: 2.5rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .subtitle {
            text-align: center;
            color: #7f8c8d;
            margin-bottom: 30px;
            font-size: 1.1rem;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }
        h3 {
            color: #2980b9;
            margin-top: 20px;
        }
        .optimization-card {
            background: #f8f9fa;
            padding: 20px;
            margin: 15px 0;
            border-radius: 10px;
            border-left: 4px solid #3498db;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .high-priority { border-left-color: #e74c3c; }
        .medium-priority { border-left-color: #f39c12; }
        .low-priority { border-left-color: #27ae60; }
        .code-snippet {
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 10px 0;
        }
        .sql-before {
            background: #e74c3c;
            color: white;
        }
        .sql-after {
            background: #27ae60;
            color: white;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .table th, .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        .table th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            font-weight: 600;
        }
        .tag {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            margin-right: 5px;
        }
        .tag-high { background: #e74c3c; color: white; }
        .tag-medium { background: #f39c12; color: white; }
        .tag-low { background: #27ae60; color: white; }
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .success-box {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 数据库优化建议</h1>
        <p class="subtitle">基于PHP页面分析的数据库性能优化方案</p>
        
        <h2>📊 当前数据库使用情况分析</h2>
        
        <table class="table">
            <thead>
                <tr>
                    <th>数据表</th>
                    <th>使用频率</th>
                    <th>查询复杂度</th>
                    <th>优化优先级</th>
                    <th>主要问题</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>tuqoa_gcproject</td>
                    <td>极高 (8+页面)</td>
                    <td>中等</td>
                    <td><span class="tag tag-high">高</span></td>
                    <td>频繁的状态筛选查询</td>
                </tr>
                <tr>
                    <td>tuqoa_htgl</td>
                    <td>高 (6+页面)</td>
                    <td>中等</td>
                    <td><span class="tag tag-high">高</span></td>
                    <td>时间范围查询较多</td>
                </tr>
                <tr>
                    <td>tuqoa_xmcztjb</td>
                    <td>高 (6+页面)</td>
                    <td>高</td>
                    <td><span class="tag tag-high">高</span></td>
                    <td>复杂的聚合查询</td>
                </tr>
                <tr>
                    <td>tuqoa_htsf</td>
                    <td>中等 (5+页面)</td>
                    <td>中等</td>
                    <td><span class="tag tag-medium">中</span></td>
                    <td>多字段时间查询</td>
                </tr>
                <tr>
                    <td>tuqoa_hrsalary</td>
                    <td>中等 (3+页面)</td>
                    <td>高</td>
                    <td><span class="tag tag-medium">中</span></td>
                    <td>嵌套查询较多</td>
                </tr>
                <tr>
                    <td>监理业务表</td>
                    <td>低 (1页面)</td>
                    <td>低</td>
                    <td><span class="tag tag-low">低</span></td>
                    <td>单表查询为主</td>
                </tr>
            </tbody>
        </table>

        <h2>🔥 高优先级优化建议</h2>

        <div class="optimization-card high-priority">
            <h3>1. 核心表索引优化</h3>
            <h4>tuqoa_gcproject 表优化</h4>
            <div class="warning-box">
                <strong>问题：</strong>频繁的项目状态筛选查询，如 WHERE xmzt in ('新开工项目','在建项目','完工未结算')
            </div>
            <div class="code-snippet">
-- 添加项目状态索引
CREATE INDEX idx_gcproject_xmzt ON tuqoa_gcproject(xmzt);

-- 添加复合索引（状态+时间）
CREATE INDEX idx_gcproject_status_time ON tuqoa_gcproject(xmzt, qdsj);

-- 添加项目ID索引（如果不存在主键）
CREATE INDEX idx_gcproject_id ON tuqoa_gcproject(id);
            </div>
            <div class="success-box">
                <strong>预期效果：</strong>项目筛选查询性能提升 60-80%
            </div>
        </div>

        <div class="optimization-card high-priority">
            <h3>2. 时间查询优化</h3>
            <h4>tuqoa_htgl 和 tuqoa_htsf 表优化</h4>
            <div class="warning-box">
                <strong>问题：</strong>大量使用 DATE_FORMAT 和时间范围查询
            </div>
            <div class="code-snippet">
-- 合同管理表时间索引
CREATE INDEX idx_htgl_qdsj ON tuqoa_htgl(qdsj);

-- 收费表时间索引
CREATE INDEX idx_htsf_yjsj ON tuqoa_htsf(yjsj);
CREATE INDEX idx_htsf_sksj ON tuqoa_htsf(sksj);

-- 复合索引（项目ID+时间）
CREATE INDEX idx_htsf_project_time ON tuqoa_htsf(projectid, yjsj);
            </div>
        </div>

        <div class="optimization-card high-priority">
            <h3>3. 关联查询优化</h3>
            <h4>项目关联字段索引</h4>
            <div class="code-snippet">
-- 成本表项目关联索引
CREATE INDEX idx_xmcztjb_projectid ON tuqoa_xmcztjb(projectid);
CREATE INDEX idx_xmcztjb_project_date ON tuqoa_xmcztjb(projectid, sbrq);

-- 人员配置表索引
CREATE INDEX idx_rydp_drxmid ON tuqoa_rydp(drxmid);
CREATE INDEX idx_rydp_status ON tuqoa_rydp(sfqz, state);

-- 工资表索引
CREATE INDEX idx_hrsalary_uname_month ON tuqoa_hrsalary(uname, month);
            </div>
        </div>

        <h2>⚡ 中优先级优化建议</h2>

        <div class="optimization-card medium-priority">
            <h3>4. SQL查询优化</h3>
            <h4>减少嵌套查询</h4>
            <div class="code-snippet sql-before">
-- 优化前：多层嵌套查询
SELECT * FROM tuqoa_rydp WHERE drxmid = $projectId;
-- 然后对每个人员执行：
SELECT * FROM tuqoa_hrsalary WHERE uname = '$name' AND month = '$month';
            </div>
            <div class="code-snippet sql-after">
-- 优化后：使用JOIN
SELECT r.*, h.* 
FROM tuqoa_rydp r
LEFT JOIN tuqoa_hrsalary h ON r.dpryxm = h.uname 
WHERE r.drxmid = $projectId 
  AND r.sfqz = '全职' 
  AND r.state = '在职'
  AND h.month BETWEEN '$startDate' AND '$endDate';
            </div>
        </div>

        <div class="optimization-card medium-priority">
            <h3>5. 聚合查询优化</h3>
            <h4>使用物化视图或汇总表</h4>
            <div class="code-snippet">
-- 创建月度汇总表
CREATE TABLE tuqoa_monthly_summary (
    year_month VARCHAR(7),
    project_id INT,
    total_cost DECIMAL(15,2),
    total_income DECIMAL(15,2),
    employee_count INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_summary_month (year_month),
    INDEX idx_summary_project (project_id)
);

-- 定期更新汇总数据
INSERT INTO tuqoa_monthly_summary 
SELECT 
    DATE_FORMAT(sbrq, '%Y-%m') as year_month,
    projectid,
    SUM(wccz) as total_cost,
    COUNT(DISTINCT uid) as employee_count
FROM tuqoa_xmcztjb 
GROUP BY DATE_FORMAT(sbrq, '%Y-%m'), projectid;
            </div>
        </div>

        <h2>🔧 低优先级优化建议</h2>

        <div class="optimization-card low-priority">
            <h3>6. 数据类型优化</h3>
            <div class="code-snippet">
-- 优化日期字段类型
ALTER TABLE tuqoa_htgl MODIFY qdsj DATE;
ALTER TABLE tuqoa_htsf MODIFY yjsj DATE;
ALTER TABLE tuqoa_htsf MODIFY sksj DATE;

-- 优化数值字段类型
ALTER TABLE tuqoa_htgl MODIFY fwf DECIMAL(15,2);
ALTER TABLE tuqoa_xmcztjb MODIFY wccz DECIMAL(15,2);
            </div>
        </div>

        <div class="optimization-card low-priority">
            <h3>7. 分区表设计</h3>
            <div class="code-snippet">
-- 对大表按年份分区
ALTER TABLE tuqoa_xmcztjb 
PARTITION BY RANGE (YEAR(sbrq)) (
    PARTITION p2022 VALUES LESS THAN (2023),
    PARTITION p2023 VALUES LESS THAN (2024),
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
            </div>
        </div>

        <h2>📈 性能监控建议</h2>

        <div class="optimization-card">
            <h3>8. 查询性能监控</h3>
            <div class="code-snippet">
-- 启用慢查询日志
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 2;

-- 查看慢查询
SELECT * FROM mysql.slow_log 
WHERE start_time > DATE_SUB(NOW(), INTERVAL 1 DAY)
ORDER BY query_time DESC;

-- 分析查询执行计划
EXPLAIN SELECT * FROM tuqoa_gcproject WHERE xmzt = '在建项目';
            </div>
        </div>

        <h2>🛡️ 安全优化建议</h2>

        <div class="optimization-card">
            <h3>9. SQL注入防护</h3>
            <div class="code-snippet sql-before">
-- 危险的查询方式
$sql = "SELECT * FROM tuqoa_hrsalary WHERE uname='".$row1["dpryxm"]."'";
            </div>
            <div class="code-snippet sql-after">
-- 安全的查询方式
$stmt = $link->prepare("SELECT * FROM tuqoa_hrsalary WHERE uname=?");
$stmt->bind_param("s", $row1["dpryxm"]);
$stmt->execute();
$result = $stmt->get_result();
            </div>
        </div>

        <h2>📋 实施计划</h2>

        <table class="table">
            <thead>
                <tr>
                    <th>阶段</th>
                    <th>优化项目</th>
                    <th>预计时间</th>
                    <th>风险等级</th>
                    <th>预期收益</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>第一阶段</td>
                    <td>核心表索引优化</td>
                    <td>1-2天</td>
                    <td><span class="tag tag-low">低</span></td>
                    <td>性能提升60-80%</td>
                </tr>
                <tr>
                    <td>第二阶段</td>
                    <td>SQL查询重构</td>
                    <td>3-5天</td>
                    <td><span class="tag tag-medium">中</span></td>
                    <td>减少数据库负载</td>
                </tr>
                <tr>
                    <td>第三阶段</td>
                    <td>汇总表设计</td>
                    <td>5-7天</td>
                    <td><span class="tag tag-medium">中</span></td>
                    <td>复杂查询性能提升</td>
                </tr>
                <tr>
                    <td>第四阶段</td>
                    <td>分区表和监控</td>
                    <td>2-3天</td>
                    <td><span class="tag tag-low">低</span></td>
                    <td>长期性能保障</td>
                </tr>
            </tbody>
        </table>

        <div class="success-box">
            <h3>🎯 预期总体效果</h3>
            <ul>
                <li><strong>查询性能：</strong>整体查询速度提升 50-70%</li>
                <li><strong>并发能力：</strong>支持更多用户同时访问</li>
                <li><strong>系统稳定性：</strong>减少数据库锁等待和超时</li>
                <li><strong>用户体验：</strong>页面加载速度明显改善</li>
            </ul>
        </div>

        <div class="warning-box">
            <h3>⚠️ 注意事项</h3>
            <ul>
                <li>在生产环境实施前，请在测试环境充分验证</li>
                <li>建议在业务低峰期执行索引创建操作</li>
                <li>实施前请做好数据备份</li>
                <li>监控优化后的性能指标，及时调整</li>
            </ul>
        </div>

        <div style="text-align: center; margin-top: 40px; color: #7f8c8d;">
            <p>📅 建议制定时间：2025年8月7日</p>
            <p>🔄 建议定期评估和更新优化方案</p>
        </div>
    </div>
</body>
</html>
