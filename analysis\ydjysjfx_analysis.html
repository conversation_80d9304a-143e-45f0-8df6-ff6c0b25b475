<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>月度经营数据分析</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 10px;
            font-size: 2.5rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .subtitle {
            text-align: center;
            color: #7f8c8d;
            margin-bottom: 30px;
            font-size: 1.1rem;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }
        h3 {
            color: #2980b9;
            margin-top: 20px;
        }
        .analysis-card {
            background: #f8f9fa;
            padding: 20px;
            margin: 15px 0;
            border-radius: 10px;
            border-left: 4px solid #3498db;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .data-source { border-left-color: #28a745; }
        .feature { border-left-color: #ffc107; }
        .chart { border-left-color: #dc3545; }
        .business { border-left-color: #6f42c1; }
        .code-snippet {
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 10px 0;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .table th, .table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
            font-size: 14px;
        }
        .table th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            font-weight: 600;
        }
        .highlight-box {
            background: #e7f3ff;
            border: 1px solid #b8daff;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .tag {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            margin-right: 5px;
        }
        .tag-query { background: #28a745; color: white; }
        .tag-calc { background: #ffc107; color: #212529; }
        .tag-chart { background: #dc3545; color: white; }
        .tag-trend { background: #6f42c1; color: white; }
        .metric-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📈 月度经营数据分析</h1>
        <p class="subtitle">ydjysjfx.php - 企业月度经营状况综合分析页面</p>
        
        <h2>📋 页面功能概述</h2>
        
        <div class="analysis-card business">
            <h3>核心功能</h3>
            <p>月度经营数据分析页面是企业经营管理的核心工具，主要功能包括：</p>
            <ul>
                <li><strong>月度经营指标：</strong>服务费、产值、收款、人员等关键指标统计</li>
                <li><strong>趋势分析：</strong>12个月的经营数据趋势对比</li>
                <li><strong>综合分析：</strong>收入、成本、利润的综合分析</li>
                <li><strong>人员效率：</strong>人均产值、人均收入等效率指标</li>
            </ul>
            
            <div class="highlight-box">
                <strong>💡 管理价值：</strong>
                <ul>
                    <li>为管理层提供月度经营决策依据</li>
                    <li>监控企业经营健康状况</li>
                    <li>识别经营趋势和异常情况</li>
                    <li>支持年度经营计划制定</li>
                </ul>
            </div>
        </div>

        <h2>🗄️ 数据来源分析</h2>

        <div class="analysis-card data-source">
            <h3>核心数据表</h3>
            
            <h4><span class="tag tag-query">收入</span>tuqoa_htgl - 合同管理表</h4>
            <div class="code-snippet">
-- 计算总服务费
SELECT sum(fwf) fwfhj FROM `tuqoa_htgl` 
WHERE `qdsj`>='$startDate' and `qdsj`<='$endDate'

-- 月度服务费统计
SELECT IFNULL(SUM(fwf), 0) AS fwfhj FROM `tuqoa_htgl` 
WHERE DATE_FORMAT(qdsj, '%Y-%m') = '$monthStr'
            </div>
            <p><strong>用途：</strong>统计合同签订金额，反映企业收入规模</p>
            
            <h4><span class="tag tag-query">产值</span>tuqoa_xmcztjb - 项目成本统计表</h4>
            <div class="code-snippet">
-- 计算总产值
SELECT IFNULL(SUM(wccz), 0) hj FROM `tuqoa_xmcztjb`  
WHERE `sbrq`>='$startDate' and `sbrq`<='$endDate'

-- 月度产值统计
SELECT IFNULL(SUM(wccz), 0) AS total_wccz FROM tuqoa_xmcztjb 
WHERE DATE_FORMAT(sbrq, '%Y-%m') = '$monthStr'
            </div>
            <p><strong>用途：</strong>统计完成产值，反映企业生产能力</p>
            
            <h4><span class="tag tag-query">收款</span>tuqoa_htsf - 合同收费表</h4>
            <div class="code-snippet">
-- 月度到账额统计
SELECT IFNULL(SUM(ysje), 0) AS ysjehj FROM `tuqoa_htsf` 
WHERE DATE_FORMAT(yjsj, '%Y-%m') = '$monthStr'

-- 收款明细查询
SELECT ifnull(sum(yjje),0) as yjjehj,ifnull(sum(ysje),0) as ysjehj 
FROM `tuqoa_htsf` WHERE `yjsj` like '$month%'
            </div>
            <p><strong>用途：</strong>统计实际收款情况，反映现金流状况</p>
            
            <h4><span class="tag tag-query">人员</span>tuqoa_rydp - 人员配置表</h4>
            <div class="code-snippet">
-- 在职员工统计
SELECT count(*) hj FROM `tuqoa_rydp` 
WHERE `status`=1 and `state`='在职' and drxm<>'工程管理部待岗人员'
            </div>
            <p><strong>用途：</strong>统计在职员工数量，计算人均效率指标</p>
            
            <h4><span class="tag tag-query">工资</span>tuqoa_hrsalary - 人力资源工资表</h4>
            <div class="code-snippet">
-- 月度工资统计
SELECT count(*) yrs,sum(sfgz) sfgzhj FROM `tuqoa_hrsalary` 
WHERE `month`='$month'
            </div>
            <p><strong>用途：</strong>统计人员成本，计算人工费用</p>
        </div>

        <h2>📊 关键指标计算</h2>

        <div class="analysis-card feature">
            <h3>经营指标体系</h3>
            
            <div class="metric-grid">
                <div class="metric-card">
                    <h4>服务费合计</h4>
                    <div class="metric-value">¥X.XX万</div>
                    <p>合同签订总金额</p>
                </div>
                <div class="metric-card">
                    <h4>完成产值</h4>
                    <div class="metric-value">¥X.XX万</div>
                    <p>实际完成产值</p>
                </div>
                <div class="metric-card">
                    <h4>到账金额</h4>
                    <div class="metric-value">¥X.XX万</div>
                    <p>实际收款金额</p>
                </div>
                <div class="metric-card">
                    <h4>在职人员</h4>
                    <div class="metric-value">XX人</div>
                    <p>当前在职员工</p>
                </div>
            </div>
            
            <h4><span class="tag tag-calc">计算</span>人均效率指标</h4>
            <div class="code-snippet">
// 人均产值计算
$avgProductivity = 0;
if ($employeeCount > 0) {
    $avgProductivity = round($totalOutput / $employeeCount, 2);
}

// 人均收入计算  
$avgRevenue = 0;
if ($employeeCount > 0) {
    $avgRevenue = round($totalRevenue / $employeeCount, 2);
}

// 产值收入比
$outputRevenueRatio = 0;
if ($totalRevenue > 0) {
    $outputRevenueRatio = round(($totalOutput / $totalRevenue) * 100, 2);
}
            </div>
        </div>

        <h2>📈 月度趋势分析</h2>

        <div class="analysis-card chart">
            <h3>12个月趋势图表</h3>
            
            <h4><span class="tag tag-trend">趋势</span>月度数据循环处理</h4>
            <div class="code-snippet">
// PHP月度数据处理逻辑
$monthlyData = [];
for ($i = 1; $i <= 12; $i++) {
    $month = str_pad($i, 2, '0', STR_PAD_LEFT);
    $monthStr = date('Y') . '-' . $month;
    
    // 服务费查询
    $fwfSql = "SELECT IFNULL(SUM(fwf), 0) AS fwfhj FROM `tuqoa_htgl` 
               WHERE DATE_FORMAT(qdsj, '%Y-%m') = '$monthStr'";
    
    // 到账额查询
    $ysjeSql = "SELECT IFNULL(SUM(ysje), 0) AS ysjehj FROM `tuqoa_htsf` 
                WHERE DATE_FORMAT(yjsj, '%Y-%m') = '$monthStr'";
    
    // 产值查询
    $wcczSql = "SELECT IFNULL(SUM(wccz), 0) AS total_wccz FROM tuqoa_xmcztjb 
                WHERE DATE_FORMAT(sbrq, '%Y-%m') = '$monthStr'";
    
    $monthlyData[$i] = [
        'month' => $month,
        'revenue' => $monthlyFwf,
        'income' => $monthlyYsje,
        'output' => $monthlyWccz
    ];
}
            </div>
            
            <h4><span class="tag tag-chart">图表</span>多维度趋势图</h4>
            <div class="code-snippet">
// Chart.js多线图实现
var ctx = document.getElementById('trendChart').getContext('2d');
var trendChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: ['1月', '2月', '3月', '4月', '5月', '6月', 
                '7月', '8月', '9月', '10月', '11月', '12月'],
        datasets: [{
            label: '服务费',
            data: revenueData,
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1
        }, {
            label: '到账额',
            data: incomeData,
            borderColor: 'rgb(54, 162, 235)',
            backgroundColor: 'rgba(54, 162, 235, 0.2)',
            tension: 0.1
        }, {
            label: '完成产值',
            data: outputData,
            borderColor: 'rgb(255, 99, 132)',
            backgroundColor: 'rgba(255, 99, 132, 0.2)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        interaction: {
            mode: 'index',
            intersect: false,
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return '¥' + (value/10000).toFixed(1) + '万';
                    }
                }
            }
        },
        plugins: {
            tooltip: {
                callbacks: {
                    label: function(context) {
                        return context.dataset.label + ': ¥' + 
                               (context.parsed.y/10000).toFixed(2) + '万';
                    }
                }
            }
        }
    }
});
            </div>
        </div>

        <h2>💼 业务分析逻辑</h2>

        <div class="analysis-card business">
            <h3>经营健康度评估</h3>
            
            <table class="table">
                <thead>
                    <tr>
                        <th>评估维度</th>
                        <th>计算方法</th>
                        <th>健康标准</th>
                        <th>预警阈值</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>收款及时性</td>
                        <td>到账额/服务费</td>
                        <td>&gt; 80%</td>
                        <td>&lt; 60%</td>
                    </tr>
                    <tr>
                        <td>产值效率</td>
                        <td>完成产值/服务费</td>
                        <td>&gt; 90%</td>
                        <td>&lt; 70%</td>
                    </tr>
                    <tr>
                        <td>人均产值</td>
                        <td>总产值/在职人数</td>
                        <td>&gt; 5万/月</td>
                        <td>&lt; 3万/月</td>
                    </tr>
                    <tr>
                        <td>增长趋势</td>
                        <td>当月/上月对比</td>
                        <td>&gt; 105%</td>
                        <td>&lt; 95%</td>
                    </tr>
                </tbody>
            </table>
            
            <h4>经营状况判断逻辑</h4>
            <div class="code-snippet">
// 经营健康度评分
function calculateHealthScore($metrics) {
    $score = 0;
    
    // 收款及时性评分 (30%)
    $collectionRate = $metrics['income'] / $metrics['revenue'];
    if ($collectionRate >= 0.8) $score += 30;
    elseif ($collectionRate >= 0.6) $score += 20;
    else $score += 10;
    
    // 产值效率评分 (25%)
    $outputRate = $metrics['output'] / $metrics['revenue'];
    if ($outputRate >= 0.9) $score += 25;
    elseif ($outputRate >= 0.7) $score += 15;
    else $score += 5;
    
    // 人均产值评分 (25%)
    $avgOutput = $metrics['output'] / $metrics['employees'];
    if ($avgOutput >= 50000) $score += 25;
    elseif ($avgOutput >= 30000) $score += 15;
    else $score += 5;
    
    // 增长趋势评分 (20%)
    $growthRate = $metrics['currentMonth'] / $metrics['lastMonth'];
    if ($growthRate >= 1.05) $score += 20;
    elseif ($growthRate >= 0.95) $score += 15;
    else $score += 5;
    
    return $score;
}

// 健康等级判断
if ($score >= 80) $level = "优秀";
elseif ($score >= 60) $level = "良好";
elseif ($score >= 40) $level = "一般";
else $level = "需改进";
            </div>
        </div>

        <h2>🔍 数据质量控制</h2>

        <div class="analysis-card feature">
            <h3>数据验证机制</h3>
            
            <h4><span class="tag tag-query">验证</span>数据完整性检查</h4>
            <div class="code-snippet">
// 数据完整性验证
function validateMonthlyData($month) {
    $issues = [];
    
    // 检查是否有服务费数据
    $revenueCount = getRecordCount('tuqoa_htgl', $month);
    if ($revenueCount == 0) {
        $issues[] = "缺少{$month}月服务费数据";
    }
    
    // 检查是否有产值数据
    $outputCount = getRecordCount('tuqoa_xmcztjb', $month);
    if ($outputCount == 0) {
        $issues[] = "缺少{$month}月产值数据";
    }
    
    // 检查数据逻辑性
    if ($income > $revenue * 1.2) {
        $issues[] = "收款金额异常：超过服务费20%";
    }
    
    return $issues;
}

// 异常数据标记
function flagAnomalies($currentData, $historicalData) {
    $anomalies = [];
    
    // 环比异常检查
    $changeRate = ($currentData - $historicalData) / $historicalData;
    if (abs($changeRate) > 0.5) {
        $anomalies[] = "数据波动异常：环比变化" . round($changeRate*100, 1) . "%";
    }
    
    return $anomalies;
}
            </div>
        </div>

        <h2>🎯 技术特点</h2>

        <div class="analysis-card">
            <h3>代码架构特色</h3>
            
            <h4>数据处理优势</h4>
            <ul>
                <li><strong>时间维度分析：</strong>支持年度、月度多维度统计</li>
                <li><strong>聚合计算：</strong>使用SUM、COUNT等聚合函数</li>
                <li><strong>空值处理：</strong>使用IFNULL确保数据完整性</li>
                <li><strong>日期格式化：</strong>灵活的时间筛选和分组</li>
            </ul>
            
            <h4>可视化特色</h4>
            <ul>
                <li><strong>多维图表：</strong>支持线图、柱图、饼图等</li>
                <li><strong>交互体验：</strong>图表悬停、缩放等交互</li>
                <li><strong>响应式设计：</strong>适配不同屏幕尺寸</li>
                <li><strong>数据导出：</strong>支持图表和数据导出</li>
            </ul>
            
            <div class="warning-box">
                <strong>⚠️ 注意事项：</strong>
                <ul>
                    <li>大数据量查询可能影响性能，建议添加分页</li>
                    <li>月度统计依赖DATE_FORMAT函数，注意索引优化</li>
                    <li>跨年度查询需要特殊处理</li>
                    <li>数据更新频率影响分析准确性</li>
                </ul>
            </div>
        </div>

        <h2>🚀 优化建议</h2>

        <div class="analysis-card">
            <h3>性能优化</h3>
            <ul>
                <li>为日期字段添加索引：qdsj, sbrq, yjsj</li>
                <li>创建月度汇总表，避免重复计算</li>
                <li>实现数据缓存机制，提高查询速度</li>
                <li>优化SQL查询，减少全表扫描</li>
            </ul>
            
            <h3>功能增强</h3>
            <ul>
                <li>添加同比、环比分析功能</li>
                <li>支持自定义时间范围分析</li>
                <li>增加预测分析和趋势预警</li>
                <li>添加部门维度的经营分析</li>
            </ul>
            
            <h3>用户体验</h3>
            <ul>
                <li>添加数据钻取功能</li>
                <li>支持报表自动生成和推送</li>
                <li>增加移动端适配</li>
                <li>添加数据异常提醒功能</li>
            </ul>
        </div>

        <div style="text-align: center; margin-top: 40px; color: #7f8c8d;">
            <p>📅 分析日期：2025年8月7日</p>
            <p>📊 页面重要性：⭐⭐⭐⭐⭐ (经营决策核心)</p>
            <p>🔄 建议更新频率：月度</p>
        </div>
    </div>
</body>
</html>
