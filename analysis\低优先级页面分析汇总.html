<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>低优先级页面分析汇总</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }
        h3 {
            color: #2980b9;
            margin-top: 20px;
        }
        .page-analysis {
            background: #f8f9fa;
            padding: 20px;
            margin: 15px 0;
            border-radius: 8px;
            border-left: 4px solid #6c757d;
        }
        .data-source {
            background: #e9ecef;
            padding: 10px;
            margin: 8px 0;
            border-radius: 5px;
        }
        .code-snippet {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 8px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            overflow-x: auto;
        }
        .tag {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 11px;
            font-weight: bold;
            margin-right: 3px;
        }
        .tag-dynamic { background: #17a2b8; color: white; }
        .tag-table { background: #28a745; color: white; }
        .tag-chart { background: #dc3545; color: white; }
        .tag-complex { background: #6f42c1; color: white; }
        .summary-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .summary-table th, .summary-table td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: left;
        }
        .summary-table th {
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>低优先级页面分析汇总</h1>
        
        <h2>页面概览</h2>
        <table class="summary-table">
            <thead>
                <tr>
                    <th>页面文件</th>
                    <th>功能描述</th>
                    <th>主要数据源</th>
                    <th>复杂度</th>
                    <th>分析状态</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>ydjysjfx.php</td>
                    <td>月度经营数据分析</td>
                    <td>tuqoa_htgl, tuqoa_xmcztjb, tuqoa_htsf等</td>
                    <td>高</td>
                    <td>✅ 分析完成</td>
                </tr>
                <tr>
                    <td>xmcbkzhzb.php</td>
                    <td>项目成本控制汇总表</td>
                    <td>tuqoa_gcproject, tuqoa_xmcztjb等</td>
                    <td>高</td>
                    <td>✅ 分析完成</td>
                </tr>
                <tr>
                    <td>ydjysjfxmx.php</td>
                    <td>月度经营数据分析明细</td>
                    <td>预计与ydjysjfx.php类似</td>
                    <td>中</td>
                    <td>🔄 快速分析</td>
                </tr>
                <tr>
                    <td>其他辅助页面</td>
                    <td>各种管理工具和明细页面</td>
                    <td>相关业务表</td>
                    <td>低-中</td>
                    <td>🔄 快速分析</td>
                </tr>
            </tbody>
        </table>

        <h2>重点页面详细分析</h2>

        <div class="page-analysis">
            <h3>ydjysjfx.php - 月度经营数据分析</h3>
            
            <h4>页面功能</h4>
            <p>提供月度经营数据的深度分析，包括服务费、产值、员工、收款等多维度的月度对比和趋势分析。</p>
            
            <h4>主要数据源</h4>
            <div class="data-source">
                <h5><span class="tag tag-dynamic">动态</span>基础统计数据</h5>
                <div class="code-snippet">
// 服务费合计
SELECT sum(fwf) fwfhj FROM `tuqoa_htgl` 
WHERE `qdsj`>='$startDate' and `qdsj`<='$endDate'

// 总产值
SELECT IFNULL(SUM(wccz), 0) hj FROM `tuqoa_xmcztjb`  
WHERE `sbrq`>='$startDate' and `sbrq`<='$endDate'

// 员工人数
SELECT count(*) hj FROM `tuqoa_rydp` 
WHERE `status`=1 and `state`='在职' and drxm<>'工程管理部待岗人员'
                </div>
            </div>
            
            <div class="data-source">
                <h5><span class="tag tag-chart">图表</span>月度趋势数据</h5>
                <div class="code-snippet">
// 月度服务费
SELECT IFNULL(SUM(fwf), 0) AS fwfhj FROM `tuqoa_htgl` 
WHERE DATE_FORMAT(qdsj, '%Y-%m') = '$monthStr'

// 月度到账额
SELECT IFNULL(SUM(ysje), 0) AS ysjehj FROM `tuqoa_htsf` 
WHERE DATE_FORMAT(yjsj, '%Y-%m') = '$monthStr'

// 月度产值
SELECT IFNULL(SUM(wccz), 0) AS total_wccz FROM tuqoa_xmcztjb 
WHERE DATE_FORMAT(sbrq, '%Y-%m') = '$monthStr'
                </div>
                <p>生成最近12个月的趋势对比图</p>
            </div>
            
            <div class="data-source">
                <h5><span class="tag tag-table">表格</span>月度明细数据</h5>
                <div class="code-snippet">
// 收款数据
SELECT ifnull(sum(yjje),0) as yjjehj,ifnull(sum(ysje),0) as ysjehj 
FROM `tuqoa_htsf` WHERE `yjsj` like '$month%'

// 工资数据
SELECT count(*) yrs,sum(sfgz) sfgzhj FROM `tuqoa_hrsalary` 
WHERE `month`='$month'

// 成本数据
SELECT * FROM `tuqoa_xmhstjzl` WHERE `sbrq` like '$month%'

// 产值数据
SELECT IFNULL(SUM(wccz), 0) hj FROM `tuqoa_xmcztjb` 
WHERE `sbrq` like '$month%'
                </div>
                <p>提供每月的详细经营数据明细表</p>
            </div>
            
            <h4>特点</h4>
            <ul>
                <li>多维度月度数据分析</li>
                <li>12个月趋势对比</li>
                <li>涉及7个主要数据表</li>
                <li>包含财务、人力、成本等全面数据</li>
                <li>提供明细表格和趋势图表</li>
            </ul>
        </div>

        <div class="page-analysis">
            <h3>xmcbkzhzb.php - 项目成本控制汇总表</h3>
            
            <h4>页面功能</h4>
            <p>项目成本控制的综合分析页面，提供项目级别的成本明细、人员配置、收款情况等全面的成本控制信息。</p>
            
            <h4>主要数据源</h4>
            <div class="data-source">
                <h5><span class="tag tag-dynamic">动态</span>项目基础数据</h5>
                <div class="code-snippet">
SELECT * FROM `tuqoa_gcproject` 
WHERE xmzt in ('新开工项目','在建项目','完工未结算') 
order by id desc
                </div>
                <p>获取所有活跃项目列表</p>
            </div>
            
            <div class="data-source">
                <h5><span class="tag tag-complex">复杂</span>成本明细计算</h5>
                <div class="code-snippet">
// 月度成本
SELECT * FROM `tuqoa_xmcztjb` 
where projectid=$projectId and `sbrq` like '$selectedMonth%'

// 年度成本
select IFNULL(SUM(wccz), 0) as wccznd from tuqoa_xmcztjb 
where projectid=$projectId and sbrq like '$year%'

// 累计成本
select IFNULL(sum(wccz),0) as wcczlj from tuqoa_xmcztjb 
where projectid=$projectId
                </div>
            </div>
            
            <div class="data-source">
                <h5><span class="tag tag-complex">复杂</span>人员成本计算</h5>
                <div class="code-snippet">
// 项目人员
SELECT * FROM `tuqoa_rydp` 
WHERE `drxmid`=$projectId and `sfqz`='全职' and `state`='在职'

// 人员工资
SELECT * FROM `tuqoa_hrsalary` 
WHERE `uname`='$employeeName' and `month`='$selectedMonth'

// 社保费用
SELECT ifnull(sum(sjje),0) as hj FROM `tuqoa_xmsjbxmx` 
WHERE `projectid`=$projectId and `ys` like '$selectedMonth%'
                </div>
            </div>
            
            <div class="data-source">
                <h5><span class="tag tag-dynamic">动态</span>收款数据</h5>
                <div class="code-snippet">
// 预计收款
SELECT ifnull(sum(yjje),0) as yjjehj FROM `tuqoa_htsf`  
WHERE projectid=$projectId and `yjsj` like '$selectedMonth%'

// 实际收款
SELECT ifnull(sum(ysje),0) as ysjehj FROM `tuqoa_htsf`  
WHERE projectid=$projectId and `sksj` like '$selectedMonth%'
                </div>
            </div>
            
            <h4>特点</h4>
            <ul>
                <li>项目级别的成本控制</li>
                <li>多维度成本分析（月度、年度、累计）</li>
                <li>人员成本详细计算</li>
                <li>收款与成本对比分析</li>
                <li>涉及8个主要数据表</li>
                <li>复杂的多表关联查询</li>
            </ul>
        </div>

        <h2>数据表使用分析</h2>
        
        <table class="summary-table">
            <thead>
                <tr>
                    <th>数据表</th>
                    <th>主要用途</th>
                    <th>使用页面</th>
                    <th>查询复杂度</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>tuqoa_htgl</td>
                    <td>合同服务费数据</td>
                    <td>ydjysjfx.php</td>
                    <td>中</td>
                </tr>
                <tr>
                    <td>tuqoa_xmcztjb</td>
                    <td>项目成本数据</td>
                    <td>ydjysjfx.php, xmcbkzhzb.php</td>
                    <td>高</td>
                </tr>
                <tr>
                    <td>tuqoa_htsf</td>
                    <td>收费数据</td>
                    <td>ydjysjfx.php, xmcbkzhzb.php</td>
                    <td>中</td>
                </tr>
                <tr>
                    <td>tuqoa_rydp</td>
                    <td>人员配置</td>
                    <td>ydjysjfx.php, xmcbkzhzb.php</td>
                    <td>中</td>
                </tr>
                <tr>
                    <td>tuqoa_hrsalary</td>
                    <td>工资数据</td>
                    <td>ydjysjfx.php, xmcbkzhzb.php</td>
                    <td>高</td>
                </tr>
                <tr>
                    <td>tuqoa_xmhstjzl</td>
                    <td>项目核算统计</td>
                    <td>ydjysjfx.php, xmcbkzhzb.php</td>
                    <td>中</td>
                </tr>
                <tr>
                    <td>tuqoa_xmsjbxmx</td>
                    <td>项目社保明细</td>
                    <td>xmcbkzhzb.php</td>
                    <td>中</td>
                </tr>
                <tr>
                    <td>tuqoa_gcproject</td>
                    <td>项目基础信息</td>
                    <td>xmcbkzhzb.php</td>
                    <td>低</td>
                </tr>
            </tbody>
        </table>

        <h2>技术特点总结</h2>
        
        <div class="data-source">
            <h4>查询特点</h4>
            <ul>
                <li><strong>时间维度：</strong>大量使用DATE_FORMAT和LIKE进行月度筛选</li>
                <li><strong>聚合计算：</strong>广泛使用SUM、COUNT等聚合函数</li>
                <li><strong>多表关联：</strong>通过projectid、uname等字段关联</li>
                <li><strong>空值处理：</strong>使用IFNULL确保数据完整性</li>
                <li><strong>条件筛选：</strong>复杂的WHERE条件组合</li>
            </ul>
        </div>
        
        <div class="data-source">
            <h4>业务逻辑</h4>
            <ul>
                <li><strong>成本控制：</strong>多维度成本分析和控制</li>
                <li><strong>经营分析：</strong>月度经营数据的深度分析</li>
                <li><strong>人员管理：</strong>人员配置和工资成本计算</li>
                <li><strong>财务监控：</strong>收款与成本的对比分析</li>
            </ul>
        </div>

        <h2>其他低优先级页面预测</h2>
        
        <div class="data-source">
            <h4>预计功能和数据源</h4>
            <ul>
                <li><strong>ydjysjfxmx.php：</strong>月度经营数据分析明细，预计是ydjysjfx.php的详细版本</li>
                <li><strong>rqxz.php：</strong>日期选择工具页面，可能包含系统配置</li>
                <li><strong>ygdt.php：</strong>员工动态，基于tuqoa_userinfo表</li>
                <li><strong>zzzsdt.php：</strong>组织制度动态，可能涉及制度管理表</li>
                <li><strong>wtgz.php：</strong>委托工作，可能涉及工作委托管理表</li>
            </ul>
        </div>

        <h2>总结</h2>
        <p>低优先级页面主要特点：</p>
        <ul>
            <li><strong>专业性强：</strong>针对特定业务需求的深度分析</li>
            <li><strong>数据复杂：</strong>涉及多表关联和复杂计算</li>
            <li><strong>功能完整：</strong>提供从统计到明细的完整数据视图</li>
            <li><strong>实用价值高：</strong>为管理决策提供重要数据支持</li>
        </ul>
        <p>这些页面虽然使用频率可能较低，但在特定业务场景下具有重要价值，特别是在成本控制和经营分析方面。</p>
    </div>
</body>
</html>
