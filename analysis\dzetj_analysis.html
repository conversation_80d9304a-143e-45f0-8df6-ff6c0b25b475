<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>dzetj.php 数据来源分析</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }
        h3 {
            color: #2980b9;
            margin-top: 20px;
        }
        .data-source {
            background: #ecf0f1;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #3498db;
        }
        .static-data {
            background: #fff3cd;
            border-left-color: #ffc107;
        }
        .dynamic-data {
            background: #d1ecf1;
            border-left-color: #17a2b8;
        }
        .table-info {
            background: #d4edda;
            border-left-color: #28a745;
        }
        .chart-info {
            background: #f8d7da;
            border-left-color: #dc3545;
        }
        .code-snippet {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        .tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            margin-right: 5px;
        }
        .tag-static { background: #ffc107; color: #212529; }
        .tag-dynamic { background: #17a2b8; color: white; }
        .tag-table { background: #28a745; color: white; }
        .tag-chart { background: #dc3545; color: white; }
        .tag-form { background: #fd7e14; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <h1>dzetj.php 页面数据来源分析</h1>
        
        <h2>页面概述</h2>
        <p>dzetj.php 是一个到账额统计页面，用于展示和分析公司的收款情况，包括本月到账额、回款率、待回款金额、年度累计等关键财务指标，并提供趋势图表和明细数据。</p>
        
        <h2>数据来源分析</h2>
        
        <h3>1. 用户输入数据</h3>
        
        <div class="data-source static-data">
            <h4><span class="tag tag-form">表单</span>日期范围选择</h4>
            <p><strong>数据内容：</strong>查询的开始日期和结束日期</p>
            <p><strong>来源：</strong>用户通过表单输入</p>
            <div class="code-snippet">
$startDate = isset($_POST['start-date']) ? $_POST['start-date'] : $firstDayOfMonth;
$endDate = isset($_POST['end-date']) ? $_POST['end-date'] : $lastDayOfMonth;
            </div>
            <p><strong>默认值：</strong>当月第一天到最后一天</p>
        </div>
        
        <h3>2. 动态数据（数据库查询）</h3>
        
        <div class="data-source dynamic-data">
            <h4><span class="tag tag-dynamic">动态</span><span class="tag tag-table">tuqoa_htsf</span>本月到账额</h4>
            <p><strong>数据内容：</strong>指定时间范围内已收款的金额总和</p>
            <p><strong>来源：</strong>tuqoa_htsf（合同收费表）</p>
            <div class="code-snippet">
SELECT COALESCE(SUM(`ysje`), 0) as hj FROM `tuqoa_htsf` 
WHERE `sfjs`='是' and `sksj`>='$startDate' and `sksj`<='$endDate'
            </div>
            <p><strong>说明：</strong>统计已结算（sfjs='是'）的收款金额（ysje）</p>
        </div>
        
        <div class="data-source dynamic-data">
            <h4><span class="tag tag-dynamic">动态</span><span class="tag tag-table">tuqoa_htsf</span>回款率计算</h4>
            <p><strong>数据内容：</strong>已收金额占预计金额的百分比</p>
            <p><strong>来源：</strong>tuqoa_htsf表</p>
            <div class="code-snippet">
SELECT SUM(COALESCE(yjje, 0)) AS 预计金额总和, 
       SUM(COALESCE(ysje, 0)) AS 已收金额总和, 
       CASE WHEN SUM(COALESCE(yjje, 0)) = 0 THEN 0 
            ELSE ROUND(SUM(COALESCE(ysje, 0)) / SUM(COALESCE(yjje, 0)) * 100, 2) 
       END AS hkbfb 
FROM tuqoa_htsf 
WHERE `sksj`>='$startDate' and `sksj`<='$endDate'
            </div>
            <p><strong>说明：</strong>计算回款完成率，避免除零错误</p>
        </div>
        
        <div class="data-source dynamic-data">
            <h4><span class="tag tag-dynamic">动态</span><span class="tag tag-table">tuqoa_htsf</span>待回款金额</h4>
            <p><strong>数据内容：</strong>未结算的预计金额总和</p>
            <p><strong>来源：</strong>tuqoa_htsf表</p>
            <div class="code-snippet">
SELECT COALESCE(SUM(`yjje`), 0) as hj FROM `tuqoa_htsf` 
WHERE `sfjs`<>'是' and `sksj`>='$startDate' and `sksj`<='$endDate'
            </div>
            <p><strong>说明：</strong>统计未结算（sfjs<>'是'）的预计金额</p>
        </div>
        
        <div class="data-source dynamic-data">
            <h4><span class="tag tag-dynamic">动态</span><span class="tag tag-table">tuqoa_htsf</span>年度累计到账</h4>
            <p><strong>数据内容：</strong>当年已收款金额总和</p>
            <p><strong>来源：</strong>tuqoa_htsf表</p>
            <div class="code-snippet">
SELECT SUM(ysje) AS ndhj FROM tuqoa_htsf 
WHERE YEAR(sksj) = YEAR(CURRENT_DATE) and `sfjs`='是'
            </div>
            <p><strong>说明：</strong>统计当年所有已结算的收款金额</p>
        </div>
        
        <h3>3. 图表数据</h3>
        
        <div class="data-source chart-info">
            <h4><span class="tag tag-chart">图表</span>到账额趋势图（最近6个月）</h4>
            <p><strong>数据内容：</strong>最近6个月的预计金额和已收金额</p>
            <p><strong>来源：</strong>tuqoa_htsf表，按月份分组统计</p>
            <div class="code-snippet">
// 预计金额查询
SELECT COALESCE(SUM(yjje), 0) as yjhj FROM tuqoa_htsf 
WHERE DATE_FORMAT(sksj, '%Y-%m') = '$month'

// 已收金额查询
SELECT COALESCE(SUM(ysje), 0) as yshj FROM tuqoa_htsf 
WHERE DATE_FORMAT(sksj, '%Y-%m') = '$month' AND sfjs='是'
            </div>
            <p><strong>说明：</strong>使用循环生成最近6个月的数据，创建折线图</p>
        </div>
        
        <div class="data-source chart-info">
            <h4><span class="tag tag-chart">图表</span>按项目分布饼图</h4>
            <p><strong>数据内容：</strong>各项目的收款金额分布（前10名）</p>
            <p><strong>来源：</strong>tuqoa_htsf表，按项目分组</p>
            <div class="code-snippet">
SELECT htmc AS project_name, SUM(ysje) AS total_amount
FROM tuqoa_htsf
WHERE sfjs='是' AND sksj>='$startDate' AND sksj<='$endDate'
GROUP BY htmc
ORDER BY total_amount DESC
LIMIT 10
            </div>
            <p><strong>说明：</strong>按合同名称分组，统计收款金额，取前10名</p>
        </div>
        
        <h3>4. 表格数据</h3>
        
        <div class="data-source table-info">
            <h4><span class="tag tag-table">表格</span>到账明细表</h4>
            <p><strong>数据内容：</strong>已结算项目的详细收款信息</p>
            <p><strong>来源：</strong>tuqoa_htsf表</p>
            <div class="code-snippet">
SELECT * FROM `tuqoa_htsf` 
WHERE `sfjs`='是' and `sksj`>='$startDate' and `sksj`<='$endDate'
            </div>
            <p><strong>显示字段：</strong></p>
            <ul>
                <li>项目名称（htmc）</li>
                <li>预计金额（yjje）</li>
                <li>到账金额（ysje）</li>
                <li>差额（计算字段：yjje - ysje）</li>
            </ul>
        </div>
        
        <h2>数据表结构</h2>
        
        <div class="table-info">
            <h4>主要数据表：tuqoa_htsf（合同收费表）</h4>
            <ul>
                <li><strong>htmc：</strong>合同名称/项目名称</li>
                <li><strong>yjje：</strong>预计金额</li>
                <li><strong>ysje：</strong>已收金额</li>
                <li><strong>sfjs：</strong>是否结算（'是'/'否'）</li>
                <li><strong>sksj：</strong>收款时间</li>
            </ul>
        </div>
        
        <h2>页面功能特点</h2>
        
        <h3>1. 统计卡片</h3>
        <ul>
            <li><strong>本月到账额：</strong>显示指定时间范围内的收款总额</li>
            <li><strong>本月回款率：</strong>已收金额占预计金额的百分比</li>
            <li><strong>待回款金额：</strong>未结算的预计金额</li>
            <li><strong>年度累计到账：</strong>当年总收款金额</li>
        </ul>
        
        <h3>2. 图表展示</h3>
        <ul>
            <li><strong>到账额趋势图：</strong>折线图显示最近6个月的预计金额和已收金额趋势</li>
            <li><strong>按项目分布图：</strong>饼图显示各项目收款金额的分布情况</li>
        </ul>
        
        <h3>3. 明细表格</h3>
        <ul>
            <li>显示所有已结算项目的详细信息</li>
            <li>包含预计金额、实际到账金额和差额</li>
        </ul>
        
        <h3>4. 交互功能</h3>
        <ul>
            <li>日期范围选择器</li>
            <li>数据查询和刷新</li>
            <li>图表交互（悬停显示详情）</li>
        </ul>
        
        <h2>计算逻辑</h2>
        
        <div class="data-source table-info">
            <h4>关键计算公式</h4>
            <ul>
                <li><strong>回款率：</strong>已收金额总和 ÷ 预计金额总和 × 100%</li>
                <li><strong>差额：</strong>预计金额 - 已收金额</li>
                <li><strong>月份标签：</strong>使用date('m', strtotime("-$i months"))生成</li>
                <li><strong>饼图百分比：</strong>(单项金额 ÷ 总金额) × 100%</li>
            </ul>
        </div>
        
        <h2>总结</h2>
        <p>dzetj.php页面主要用于财务收款统计分析，数据来源包括：</p>
        <ul>
            <li><strong>用户输入：</strong>查询日期范围</li>
            <li><strong>动态数据：</strong>从tuqoa_htsf表查询的收款相关数据</li>
            <li><strong>图表数据：</strong>基于数据库查询生成的趋势图和分布图</li>
            <li><strong>计算数据：</strong>回款率、差额等计算字段</li>
        </ul>
        <p>该页面提供了全面的收款情况分析，包括统计卡片、趋势图表和详细明细，帮助管理层了解公司的财务收款状况。</p>
    </div>
</body>
</html>
