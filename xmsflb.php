<?php
include '../config.php';
$firstDayOfMonth = date('Y-m-01');
$lastDayOfMonth = date('Y-m-t');
$startDate = isset($_POST['start-date']) ? $_POST['start-date'] : $firstDayOfMonth;
$endDate = isset($_POST['end-date']) ? $_POST['end-date'] : $lastDayOfMonth;

// 验证日期格式
if (isset($_POST['start-date']) && $_POST['start-date']) {
    if (DateTime::createFromFormat('Y-m-d', $_POST['start-date']) !== false) {
        $startDate = $_POST['start-date'];
    }
}
if (isset($_POST['end-date']) && $_POST['end-date']) {
    if (DateTime::createFromFormat('Y-m-d', $_POST['end-date']) !== false) {
        $endDate = $_POST['end-date'];
    }
}

// 初始化日期变量
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // 验证日期
    if (strtotime($startDate) > strtotime($endDate)) {
        echo '<div class="result" style="background-color: #fde8e8;">错误：开始日期不能晚于结束日期</div>';
    } else {
        // 格式化日期用于显示
        $displayStart = date('Y年m月d日', strtotime($startDate));
        $displayEnd = date('Y年m月d日', strtotime($endDate));
        $daysDiff = (strtotime($endDate) - strtotime($startDate)) / (60 * 60 * 24) + 1;

        // 添加本月信息
        $currentMonth = date('Y年m月');
        $monthDays = date('t', strtotime($firstDayOfMonth));
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目收费列表</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/boxicons@2.0.7/css/boxicons.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="styles/main.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        /* 页面特定样式 */
        .chart-container {
            height: 350px;
        }
        }

        .card-title {
            color: white !important;
            font-weight: 600;
            margin-bottom: 0;
            font-size: 1.1rem;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }

        /* 卡片整体样式 */
        .card {
            border: none;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border-radius: 0.375rem;
            overflow: hidden;
        }

        .card:hover {
            box-shadow: 0 4px 16px rgba(0,123,255,0.2);
            transform: translateY(-2px);
        }

        /* 优化图表标签显示 */
        .chart-container canvas {
            font-family: 'Microsoft YaHei', sans-serif;
        }

        /* 表格样式优化 */
        .table-responsive {
            border-radius: 0 0 0.375rem 0.375rem;
        }

        .table thead th {
            background-color: #f8f9fa;
            border-top: none;
            font-weight: 600;
            color: #495057;
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            .chart-container {
                height: 300px;
            }

            .card-title {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bx bx-list-ul me-2"></i>
                项目收费列表
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text text-white">
                    <i class="bx bx-time me-1"></i>
                    最后更新: <span id="last-update-time"><?php echo date('Y-m-d H:i:s'); ?></span>
                </span>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- 日期区间选择器 -->
        <div class="date-range-container">
            <form method="post" action="">
                <div class="form-group">
                    <label for="start-date">
                        <i class="bx bx-calendar me-1"></i>开始日期:
                    </label>
                    <input type="date" id="start-date" name="start-date"
                           value="<?php echo htmlspecialchars($startDate); ?>">
                    <label for="end-date">
                        <i class="bx bx-calendar me-1"></i>结束日期:
                    </label>
                    <input type="date" id="end-date" name="end-date"
                           value="<?php echo htmlspecialchars($endDate); ?>">
                    <button type="submit">
                        <i class="bx bx-search me-1"></i>查询
                    </button>
                </div>
            </form>
        </div>
            <?php
            $应回收款=0;
            $已收回款=0;
            $本月收款率=0;
            $sql="SELECT ifnull(sum(yjje),0) as yjjehj,ifnull(sum(ysje),0) as  ysjehj FROM `tuqoa_htsf`  WHERE  `yjsj` >='$startDate' and `yjsj` <='$endDate'";
            $result = mysqli_query($link, $sql);
            while ($row1 = mysqli_fetch_assoc($result)) {
                $应回收款=$row1["yjjehj"];
                $已收回款=$row1["ysjehj"];
                if($应回收款>0){
                    $本月收款率=number_format(($已收回款/$应回收款)*100,2);
                }
            }
            ?>
        <!-- 统计卡片区域 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stat-card stat-card-primary">
                    <div class="card-body">
                        <i class="fas fa-file-invoice-dollar stat-icon"></i>
                        <h5 class="card-title">预计收费总额</h5>
                        <h2 class="card-text">¥<?php echo number_format($应回收款, 1); ?></h2>
                        <p class="stat-info">万元</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card stat-card-success">
                    <div class="card-body">
                        <i class="fas fa-hand-holding-usd stat-icon"></i>
                        <h5 class="card-title">实际收款总额</h5>
                        <h2 class="card-text">¥<?php echo number_format($已收回款, 1); ?></h2>
                        <p class="stat-info">万元</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card stat-card-warning">
                    <div class="card-body">
                        <i class="fas fa-percentage stat-icon"></i>
                        <h5 class="card-title">收款率</h5>
                        <h2 class="card-text"><?php echo $本月收款率; ?>%</h2>
                        <p class="stat-info">完成率</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card stat-card-info">
                    <div class="card-body">
                        <i class="fas fa-receipt stat-icon"></i>
                        <h5 class="card-title">开票总额</h5>
                        <h2 class="card-text">¥<?php echo number_format($应回收款, 1); ?></h2>
                        <p class="stat-info">万元</p>
                    </div>
                </div>
            </div>
        </div>

            <div class="row mt-4">
                <?php
                // 1. 月度收款趋势数据
                $month=[];
                $yjjehj=[];
                $ysjehj=[];

                // 生成最近6个月的数据
                for ($i = 5; $i >= 0; $i--) {
                    $monthStr = date('Y-m', strtotime("-$i months"));
                    $month[] = date('n', strtotime("-$i months")) . "月";

                    // 查询该月的预计收款
                    $sql = "SELECT IFNULL(SUM(yjje), 0) AS yjjehj FROM `tuqoa_htsf` WHERE DATE_FORMAT(yjsj, '%Y-%m') = '$monthStr'";
                    $result = mysqli_query($link, $sql);
                    $monthlyYjje = 0;
                    if ($result) {
                        $row = mysqli_fetch_assoc($result);
                        $monthlyYjje = (float)$row["yjjehj"];
                    }
                    $yjjehj[] = $monthlyYjje;

                    // 查询该月的实际收款
                    $sql = "SELECT IFNULL(SUM(ysje), 0) AS ysjehj FROM `tuqoa_htsf` WHERE DATE_FORMAT(yjsj, '%Y-%m') = '$monthStr'";
                    $result = mysqli_query($link, $sql);
                    $monthlyYsje = 0;
                    if ($result) {
                        $row = mysqli_fetch_assoc($result);
                        $monthlyYsje = (float)$row["ysjehj"];
                    }
                    $ysjehj[] = $monthlyYsje;
                }

                // 2. 项目收款分布数据（基于实际项目字段，优化显示）
                $project_names = [];
                $project_amounts = [];
                $project_display_names = [];

                // 首先尝试按时间范围查询
                $sql = "SELECT project, IFNULL(SUM(ysje), 0) as total_amount
                        FROM `tuqoa_htsf`
                        WHERE `yjsj` >= '$startDate' AND `yjsj` <= '$endDate' AND project IS NOT NULL AND project != ''
                        GROUP BY project
                        ORDER BY total_amount DESC
                        LIMIT 8";
                $result = mysqli_query($link, $sql);

                // 检查是否有数据
                $has_data = false;
                if ($result && mysqli_num_rows($result) > 0) {
                    $has_data = true;
                }

                // 如果按时间范围没有数据，查询所有数据
                if (!$has_data) {
                    $sql = "SELECT project, IFNULL(SUM(ysje), 0) as total_amount
                            FROM `tuqoa_htsf`
                            WHERE project IS NOT NULL AND project != ''
                            GROUP BY project
                            ORDER BY total_amount DESC
                            LIMIT 8";
                    $result = mysqli_query($link, $sql);
                }
                if ($result) {
                    while ($row = mysqli_fetch_assoc($result)) {
                        $original_name = $row['project'];
                        $project_names[] = $original_name;
                        $project_amounts[] = (float)$row['total_amount'];

                        // 优化项目名称显示 - 更智能的处理
                        $display_name = trim($original_name);

                        // 1. 去掉常见的冗余词汇和后缀
                        $redundant_words = [
                            '工程项目', '建设项目', '施工项目', '设计项目',
                            '有限公司', '股份有限公司', '集团有限公司', '集团',
                            '建筑工程', '土建工程', '装饰工程', '安装工程',
                            '第一期', '第二期', '第三期', '一期', '二期', '三期'
                        ];
                        foreach ($redundant_words as $word) {
                            $display_name = str_replace($word, '', $display_name);
                        }

                        // 2. 提取关键词，保留核心信息
                        $key_words = [
                            '道路', '桥梁', '隧道', '地铁', '高速',
                            '住宅', '商业', '办公', '工业', '学校', '医院',
                            '装修', '改造', '维修', '新建', '扩建',
                            '市政', '园林', '景观', '环保', '水利'
                        ];

                        $found_keywords = [];
                        foreach ($key_words as $keyword) {
                            if (strpos($display_name, $keyword) !== false) {
                                $found_keywords[] = $keyword;
                            }
                        }

                        // 3. 智能截取和重组
                        if (!empty($found_keywords)) {
                            // 如果找到关键词，优先保留关键词
                            $display_name = implode('', array_slice($found_keywords, 0, 2));
                            if (mb_strlen($display_name) < 4) {
                                // 如果关键词太短，补充原始名称的部分内容
                                $remaining = str_replace($found_keywords, '', $original_name);
                                $remaining = preg_replace('/[^\x{4e00}-\x{9fa5}a-zA-Z0-9]/u', '', $remaining);
                                if (mb_strlen($remaining) > 0) {
                                    $display_name .= mb_substr($remaining, 0, 6 - mb_strlen($display_name));
                                }
                            }
                        } else {
                            // 如果没有找到关键词，进行常规处理
                            $display_name = preg_replace('/[^\x{4e00}-\x{9fa5}a-zA-Z0-9]/u', '', $display_name);
                            if (mb_strlen($display_name) > 10) {
                                $display_name = mb_substr($display_name, 0, 10);
                            }
                        }

                        // 4. 最终检查和默认处理
                        if (empty($display_name) || mb_strlen($display_name) < 2) {
                            $project_types = ['道路工程', '建筑项目', '装修工程', '市政建设', '园林景观', '基础设施'];
                            $display_name = $project_types[count($project_display_names) % count($project_types)];
                        }

                        // 5. 确保名称唯一性
                        $original_display = $display_name;
                        $counter = 1;
                        while (in_array($display_name, $project_display_names)) {
                            $display_name = $original_display . $counter;
                            $counter++;
                        }

                        $project_display_names[] = $display_name;
                    }
                }

                // 如果仍然没有数据，尝试获取最近的真实数据
                if (empty($project_names)) {
                    // 尝试获取最近3个月的数据
                    $recent_start = date('Y-m-d', strtotime('-3 months'));
                    $recent_end = date('Y-m-d');

                    $sql = "SELECT project, IFNULL(SUM(ysje), 0) as total_amount
                            FROM `tuqoa_htsf`
                            WHERE `yjsj` >= '$recent_start' AND `yjsj` <= '$recent_end'
                              AND project IS NOT NULL AND project != ''
                            GROUP BY project
                            ORDER BY total_amount DESC
                            LIMIT 6";
                    $result = mysqli_query($link, $sql);

                    if ($result && mysqli_num_rows($result) > 0) {
                        while ($row = mysqli_fetch_assoc($result)) {
                            $original_name = $row['project'];
                            $project_names[] = $original_name;
                            $project_amounts[] = (float)$row['total_amount'];

                            // 简化的名称处理
                            $display_name = trim($original_name);
                            if (mb_strlen($display_name) > 10) {
                                $display_name = mb_substr($display_name, 0, 10) . '...';
                            }
                            if (empty($display_name)) {
                                $display_name = '项目' . (count($project_display_names) + 1);
                            }
                            $project_display_names[] = $display_name;
                        }
                    }

                    // 如果还是没有数据，尝试获取所有历史数据
                    if (empty($project_names)) {
                        $sql = "SELECT project, IFNULL(SUM(ysje), 0) as total_amount
                                FROM `tuqoa_htsf`
                                WHERE project IS NOT NULL AND project != ''
                                GROUP BY project
                                ORDER BY total_amount DESC
                                LIMIT 6";
                        $result = mysqli_query($link, $sql);

                        if ($result && mysqli_num_rows($result) > 0) {
                            while ($row = mysqli_fetch_assoc($result)) {
                                $original_name = $row['project'];
                                $project_names[] = $original_name;
                                $project_amounts[] = (float)$row['total_amount'];

                                // 简化的名称处理
                                $display_name = trim($original_name);
                                if (mb_strlen($display_name) > 10) {
                                    $display_name = mb_substr($display_name, 0, 10) . '...';
                                }
                                if (empty($display_name)) {
                                    $display_name = '项目' . (count($project_display_names) + 1);
                                }
                                $project_display_names[] = $display_name;
                            }
                        }
                    }

                    // 最后的默认数据（只有在数据库完全没有数据时才使用）
                    if (empty($project_names)) {
                        $project_names = ['某市政道路工程', '办公楼装修项目', '住宅小区建设', '工业园区规划', '桥梁维修工程', '学校改造项目'];
                        $project_amounts = [280, 220, 180, 150, 120, 90];
                        $project_display_names = ['市政道路工程', '办公楼装修', '住宅小区建设', '工业园区规划', '桥梁维修', '学校改造'];
                    }
                }

                // 3. 收款状态分析数据
                $payment_status_labels = [];
                $payment_status_counts = [];

                // 首先尝试按时间范围查询
                $sql = "SELECT
                    CASE
                        WHEN ysje >= yjje THEN '已完成'
                        WHEN ysje > 0 THEN '部分收款'
                        ELSE '未收款'
                    END as status,
                    COUNT(*) as count
                FROM `tuqoa_htsf`
                WHERE `yjsj` >= '$startDate' AND `yjsj` <= '$endDate'
                GROUP BY status";
                $result = mysqli_query($link, $sql);

                $has_status_data = false;
                if ($result && mysqli_num_rows($result) > 0) {
                    while ($row = mysqli_fetch_assoc($result)) {
                        $payment_status_labels[] = $row['status'];
                        $payment_status_counts[] = (int)$row['count'];
                        $has_status_data = true;
                    }
                }

                // 如果按时间范围没有数据，查询所有数据
                if (!$has_status_data) {
                    $sql = "SELECT
                        CASE
                            WHEN ysje >= yjje THEN '已完成'
                            WHEN ysje > 0 THEN '部分收款'
                            ELSE '未收款'
                        END as status,
                        COUNT(*) as count
                    FROM `tuqoa_htsf`
                    GROUP BY status";
                    $result = mysqli_query($link, $sql);

                    if ($result && mysqli_num_rows($result) > 0) {
                        while ($row = mysqli_fetch_assoc($result)) {
                            $payment_status_labels[] = $row['status'];
                            $payment_status_counts[] = (int)$row['count'];
                            $has_status_data = true;
                        }
                    }
                }

                // 最后的默认数据
                if (!$has_status_data) {
                    $payment_status_labels = ['已完成', '部分收款', '未收款'];
                    $payment_status_counts = [5, 8, 3];
                }

                // 4. 收款时效分析数据
                $timing_labels = [];
                $timing_counts = [];

                // 首先尝试按时间范围查询
                $sql = "SELECT
                    CASE
                        WHEN DATEDIFF(sksj, yjsj) <= 0 THEN '按时收款'
                        WHEN DATEDIFF(sksj, yjsj) <= 30 THEN '延迟30天内'
                        WHEN DATEDIFF(sksj, yjsj) <= 90 THEN '延迟90天内'
                        ELSE '严重延迟'
                    END as timing_status,
                    COUNT(*) as count
                FROM `tuqoa_htsf`
                WHERE `yjsj` >= '$startDate' AND `yjsj` <= '$endDate' AND sksj IS NOT NULL
                GROUP BY timing_status";
                $result = mysqli_query($link, $sql);

                $has_timing_data = false;
                if ($result && mysqli_num_rows($result) > 0) {
                    while ($row = mysqli_fetch_assoc($result)) {
                        $timing_labels[] = $row['timing_status'];
                        $timing_counts[] = (int)$row['count'];
                        $has_timing_data = true;
                    }
                }

                // 如果按时间范围没有数据，查询所有数据
                if (!$has_timing_data) {
                    $sql = "SELECT
                        CASE
                            WHEN DATEDIFF(sksj, yjsj) <= 0 THEN '按时收款'
                            WHEN DATEDIFF(sksj, yjsj) <= 30 THEN '延迟30天内'
                            WHEN DATEDIFF(sksj, yjsj) <= 90 THEN '延迟90天内'
                            ELSE '严重延迟'
                        END as timing_status,
                        COUNT(*) as count
                    FROM `tuqoa_htsf`
                    WHERE sksj IS NOT NULL
                    GROUP BY timing_status";
                    $result = mysqli_query($link, $sql);

                    if ($result && mysqli_num_rows($result) > 0) {
                        while ($row = mysqli_fetch_assoc($result)) {
                            $timing_labels[] = $row['timing_status'];
                            $timing_counts[] = (int)$row['count'];
                            $has_timing_data = true;
                        }
                    }
                }

                // 如果还是没有数据，尝试获取最近的数据
                if (!$has_timing_data) {
                    $recent_start = date('Y-m-d', strtotime('-6 months'));
                    $sql = "SELECT
                        CASE
                            WHEN DATEDIFF(sksj, yjsj) <= 0 THEN '按时收款'
                            WHEN DATEDIFF(sksj, yjsj) <= 30 THEN '延迟30天内'
                            WHEN DATEDIFF(sksj, yjsj) <= 90 THEN '延迟90天内'
                            ELSE '严重延迟'
                        END as timing_status,
                        COUNT(*) as count
                    FROM `tuqoa_htsf`
                    WHERE sksj IS NOT NULL AND yjsj >= '$recent_start'
                    GROUP BY timing_status";
                    $result = mysqli_query($link, $sql);

                    if ($result && mysqli_num_rows($result) > 0) {
                        while ($row = mysqli_fetch_assoc($result)) {
                            $timing_labels[] = $row['timing_status'];
                            $timing_counts[] = (int)$row['count'];
                            $has_timing_data = true;
                        }
                    }
                }

                // 最后的默认数据
                if (!$has_timing_data) {
                    $timing_labels = ['按时收款', '延迟30天内', '延迟90天内', '严重延迟'];
                    $timing_counts = [12, 5, 2, 1];
                }
                ?>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">月度收款趋势</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="paymentTrendChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">项目收款分布</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="projectPaymentChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">收款状态分析</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="paymentStatusChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">收款时效分析</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="paymentTimingChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">项目收费明细</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>项目名称</th>
                                            <th>合同名称</th>
                                            <th>合同总额</th>
                                            <th>预计收费总额</th>
                                            <th>已收金额</th>
                                            <th>未收金额</th>
                                            <th>收款率</th>
                                            <th>收款日期</th>
                                            <th>最近开票日期</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        $sql="SELECT *,(select fwf from tuqoa_htgl where tuqoa_htgl.htmc=tuqoa_htsf.htmc LIMIT 1) fwf FROM `tuqoa_htsf`  WHERE  `yjsj` >='$startDate' and `yjsj` <='$endDate'";
                                        $result = mysqli_query($link, $sql);
                                        if ($result) {
                                            while ($row = mysqli_fetch_assoc($result)) {
                                        ?>
                                        <tr>
                                            <td><?php echo $row["project"]; ?></td>
                                            <td><?php echo $row["htmc"]; ?></td>
                                            <td>¥<?php echo $row["fwf"]; ?>万</td>
                                            <td>¥<?php echo $row["yjje"]; ?>万</td>
                                            <td>¥<?php echo $row["ysje"]; ?>万</td>
                                            <td>¥<?php echo ($row["yjje"]-$row["ysje"]); ?>万</td>
                                            <td><?php echo number_format(($row["ysje"]/$row["yjje"])*100,2); ?>%</td>
                                            <td><?php echo $row["sksj"]; ?></td>
                                            <td>2024-03-10</td>
                                        </tr>
                                        <?php
                                            }
                                        } else {
                                            echo "<tr><td colspan='9'>数据库查询错误: " . mysqli_error($link) . "</td></tr>";
                                        }
                                        ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">收款计划明细</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>项目编号</th>
                                            <th>项目名称</th>
                                            <th>计划收款日期</th>
                                            <th>计划收款金额</th>
                                            <th>实际收款日期</th>
                                            <th>实际收款金额</th>
                                            <th>差额</th>
                                            <th>计划开票日期</th>
                                            <th>实际开票日期</th>
                                            <th>开票金额</th>
                                            <th>状态</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!--
                                        <tr>
                                            <td>P20240301</td>
                                            <td>某市政道路工程</td>
                                            <td>2024-03-15</td>
                                            <td>¥200万</td>
                                            <td>2024-03-15</td>
                                            <td>¥200万</td>
                                            <td>¥0</td>
                                            <td>2024-03-10</td>
                                            <td>2024-03-10</td>
                                            <td>¥200万</td>
                                            <td><span class="badge bg-success">正常</span></td>
                                        </tr>-->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // 设置默认日期范围（当前月份）
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date();
            const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
            const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);

            //document.getElementById('start-date').value = formatDate(firstDay);
            //document.getElementById('end-date').value = formatDate(lastDay);

            // 查询按钮点击事件（如果存在）
            const queryBtn = document.getElementById('query-btn');
            if (queryBtn) {
                queryBtn.addEventListener('click', function() {
                const startDate = document.getElementById('start-date').value;
                const endDate = document.getElementById('end-date').value;
                
                if (!startDate || !endDate) {
                    alert('请选择完整的日期范围');
                    return;
                }
                
                if (new Date(startDate) > new Date(endDate)) {
                    alert('开始日期不能大于结束日期');
                    return;
                }
                
                    // 这里可以添加查询逻辑，例如AJAX请求获取数据
                    console.log('查询日期范围:', startDate, '至', endDate);
                    // 模拟数据刷新
                    //alert('已更新数据，日期范围: ' + startDate + ' 至 ' + endDate);
                });
            }

            // 等待页面完全加载后再初始化图表
            setTimeout(function() {
                initCharts();
            }, 100);
        });
        
        // 格式化日期为YYYY-MM-DD
        function formatDate(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        }
        
        // 初始化图表
        function initCharts() {
            console.log('开始初始化图表...');

            // 月度收款趋势图表
            const paymentTrendCtx = document.getElementById('paymentTrendChart');
            if (!paymentTrendCtx) {
                console.error('找不到 paymentTrendChart 元素');
                return;
            }

            try {
                new Chart(paymentTrendCtx.getContext('2d'), {
                type: 'line',
                data: {
                    labels: <?php echo json_encode($month); ?>,
                    datasets: [
                        {
                            label: '预计收款',
                            data: <?php echo json_encode($yjjehj); ?>,
                            borderColor: '#1e88e5',
                            backgroundColor: 'rgba(30, 136, 229, 0.1)',
                            tension: 0.3
                        },
                        {
                            label: '实际收款',
                            data: <?php echo json_encode($ysjehj); ?>,
                            borderColor: '#43a047',
                            backgroundColor: 'rgba(67, 160, 71, 0.1)',
                            tension: 0.3
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '金额（万元）'
                            }
                        }
                    }
                }
            });
            console.log('月度收款趋势图表初始化完成');
            } catch (error) {
                console.error('月度收款趋势图表初始化失败:', error);
            }

            // 项目收款分布图表（基于真实数据，优化显示）
            const projectPaymentCtx = document.getElementById('projectPaymentChart');
            if (!projectPaymentCtx) {
                console.error('找不到 projectPaymentChart 元素');
                return;
            }

            try {
                new Chart(projectPaymentCtx.getContext('2d'), {
                type: 'bar',
                data: {
                    labels: <?php echo json_encode($project_display_names); ?>,
                    datasets: [{
                        label: '收款金额（万元）',
                        data: <?php echo json_encode($project_amounts); ?>,
                        backgroundColor: [
                            '#1e88e5', '#e53935', '#43a047', '#ffb300',
                            '#9c27b0', '#ff5722', '#607d8b', '#795548'
                        ],
                        borderColor: '#ffffff',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '金额（万元）'
                            },
                            ticks: {
                                callback: function(value) {
                                    return value + '万';
                                }
                            }
                        },
                        x: {
                            ticks: {
                                maxRotation: 45,
                                minRotation: 0,
                                font: {
                                    size: 11
                                }
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                title: function(context) {
                                    // 在tooltip中显示完整的项目名称
                                    const index = context[0].dataIndex;
                                    return <?php echo json_encode($project_names); ?>[index] || context[0].label;
                                },
                                label: function(context) {
                                    return '收款金额: ' + context.parsed.y + '万元';
                                }
                            }
                        }
                    }
                }
            });
            console.log('项目收款分布图表初始化完成');
            } catch (error) {
                console.error('项目收款分布图表初始化失败:', error);
            }

            // 收款状态分析图表（基于真实数据）
            const paymentStatusCtx = document.getElementById('paymentStatusChart');
            if (!paymentStatusCtx) {
                console.error('找不到 paymentStatusChart 元素');
                return;
            }

            try {
                new Chart(paymentStatusCtx.getContext('2d'), {
                type: 'doughnut',
                data: {
                    labels: <?php echo json_encode($payment_status_labels); ?>,
                    datasets: [{
                        data: <?php echo json_encode($payment_status_counts); ?>,
                        backgroundColor: ['#43a047', '#ffb300', '#e53935'],
                        borderColor: '#ffffff',
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
            console.log('收款状态分析图表初始化完成');
            } catch (error) {
                console.error('收款状态分析图表初始化失败:', error);
            }

            // 收款时效分析图表（基于真实数据）
            const paymentTimingCtx = document.getElementById('paymentTimingChart');
            if (!paymentTimingCtx) {
                console.error('找不到 paymentTimingChart 元素');
                return;
            }

            try {
                new Chart(paymentTimingCtx.getContext('2d'), {
                type: 'pie',
                data: {
                    labels: <?php echo json_encode($timing_labels); ?>,
                    datasets: [{
                        data: <?php echo json_encode($timing_counts); ?>,
                        backgroundColor: ['#43a047', '#ffb300', '#ff9800', '#e53935'],
                        borderColor: '#ffffff',
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
            console.log('收款时效分析图表初始化完成');
            } catch (error) {
                console.error('收款时效分析图表初始化失败:', error);
            }

            console.log('所有图表初始化完成');
        }

        // 更新时间显示
        function updateTime() {
            const now = new Date();
            const formattedDate = now.getFullYear() + '-' +
                                 String(now.getMonth() + 1).padStart(2, '0') + '-' +
                                 String(now.getDate()).padStart(2, '0') + ' ' +
                                 String(now.getHours()).padStart(2, '0') + ':' +
                                 String(now.getMinutes()).padStart(2, '0') + ':' +
                                 String(now.getSeconds()).padStart(2, '0');
            document.getElementById('last-update-time').textContent = formattedDate;
        }

        // 每3秒更新一次时间
        setInterval(updateTime, 3000);
    </script>
<?php mysqli_close($link); ?>
</body>
</html>