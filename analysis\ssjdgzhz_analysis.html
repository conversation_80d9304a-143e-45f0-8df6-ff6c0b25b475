<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ssjdgzhz.php 数据来源分析</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }
        h3 {
            color: #2980b9;
            margin-top: 20px;
        }
        .data-source {
            background: #ecf0f1;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #3498db;
        }
        .static-data {
            background: #fff3cd;
            border-left-color: #ffc107;
        }
        .dynamic-data {
            background: #d1ecf1;
            border-left-color: #17a2b8;
        }
        .table-info {
            background: #d4edda;
            border-left-color: #28a745;
        }
        .chart-info {
            background: #f8d7da;
            border-left-color: #dc3545;
        }
        .code-snippet {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        .tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            margin-right: 5px;
        }
        .tag-static { background: #ffc107; color: #212529; }
        .tag-dynamic { background: #17a2b8; color: white; }
        .tag-table { background: #28a745; color: white; }
        .tag-chart { background: #dc3545; color: white; }
        .tag-complex { background: #6f42c1; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <h1>ssjdgzhz.php 页面数据来源分析</h1>
        
        <h2>页面概述</h2>
        <p>ssjdgzhz.php 是一个实时进度工作汇总页面，用于监控和分析各类工作任务的完成情况，包括工程项目、审批流程、监理工作等多个业务模块的进度统计。</p>
        
        <h2>数据来源分析</h2>
        
        <h3>1. 时间范围设置</h3>
        
        <div class="data-source static-data">
            <h4><span class="tag tag-static">静态</span>固定时间范围</h4>
            <p><strong>数据内容：</strong>本月第一天到当前日期</p>
            <p><strong>来源：</strong>PHP date函数计算</p>
            <div class="code-snippet">
$start_date = date('Y-m-01');  // 本月第一天
$end_date = date('Y-m-d');     // 当前日期
            </div>
            <p><strong>说明：</strong>不同于其他页面的用户输入，此页面使用固定的本月数据范围</p>
        </div>
        
        <h3>2. 多表数据源配置</h3>
        
        <div class="data-source table-info">
            <h4><span class="tag tag-complex">复杂</span>工作表映射配置</h4>
            <p><strong>数据内容：</strong>多个业务表的统一配置</p>
            <p><strong>来源：</strong>硬编码的表映射数组</p>
            <div class="code-snippet">
$work_tables = [
    'tuqoa_gcproject' => ['time_field' => 'optdt', 'name' => '工程项目'],
    'tuqoa_flow_bill' => ['time_field' => 'applydt', 'name' => '审批流程'],
    'tuqoa_planm' => ['time_field' => 'startdt', 'name' => '项目计划'],
    'tuqoa_jlrz' => ['time_field' => 'kssj', 'name' => '监理日志'],
    'tuqoa_aqjc' => ['time_field' => 'jcsj', 'name' => '安全检查'],
    'tuqoa_pzjl' => ['time_field' => 'kssj', 'name' => '旁站监理']
];
            </div>
            <p><strong>说明：</strong>每个表配置了时间字段、显示名称和状态逻辑</p>
        </div>
        
        <h3>3. 动态数据查询</h3>
        
        <div class="data-source dynamic-data">
            <h4><span class="tag tag-dynamic">动态</span><span class="tag tag-table">tuqoa_gcproject</span>工程项目统计</h4>
            <p><strong>数据内容：</strong>项目总数、已完成、待处理、逾期数量</p>
            <p><strong>来源：</strong>tuqoa_gcproject表</p>
            <div class="code-snippet">
SELECT COUNT(*) as total,
    SUM(CASE WHEN state = 2 THEN 1 ELSE 0 END) as completed,
    SUM(CASE WHEN state = 1 THEN 1 ELSE 0 END) as pending,
    SUM(CASE WHEN state = 0 AND jhenddt IS NOT NULL 
        AND DATE(jhenddt) < CURDATE() THEN 1 ELSE 0 END) as overdue
FROM `tuqoa_gcproject` 
WHERE DATE(optdt) >= '$start_date' AND DATE(optdt) <= '$end_date'
            </div>
            <p><strong>说明：</strong>基于state字段判断项目状态，jhenddt判断是否逾期</p>
        </div>
        
        <div class="data-source dynamic-data">
            <h4><span class="tag tag-dynamic">动态</span><span class="tag tag-table">tuqoa_flow_bill</span>审批流程统计</h4>
            <p><strong>数据内容：</strong>审批流程的各状态统计</p>
            <p><strong>来源：</strong>tuqoa_flow_bill表</p>
            <div class="code-snippet">
SELECT COUNT(*) as total,
    SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as completed,
    SUM(CASE WHEN status = 0 AND isturn = 1 THEN 1 ELSE 0 END) as pending,
    SUM(CASE WHEN status = -1 OR nstatus = -1 THEN 1 ELSE 0 END) as overdue
FROM `tuqoa_flow_bill` 
WHERE DATE(applydt) >= '$start_date' AND DATE(applydt) <= '$end_date' 
AND isdel = 0
            </div>
            <p><strong>说明：</strong>基于status、isturn、nstatus字段判断审批状态</p>
        </div>
        
        <div class="data-source dynamic-data">
            <h4><span class="tag tag-dynamic">动态</span><span class="tag tag-table">tuqoa_planm</span>项目计划统计</h4>
            <p><strong>数据内容：</strong>项目计划的执行状态统计</p>
            <p><strong>来源：</strong>tuqoa_planm表</p>
            <div class="code-snippet">
SELECT COUNT(*) as total,
    SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as completed,
    SUM(CASE WHEN status = 0 AND (enddt IS NULL OR DATE(enddt) >= CURDATE()) 
        THEN 1 ELSE 0 END) as pending,
    SUM(CASE WHEN status = 0 AND enddt IS NOT NULL 
        AND DATE(enddt) < CURDATE() THEN 1 ELSE 0 END) as overdue
FROM `tuqoa_planm` 
WHERE DATE(startdt) >= '$start_date' AND DATE(startdt) <= '$end_date'
            </div>
            <p><strong>说明：</strong>基于status和enddt字段判断计划执行状态</p>
        </div>
        
        <div class="data-source dynamic-data">
            <h4><span class="tag tag-dynamic">动态</span><span class="tag tag-table">其他业务表</span>监理和检查统计</h4>
            <p><strong>数据内容：</strong>监理日志、安全检查、旁站监理统计</p>
            <p><strong>来源：</strong>tuqoa_jlrz、tuqoa_aqjc、tuqoa_pzjl表</p>
            <div class="code-snippet">
// 监理日志 - 基于日期判断状态
SELECT COUNT(*) as total,
    SUM(CASE WHEN DATE(kssj) <= CURDATE() THEN 1 ELSE 0 END) as completed,
    SUM(CASE WHEN DATE(kssj) = CURDATE() THEN 1 ELSE 0 END) as pending,
    SUM(CASE WHEN DATE(kssj) < CURDATE() - INTERVAL 1 DAY THEN 1 ELSE 0 END) as overdue
FROM `tuqoa_jlrz`

// 旁站监理 - 基于开始和结束时间判断状态  
SELECT COUNT(*) as total,
    SUM(CASE WHEN jssj IS NOT NULL THEN 1 ELSE 0 END) as completed,
    SUM(CASE WHEN jssj IS NULL AND DATE(kssj) >= CURDATE() THEN 1 ELSE 0 END) as pending,
    SUM(CASE WHEN jssj IS NULL AND DATE(kssj) < CURDATE() THEN 1 ELSE 0 END) as overdue
FROM `tuqoa_pzjl`
            </div>
            <p><strong>说明：</strong>每个表根据其业务特点使用不同的状态判断逻辑</p>
        </div>
        
        <h3>4. 图表数据生成</h3>
        
        <div class="data-source chart-info">
            <h4><span class="tag tag-chart">图表</span>工作效率趋势（最近7天）</h4>
            <p><strong>数据内容：</strong>每日计划工作量和实际完成量</p>
            <p><strong>来源：</strong>多表统计+计算生成</p>
            <div class="code-snippet">
// 循环生成最近7天数据
for ($i = 6; $i >= 0; $i--) {
    $date = date('Y-m-d', strtotime("-$i days"));
    
    // 统计当日实际工作量
    foreach ($work_tables as $table => $info) {
        $sql = "SELECT COUNT(*) as count FROM `$table` 
                WHERE DATE($time_field) = '$date'";
    }
    
    // 计算计划工作量
    $planned_work_sql = "SELECT COUNT(*) as count FROM tuqoa_gcproject 
                        WHERE DATE(jhstartdt) = '$date' OR DATE(jhenddt) = '$date'";
}
            </div>
            <p><strong>说明：</strong>结合实际数据和计划数据生成趋势图</p>
        </div>
        
        <div class="data-source chart-info">
            <h4><span class="tag tag-chart">图表</span>项目类型分布</h4>
            <p><strong>数据内容：</strong>不同类型项目的数量分布</p>
            <p><strong>来源：</strong>tuqoa_gcproject表的leixing字段</p>
            <div class="code-snippet">
SELECT
    COALESCE(leixing, '未分类') as project_type,
    COUNT(*) as count
FROM tuqoa_gcproject
WHERE DATE(COALESCE(optdt, jhstartdt)) >= '$start_date' 
    AND DATE(COALESCE(optdt, jhstartdt)) <= '$end_date'
GROUP BY leixing
ORDER BY count DESC
LIMIT 8
            </div>
            <p><strong>说明：</strong>按项目类型分组统计，取前8名</p>
        </div>
        
        <div class="data-source chart-info">
            <h4><span class="tag tag-chart">图表</span>项目进度统计</h4>
            <p><strong>数据内容：</strong>项目按进度状态的分布</p>
            <p><strong>来源：</strong>tuqoa_gcproject表的state字段</p>
            <div class="code-snippet">
SELECT
    CASE
        WHEN state = 0 THEN '未开始'
        WHEN state = 1 THEN '进行中'
        WHEN state = 2 THEN '已完成'
        ELSE '其他'
    END as progress_status,
    COUNT(*) as count
FROM tuqoa_gcproject
WHERE DATE(COALESCE(optdt, jhstartdt)) >= '$start_date' 
    AND DATE(COALESCE(optdt, jhstartdt)) <= '$end_date'
GROUP BY state
ORDER BY state
            </div>
            <p><strong>说明：</strong>使用CASE语句将state值转换为可读的进度状态</p>
        </div>
        
        <h3>5. 容错和默认数据</h3>
        
        <div class="data-source static-data">
            <h4><span class="tag tag-static">静态</span>默认数据机制</h4>
            <p><strong>数据内容：</strong>当数据库无数据时的默认值</p>
            <p><strong>来源：</strong>硬编码的默认数据</p>
            <div class="code-snippet">
// 如果没有真实数据，使用默认值
if ($total_work == 0) {
    $total_work = 156;
    $completed_work = 98;
    $pending_work = 45;
    $overdue_work = 13;
    $work_type_counts = [
        '现场巡视' => 35,
        '监理日志' => 28,
        '安全检查' => 22,
        '旁站监理' => 18,
        '工程验收' => 15
    ];
}
            </div>
            <p><strong>说明：</strong>确保页面在任何情况下都能正常显示</p>
        </div>
        
        <h2>数据表结构</h2>
        
        <div class="table-info">
            <h4>主要数据表及关键字段</h4>
            <ul>
                <li><strong>tuqoa_gcproject：</strong>工程项目表
                    <ul>
                        <li>state：项目状态（0=未开始，1=进行中，2=已完成）</li>
                        <li>optdt：操作日期</li>
                        <li>jhstartdt：计划开始日期</li>
                        <li>jhenddt：计划结束日期</li>
                        <li>leixing：项目类型</li>
                    </ul>
                </li>
                <li><strong>tuqoa_flow_bill：</strong>审批流程表
                    <ul>
                        <li>status：审批状态</li>
                        <li>isturn：是否轮转</li>
                        <li>nstatus：新状态</li>
                        <li>applydt：申请日期</li>
                        <li>isdel：是否删除</li>
                    </ul>
                </li>
                <li><strong>tuqoa_planm：</strong>项目计划表
                    <ul>
                        <li>status：计划状态</li>
                        <li>startdt：开始日期</li>
                        <li>enddt：结束日期</li>
                    </ul>
                </li>
                <li><strong>tuqoa_jlrz：</strong>监理日志表
                    <ul>
                        <li>kssj：开始时间</li>
                    </ul>
                </li>
                <li><strong>tuqoa_aqjc：</strong>安全检查表
                    <ul>
                        <li>jcsj：检查时间</li>
                    </ul>
                </li>
                <li><strong>tuqoa_pzjl：</strong>旁站监理表
                    <ul>
                        <li>kssj：开始时间</li>
                        <li>jssj：结束时间</li>
                    </ul>
                </li>
            </ul>
        </div>
        
        <h2>页面特点</h2>
        
        <h3>1. 多业务模块整合</h3>
        <ul>
            <li>整合6个不同的业务表数据</li>
            <li>每个表使用不同的状态判断逻辑</li>
            <li>统一的数据处理和展示框架</li>
        </ul>
        
        <h3>2. 智能状态判断</h3>
        <ul>
            <li>根据不同表的业务特点设计状态逻辑</li>
            <li>考虑时间因素判断逾期状态</li>
            <li>处理空值和异常情况</li>
        </ul>
        
        <h3>3. 容错机制</h3>
        <ul>
            <li>表存在性检查</li>
            <li>数据为空时的默认值</li>
            <li>合理的数据分布计算</li>
        </ul>
        
        <h3>4. 实时性</h3>
        <ul>
            <li>固定使用本月数据范围</li>
            <li>基于当前日期判断逾期状态</li>
            <li>最近7天的趋势分析</li>
        </ul>
        
        <h2>总结</h2>
        <p>ssjdgzhz.php是一个复杂的多业务模块整合页面，数据来源包括：</p>
        <ul>
            <li><strong>多表动态数据：</strong>6个不同业务表的实时统计</li>
            <li><strong>智能计算：</strong>基于业务逻辑的状态判断和趋势分析</li>
            <li><strong>容错机制：</strong>完善的默认数据和异常处理</li>
            <li><strong>实时监控：</strong>本月数据的实时进度跟踪</li>
        </ul>
        <p>该页面展现了复杂业务系统中多模块数据整合的典型模式，具有很强的实用性和扩展性。</p>
    </div>
</body>
</html>
