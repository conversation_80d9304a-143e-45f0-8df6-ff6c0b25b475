<!DOCTYPE html>
<html lang="zh-CN">
<?php
include '../config.php';
error_reporting(0);
session_start();
$firstDayOfMonth = date('Y-m');
$lastDayOfMonth = date('Y-m');
$startDate = isset($_POST['start-date']) ? $_POST['start-date'] : $firstDayOfMonth;
$endDate = isset($_POST['end-date']) ? $_POST['end-date'] : $lastDayOfMonth;
// 初始化日期变量
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
        // 验证日期
    if (strtotime($startDate) > strtotime($endDate)) {
        echo '<div class="result" style="background-color: #fde8e8;">错误：开始日期不能晚于结束日期</div>';
    } else {
        // 格式化日期用于显示
        $displayStart = date('Y年m月', strtotime($startDate));
        $displayEnd = date('Y年m月', strtotime($endDate));
        $daysDiff = (strtotime($endDate) - strtotime($startDate)) / (60 * 60 * 24) + 1;
        
       
        
        // 添加本月信息
        $currentMonth = date('Y年m月');
        $monthDays = date('t', strtotime($firstDayOfMonth));
       
    }
}
$uid = $_SESSION['xinhu_adminid'];
$projectid=$_SESSION['xinhu_projectid'];
$project=$_SESSION['xinhu_project'];
?>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="styles/bootstrap.min.css" rel="stylesheet">
    <link href="styles//boxicons.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles/main.css">
    <title>项目费用明细表</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8fafc;
        }
        .header {
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e2e8f0;
            position: sticky;
            top: 0;
            background-color: #f8fafc;
            z-index: 20;
        }
        .header h2 {
            color: #2d3748;
            margin: 0;
        }
        .header p {
            color: #718096;
            margin: 5px 0 0;
            font-size: 14px;
        }
        .table-container {
            width: 100%;
            height: calc(100vh - 180px); /* 设置固定高度，留出空间给页眉页脚 */
            overflow: auto;
            border-radius: 8px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            background: white;
            position: relative;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }
        thead {
            position: sticky;
            top: 0;
            z-index: 15;
        }
        th {
            background-color: #2563eb;
            color: white;
            padding: 8px 12px;
            text-align: left;
            font-weight: 500;
            white-space: nowrap;
            border: 1px solid rgba(255,255,255,0.2);
            position: sticky;
            top: 0;
        }
        td {
            padding: 8px 12px;
            border: 1px solid #e2e8f0;
            white-space: nowrap;
            color: #4a5568;
            text-align: left;
        }
        .value-cell {
            text-align: right;
        }
        tbody tr:hover td {
            background-color: #f7fafc;
        }
        tbody tr:nth-child(even) {
            background-color: #f8fafc;
        }
        .footer {
            margin-top: 15px;
            font-size: 12px;
            color: #718096;
            text-align: right;
            position: sticky;
            bottom: 0;
            background-color: #f8fafc;
            padding: 10px 0;
        }
        .text-left {
            text-align: left;
        }
        .text-center {
            text-align: center;
        }
        .highlight {
            font-weight: 500;
        }
        .category-header {
            background-color: #3b82f6;
            font-weight: bold;
        }
        .subcategory-header {
            background-color: #60a5fa;
            padding-left: 20px;
        }
    </style>
    <script language="JavaScript" type="text/javascript">
            var idTmr;
            function getExplorer() {
              var explorer = window.navigator.userAgent ;
              //ie
              if (explorer.indexOf("MSIE") >= 0) {
                return 'ie';
              }
              //firefox
              else if (explorer.indexOf("Firefox") >= 0) {
                return 'Firefox';
              }
              //Chrome
              else if(explorer.indexOf("Chrome") >= 0){
                return 'Chrome';
              }
              //Opera
              else if(explorer.indexOf("Opera") >= 0){
                return 'Opera';
              }
              //Safari
              else if(explorer.indexOf("Safari") >= 0){
                return 'Safari';
              }
            }
            function method5(tableid) {
              if(getExplorer()=='ie')
              {
                var curTbl = document.getElementById(tableid);
                var oXL = new ActiveXObject("Excel.Application");
                var oWB = oXL.Workbooks.Add();
                var xlsheet = oWB.Worksheets(1);
                var sel = document.body.createTextRange();
                sel.moveToElementText(curTbl);
                sel.select();
                sel.execCommand("Copy");
                xlsheet.Paste();
                oXL.Visible = true;
                try {
                  var fname = oXL.Application.GetSaveAsFilename("Excel.xls", "Excel Spreadsheets (*.xls), *.xls");
                } catch (e) {
                  print("Nested catch caught " + e);
                } finally {
                  oWB.SaveAs(fname);
                  oWB.Close(savechanges = false);
                  oXL.Quit();
                  oXL = null;
                  idTmr = window.setInterval("Cleanup();", 1);
                }
              }
              else
              {
                tableToExcel(tableid)
              }
            }
            function Cleanup() {
              window.clearInterval(idTmr);
              CollectGarbage();
            }
            var tableToExcel = (function() {
              var uri = 'data:application/vnd.ms-excel;base64,',
                  template = '<html><head><meta charset="UTF-8"></head><body><table>{table}</table></body></html>',
                  base64 = function(s) { return window.btoa(unescape(encodeURIComponent(s))) },
                  format = function(s, c) {
                    return s.replace(/{(\w+)}/g,
                        function(m, p) { return c[p]; }) }
              return function(table, name) {
                if (!table.nodeType) table = document.getElementById(table)
                var ctx = {worksheet: name || 'Worksheet', table: table.innerHTML}
                window.location.href = uri + base64(format(template, ctx))
              }
            })()
          </script>
</head>
<body>
    <div class="header">
        <h2>项目费用明细表</h2>
    </div>
    <div class="date-range-container">
        <form method="post" action="">
            <div class="form-group">
                <label for="start-date">开始日期:</label>
                <input type="month" id="start-date" name="start-date" 
                       value="<?php echo isset($_POST['start-date']) ? htmlspecialchars($_POST['start-date']) : date('Y-m'); ?>">
                <label for="end-date">结束日期:</label>
                <input type="month" id="end-date" name="end-date" 
                       value="<?php echo isset($_POST['end-date']) ? htmlspecialchars($_POST['end-date']) : date('Y-m'); ?>">
                <button type="submit" id="query-btn">提交</button>
                <button type="button" onclick="method5('tableExcel')">导出Excel</button>
            </div>
        </form>
    </div>
    <div class="table-container">
        <table id="tableExcel">
            <thead>
                <tr>
                    <th>项目</th>
                    <th>金额</th>
                    <th></th>
                    <th></th>
                    <th></th>
                    <th></th>
                    <th></th>
                </tr>
            </thead>
            <tbody>
                <?
                $sql="SELECT * FROM `tuqoa_gcproject` WHERE id=$projectid";
                $result = mysqli_query($link, $sql);
                while ($row = mysqli_fetch_assoc($result)) {
                    // 计算各项费用
                    $实发工资合计=0;
                    $实发工资明细="";
                    $应发工资合计=0;
                    $公积金合计=0;
                    $伙食费合计=0;
                    $采暖费合计=0;
                    $sql1="SELECT * FROM `tuqoa_rydp` WHERE `drxmid`=".$row["id"]." and `sfqz`='全职' and `state`='在职'";
                    $result1 = mysqli_query($link, $sql1);
                    while ($row1 = mysqli_fetch_assoc($result1)) {
                        $sql2="SELECT *  FROM `tuqoa_hrsalary` WHERE `uname`='".$row1["dpryxm"]."' and  `month`>='$startDate' and `month`<='$endDate'";
                        $result2 = mysqli_query($link, $sql2);
                        while ($row2 = mysqli_fetch_assoc($result2)) {
                            $实发工资合计+=$row2["sfgz"];
                            //$实发工资明细.=($row2["uname"].":".$row2["sfgz"].".\n");
                            $实发工资明细 .= (string)$row2["uname"] . ":" . (string)$row2["sfgz"] . "\n";
                            $应发工资合计+=$row2["yfgz"];
                            $公积金合计+=$row2["zfgjj"]*2;
                            $伙食费合计+=$row2["foodbt"];
                            $采暖费合计+=$row2["cnf"];
                        }
                    }
                    
                    $date = new DateTime($endDate);
                    $lastDayOfMonth = $date->format('Y-m-t');
                    
                    // 项目上缴社保费用明细
                    $养老合计=0;
                    $医疗报销合计=0;
                    $sql1="SELECT ifnull(sum(ylxj),0) ylhj,ifnull(sum(yiliaobxxj),0) yiliaobxxjhj FROM `tuqoa_xmsjbxmx` WHERE `projectid`=".$row["id"]." and  `ys`>='$startDate.-01' and `ys`<='$lastDayOfMonth'";
                    $result1 = mysqli_query($link, $sql1);
                    while ($row1 = mysqli_fetch_assoc($result1)) {
                        $养老合计=$row1["ylhj"];
                        $医疗报销合计=$row1["yiliaobxxjhj"];
                    }
                    
                    // 企业上缴社保管理费等
                    $福利费合计=0;
                    $购买行政用品=0;
                    $办公费=0;
                    $折旧费=0;
                    $低值易耗品摊销=0;
                    $差旅费=0;
                    $其他费用=0;
                    $中标服务费=0;
                    $业务招待费=0;
                    $企业管理费=0;
                    $经营业务费=0;
                    $利润=0;
                    $税金=0;
                    $sql1="SELECT * FROM `tuqoa_xmhstjzl` WHERE `projectid`=".$row["id"]." and  `sbrq`>='$startDate.-01' and `sbrq`<='$lastDayOfMonth'";
                    $result1 = mysqli_query($link, $sql1);
                    while ($row1 = mysqli_fetch_assoc($result1)) {
                        $福利费合计+=$row1["flf"];
                        $购买行政用品+=$row1["gmxzyp"];
                        $办公费+=$row1["bgf"];
                        $折旧费+=$row1["zzjf"];
                        $低值易耗品摊销+=$row1["dzyhptx"];
                        $差旅费+=$row1["clf"];
                        $其他费用+=$row1["qtfy"];
                        $中标服务费+=$row1["zbfwf"];
                        $业务招待费+=$row1["ywzdf"];
                        $企业管理费+=$row1["qyglf"];
                        $经营业务费+=$row1["jyywf"];
                        $利润+=$row1["lr"];
                        $税金+=$row1["sj"];
                    }
                ?>
                <tr>
                    <td colspan="2" class="category-header"><?=$row["gcname"]?> (项目ID: <?=$row["id"]?>)</td>
                </tr>
                
                <tr>
                    <td colspan="2" class="subcategory-header">直接费</td>
                </tr>
                <tr>
                    <td colspan="2" style="padding-left: 30px;">人工费</td>
                </tr>
                <tr>
                    <td style="padding-left: 50px;">实发工资</td>
                    <td class="value-cell"><p title="<?=$实发工资明细?>"><?=$实发工资合计?></p></td>
                </tr>
                <tr>
                    <td style="padding-left: 50px;">公积金（单位+个人）</td>
                    <td class="value-cell"><?=$公积金合计?></td>
                </tr>
                <tr>
                    <td style="padding-left: 50px;">社医保（单位+个人）</td>
                    <td class="value-cell"><?=$养老合计+$医疗报销合计?></td>
                </tr>
                <tr>
                    <td style="padding-left: 50px;">伙食费</td>
                    <td class="value-cell"><?=$伙食费合计?></td>
                </tr>
                <tr>
                    <td style="padding-left: 50px;">采暖费</td>
                    <td class="value-cell"><?=$采暖费合计?></td>
                </tr>
                <tr>
                    <td style="padding-left: 50px;">福利费</td>
                    <td class="value-cell"><?=$福利费合计?></td>
                </tr>
                <tr>
                    <td style="padding-left: 50px;">工会经费（工资*2%）</td>
                    <td class="value-cell"><?=round($应发工资合计*0.02,2)?></td>
                </tr>
                
                <tr>
                    <td colspan="2" style="padding-left: 30px;">其他直接费</td>
                </tr>
                <tr>
                    <td style="padding-left: 50px;">购买行政用品</td>
                    <td class="value-cell"><?=$购买行政用品?></td>
                </tr>
                <tr>
                    <td style="padding-left: 50px;">办公费</td>
                    <td class="value-cell"><?=$办公费?></td>
                </tr>
                <tr>
                    <td style="padding-left: 50px;">折旧费</td>
                    <td class="value-cell"><?=$折旧费?></td>
                </tr>
                <tr>
                    <td style="padding-left: 50px;">低值易耗品摊销</td>
                    <td class="value-cell"><?=$低值易耗品摊销?></td>
                </tr>
                <tr>
                    <td style="padding-left: 50px;">差旅费</td>
                    <td class="value-cell"><?=$差旅费?></td>
                </tr>
                <tr>
                    <td style="padding-left: 50px;">其他费用</td>
                    <td class="value-cell"><?=$其他费用?></td>
                </tr>
                <tr>
                    <td style="padding-left: 50px;">中标服务费</td>
                    <td class="value-cell"><?=$中标服务费?></td>
                </tr>
                <tr>
                    <td style="padding-left: 50px;">业务招待费</td>
                    <td class="value-cell"><?=$业务招待费?></td>
                </tr>
                
                <tr>
                    <td colspan="2" class="subcategory-header">间接费</td>
                </tr>
                <tr>
                    <td style="padding-left: 30px;">企业管理费</td>
                    <td class="value-cell"><?=$企业管理费?></td>
                </tr>
                <tr>
                    <td style="padding-left: 30px;">经营业务费</td>
                    <td class="value-cell"><?=$经营业务费?></td>
                </tr>
                
                <tr>
                    <td colspan="2" class="subcategory-header">其他</td>
                </tr>
                <tr>
                    <td style="padding-left: 30px;">利润（%）</td>
                    <td class="value-cell"><?=$利润?></td>
                </tr>
                <tr>
                    <td style="padding-left: 30px;">税金（8.2%）</td>
                    <td class="value-cell"><?=$税金?></td>
                </tr>
                
                <tr>
                    <td colspan="2" style="height: 20px; background-color: #f8fafc;"></td>
                </tr>
                <?
                }
                ?>
            </tbody>
        </table>
    </div>
</body>
</html>