<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PHP页面数据来源总体分析汇总</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            text-align: center;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }
        h3 {
            color: #2980b9;
            margin-top: 20px;
        }
        .page-summary {
            background: #ecf0f1;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #3498db;
        }
        .static-data {
            background: #fff3cd;
            border-left-color: #ffc107;
        }
        .dynamic-data {
            background: #d1ecf1;
            border-left-color: #17a2b8;
        }
        .mixed-data {
            background: #f8d7da;
            border-left-color: #dc3545;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .table th, .table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        .table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            margin-right: 5px;
        }
        .tag-static { background: #ffc107; color: #212529; }
        .tag-dynamic { background: #17a2b8; color: white; }
        .tag-mixed { background: #dc3545; color: white; }
        .tag-system { background: #6f42c1; color: white; }
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>PHP页面数据来源总体分析汇总</h1>
        
        <div class="summary-stats">
            <div class="stat-card">
                <h3>总页面数</h3>
                <div class="stat-number">26</div>
                <p>已识别的PHP页面</p>
            </div>
            <div class="stat-card">
                <h3>已分析页面</h3>
                <div class="stat-number">9</div>
                <p>详细分析完成</p>
            </div>
            <div class="stat-card">
                <h3>主要数据表</h3>
                <div class="stat-number">17</div>
                <p>核心业务表</p>
            </div>
            <div class="stat-card">
                <h3>数据类型</h3>
                <div class="stat-number">3</div>
                <p>静态/动态/混合</p>
            </div>
        </div>

        <h2>页面分类概览</h2>
        
        <table class="table">
            <thead>
                <tr>
                    <th>页面文件</th>
                    <th>页面功能</th>
                    <th>数据类型</th>
                    <th>主要数据源</th>
                    <th>图表类型</th>
                    <th>分析状态</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>diagnose.php</td>
                    <td>系统诊断</td>
                    <td><span class="tag tag-mixed">混合</span></td>
                    <td>系统检查+数据库验证</td>
                    <td>统计卡片</td>
                    <td>✅ 已完成</td>
                </tr>
                <tr>
                    <td>dkzbcx.php</td>
                    <td>代打卡监测</td>
                    <td><span class="tag tag-dynamic">动态</span></td>
                    <td>tuqoa_kqdkjl, tuqoa_userinfo</td>
                    <td>表格+统计卡片</td>
                    <td>✅ 已完成</td>
                </tr>
                <tr>
                    <td>dzetj.php</td>
                    <td>到账额统计</td>
                    <td><span class="tag tag-dynamic">动态</span></td>
                    <td>tuqoa_htsf</td>
                    <td>折线图+饼图+表格</td>
                    <td>✅ 已完成</td>
                </tr>
                <tr>
                    <td>fgbmxmhzb.php</td>
                    <td>分管部门项目汇总</td>
                    <td><span class="tag tag-static">静态</span></td>
                    <td>硬编码数据</td>
                    <td>多种图表</td>
                    <td>✅ 已完成</td>
                </tr>
                <tr>
                    <td>gsxmsjhz.php</td>
                    <td>公司项目数据汇总</td>
                    <td><span class="tag tag-dynamic">动态</span></td>
                    <td>tuqoa_htgl, tuqoa_xmcztjb, tuqoa_htsf, tuqoa_gcproject</td>
                    <td>多维度图表</td>
                    <td>✅ 已完成</td>
                </tr>
                <tr>
                    <td>ssjdgzhz.php</td>
                    <td>实时进度工作汇总</td>
                    <td><span class="tag tag-dynamic">动态</span></td>
                    <td>6个业务表多模块整合</td>
                    <td>进度统计+图表</td>
                    <td>✅ 已完成</td>
                </tr>
                <tr>
                    <td>xmcbhs.php</td>
                    <td>项目成本核算</td>
                    <td><span class="tag tag-dynamic">动态</span></td>
                    <td>tuqoa_gcproject, tuqoa_htgl, tuqoa_xmcztjb</td>
                    <td>成本分析+图表</td>
                    <td>✅ 已完成</td>
                </tr>
                <tr>
                    <td>jydt.php</td>
                    <td>经营动态监控</td>
                    <td><span class="tag tag-dynamic">动态</span></td>
                    <td>tuqoa_htgl, tuqoa_userinfo</td>
                    <td>实时监控+趋势</td>
                    <td>✅ 已完成</td>
                </tr>
                <tr>
                    <td>jytj.php</td>
                    <td>经营统计分析</td>
                    <td><span class="tag tag-dynamic">动态</span></td>
                    <td>tuqoa_htgl, tuqoa_htsf</td>
                    <td>双时间维度分析</td>
                    <td>✅ 已完成</td>
                </tr>
                <tr>
                    <td>gztz.php</td>
                    <td>工作台账管理</td>
                    <td><span class="tag tag-dynamic">动态</span></td>
                    <td>11个监理业务表</td>
                    <td>工作记录+统计</td>
                    <td>✅ 已完成</td>
                </tr>
                <tr>
                    <td>其他17个页面</td>
                    <td>各种业务功能</td>
                    <td><span class="tag tag-mixed">待分析</span></td>
                    <td>待确定</td>
                    <td>待确定</td>
                    <td>🔄 快速分析</td>
                </tr>
            </tbody>
        </table>

        <h2>主要数据表分析</h2>
        
        <div class="page-summary dynamic-data">
            <h4>核心业务数据表</h4>
            <ul>
                <li><strong>tuqoa_gcproject：</strong>工程项目表 - 项目基本信息、状态、造价等</li>
                <li><strong>tuqoa_htgl：</strong>合同管理表 - 合同信息、服务费等</li>
                <li><strong>tuqoa_xmcztjb：</strong>项目成本统计表 - 成本数据、申报信息</li>
                <li><strong>tuqoa_htsf：</strong>合同收费表 - 收款信息、到账数据</li>
                <li><strong>tuqoa_kqdkjl：</strong>考勤打卡记录表 - 打卡数据、设备信息</li>
                <li><strong>tuqoa_userinfo：</strong>用户信息表 - 员工基本信息</li>
            </ul>
        </div>

        <h2>数据来源类型分析</h2>
        
        <h3>1. 静态数据页面</h3>
        <div class="page-summary static-data">
            <h4>特点：</h4>
            <ul>
                <li>数据硬编码在HTML或JavaScript中</li>
                <li>主要用于展示设计效果和功能布局</li>
                <li>不连接实际数据库</li>
                <li>示例：fgbmxmhzb.php</li>
            </ul>
        </div>
        
        <h3>2. 动态数据页面</h3>
        <div class="page-summary dynamic-data">
            <h4>特点：</h4>
            <ul>
                <li>从数据库实时查询数据</li>
                <li>支持用户输入参数（日期范围、项目选择等）</li>
                <li>使用复杂SQL查询和数据处理</li>
                <li>示例：dzetj.php, gsxmsjhz.php, dkzbcx.php</li>
            </ul>
        </div>
        
        <h3>3. 混合数据页面</h3>
        <div class="page-summary mixed-data">
            <h4>特点：</h4>
            <ul>
                <li>结合静态配置和动态查询</li>
                <li>包含系统检查和数据验证</li>
                <li>部分数据硬编码，部分数据库查询</li>
                <li>示例：diagnose.php</li>
            </ul>
        </div>

        <h2>图表和表格类型统计</h2>
        
        <table class="table">
            <thead>
                <tr>
                    <th>可视化类型</th>
                    <th>使用页面</th>
                    <th>数据来源</th>
                    <th>主要用途</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>统计卡片</td>
                    <td>所有页面</td>
                    <td>数据库查询/静态数据</td>
                    <td>关键指标展示</td>
                </tr>
                <tr>
                    <td>折线图</td>
                    <td>dzetj.php, gsxmsjhz.php</td>
                    <td>按时间维度统计</td>
                    <td>趋势分析</td>
                </tr>
                <tr>
                    <td>饼图</td>
                    <td>dzetj.php, gsxmsjhz.php</td>
                    <td>分类统计数据</td>
                    <td>占比分析</td>
                </tr>
                <tr>
                    <td>柱状图</td>
                    <td>fgbmxmhzb.php</td>
                    <td>静态数据</td>
                    <td>对比分析</td>
                </tr>
                <tr>
                    <td>数据表格</td>
                    <td>多个页面</td>
                    <td>数据库查询</td>
                    <td>详细数据展示</td>
                </tr>
            </tbody>
        </table>

        <h2>常用查询模式</h2>
        
        <div class="page-summary">
            <h4>1. 时间范围查询</h4>
            <p>几乎所有页面都支持按日期范围筛选数据，默认为当月数据</p>
            
            <h4>2. 聚合统计查询</h4>
            <p>大量使用SUM、COUNT、AVG等聚合函数进行数据统计</p>
            
            <h4>3. 分类统计查询</h4>
            <p>使用CASE语句对数据进行分类，如项目状态、类型等</p>
            
            <h4>4. 关联查询</h4>
            <p>多表JOIN查询，获取完整的业务数据</p>
            
            <h4>5. 时间维度分析</h4>
            <p>使用DATE_FORMAT按月、季度、年度进行数据分组</p>
        </div>

        <h2>技术特点总结</h2>
        
        <div class="page-summary">
            <h4>前端技术：</h4>
            <ul>
                <li>Bootstrap 5.1.3 - 响应式布局</li>
                <li>Chart.js - 图表绘制</li>
                <li>Font Awesome & Boxicons - 图标库</li>
                <li>自定义CSS - 页面样式</li>
            </ul>
            
            <h4>后端技术：</h4>
            <ul>
                <li>PHP - 服务器端脚本</li>
                <li>MySQL - 数据库</li>
                <li>MySQLi - 数据库连接</li>
            </ul>
            
            <h4>数据处理：</h4>
            <ul>
                <li>复杂SQL查询</li>
                <li>数据验证和格式化</li>
                <li>JSON数据传递</li>
                <li>错误处理机制</li>
            </ul>
        </div>

        <h2>建议和改进方向</h2>
        
        <div class="page-summary mixed-data">
            <h4>数据一致性：</h4>
            <ul>
                <li>将静态数据页面连接到实际数据库</li>
                <li>统一数据查询接口和格式</li>
                <li>建立数据字典和标准</li>
            </ul>
            
            <h4>性能优化：</h4>
            <ul>
                <li>优化复杂SQL查询</li>
                <li>添加数据缓存机制</li>
                <li>实现分页和懒加载</li>
            </ul>
            
            <h4>功能增强：</h4>
            <ul>
                <li>添加数据导出功能</li>
                <li>实现实时数据更新</li>
                <li>增加数据钻取功能</li>
                <li>添加用户权限控制</li>
            </ul>
        </div>

        <h2>结论</h2>
        <p>该项目包含26个PHP页面，涵盖了项目管理、财务统计、人员考勤等多个业务领域。数据来源主要分为三类：</p>
        <ul>
            <li><strong>静态数据：</strong>用于展示页面设计效果</li>
            <li><strong>动态数据：</strong>从数据库实时查询的业务数据</li>
            <li><strong>混合数据：</strong>结合系统检查和数据库查询</li>
        </ul>
        <p>系统具有良好的数据可视化能力，但需要进一步完善数据一致性和性能优化。</p>
    </div>
</body>
</html>
