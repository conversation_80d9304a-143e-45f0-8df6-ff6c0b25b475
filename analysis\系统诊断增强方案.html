<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统诊断增强方案</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 10px;
            font-size: 2.5rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .subtitle {
            text-align: center;
            color: #7f8c8d;
            margin-bottom: 30px;
            font-size: 1.1rem;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }
        h3 {
            color: #2980b9;
            margin-top: 20px;
        }
        .diagnostic-card {
            background: #f8f9fa;
            padding: 20px;
            margin: 15px 0;
            border-radius: 10px;
            border-left: 4px solid #3498db;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .critical { border-left-color: #dc3545; }
        .warning { border-left-color: #ffc107; }
        .info { border-left-color: #17a2b8; }
        .success { border-left-color: #28a745; }
        .code-snippet {
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 10px 0;
        }
        .php-code { background: #4a5568; }
        .sql-code { background: #2d3748; }
        .bash-code { background: #1a202c; }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .table th, .table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
            font-size: 14px;
        }
        .table th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            font-weight: 600;
        }
        .status-indicator {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-ok { background: #d4edda; color: #155724; }
        .status-warning { background: #fff3cd; color: #856404; }
        .status-error { background: #f8d7da; color: #721c24; }
        .metric-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            margin: 10px 0;
        }
        .alert-box {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            border-left: 4px solid #dc3545;
        }
        .tip-box {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            border-left: 4px solid #17a2b8;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 系统诊断增强方案</h1>
        <p class="subtitle">基于现有diagnose.php的全面系统健康监控解决方案</p>
        
        <h2>📊 当前诊断功能分析</h2>
        
        <div class="diagnostic-card info">
            <h3>现有diagnose.php功能</h3>
            <p>当前的系统诊断页面主要提供基础的系统状态检查，包括：</p>
            <ul>
                <li>数据库连接状态检查</li>
                <li>基本的系统信息显示</li>
                <li>简单的配置验证</li>
                <li>静态的系统状态展示</li>
            </ul>
            
            <div class="alert-box">
                <strong>⚠️ 当前限制：</strong>
                <ul>
                    <li>缺乏实时性能监控</li>
                    <li>没有历史趋势分析</li>
                    <li>缺少自动化告警机制</li>
                    <li>诊断项目相对简单</li>
                </ul>
            </div>
        </div>

        <h2>🚀 增强诊断方案</h2>

        <div class="diagnostic-card critical">
            <h3>1. 数据库健康监控</h3>
            
            <h4>连接池监控</h4>
            <div class="code-snippet php-code">
&lt;?php
// 增强的数据库连接监控
function getDatabaseHealth() {
    global $link;
    
    $health = [
        'connection_status' => 'unknown',
        'active_connections' => 0,
        'max_connections' => 0,
        'slow_queries' => 0,
        'query_cache_hit_rate' => 0,
        'innodb_buffer_pool_usage' => 0
    ];
    
    try {
        // 检查连接状态
        if (mysqli_ping($link)) {
            $health['connection_status'] = 'connected';
            
            // 获取连接数信息
            $result = mysqli_query($link, "SHOW STATUS LIKE 'Threads_connected'");
            if ($row = mysqli_fetch_assoc($result)) {
                $health['active_connections'] = (int)$row['Value'];
            }
            
            // 获取最大连接数
            $result = mysqli_query($link, "SHOW VARIABLES LIKE 'max_connections'");
            if ($row = mysqli_fetch_assoc($result)) {
                $health['max_connections'] = (int)$row['Value'];
            }
            
            // 获取慢查询数量
            $result = mysqli_query($link, "SHOW STATUS LIKE 'Slow_queries'");
            if ($row = mysqli_fetch_assoc($result)) {
                $health['slow_queries'] = (int)$row['Value'];
            }
            
            // 查询缓存命中率
            $cache_hits = 0;
            $cache_inserts = 0;
            
            $result = mysqli_query($link, "SHOW STATUS LIKE 'Qcache_hits'");
            if ($row = mysqli_fetch_assoc($result)) {
                $cache_hits = (int)$row['Value'];
            }
            
            $result = mysqli_query($link, "SHOW STATUS LIKE 'Qcache_inserts'");
            if ($row = mysqli_fetch_assoc($result)) {
                $cache_inserts = (int)$row['Value'];
            }
            
            if (($cache_hits + $cache_inserts) > 0) {
                $health['query_cache_hit_rate'] = round(($cache_hits / ($cache_hits + $cache_inserts)) * 100, 2);
            }
            
        } else {
            $health['connection_status'] = 'disconnected';
        }
        
    } catch (Exception $e) {
        $health['connection_status'] = 'error';
        $health['error'] = $e->getMessage();
    }
    
    return $health;
}
?&gt;
            </div>
        </div>

        <div class="diagnostic-card warning">
            <h3>2. 系统性能监控</h3>
            
            <h4>服务器资源监控</h4>
            <div class="code-snippet php-code">
&lt;?php
// 系统资源监控
function getSystemMetrics() {
    $metrics = [];
    
    // CPU使用率
    if (function_exists('sys_getloadavg')) {
        $load = sys_getloadavg();
        $metrics['cpu_load_1min'] = $load[0];
        $metrics['cpu_load_5min'] = $load[1];
        $metrics['cpu_load_15min'] = $load[2];
    }
    
    // 内存使用情况
    $metrics['memory_usage'] = memory_get_usage(true);
    $metrics['memory_peak'] = memory_get_peak_usage(true);
    $metrics['memory_limit'] = ini_get('memory_limit');
    
    // 磁盘空间
    $metrics['disk_free'] = disk_free_space('/');
    $metrics['disk_total'] = disk_total_space('/');
    $metrics['disk_usage_percent'] = round((1 - ($metrics['disk_free'] / $metrics['disk_total'])) * 100, 2);
    
    // PHP配置检查
    $metrics['php_version'] = PHP_VERSION;
    $metrics['max_execution_time'] = ini_get('max_execution_time');
    $metrics['upload_max_filesize'] = ini_get('upload_max_filesize');
    $metrics['post_max_size'] = ini_get('post_max_size');
    
    return $metrics;
}

// 性能基准测试
function performanceBenchmark() {
    $start_time = microtime(true);
    
    // 数据库查询性能测试
    global $link;
    $db_start = microtime(true);
    mysqli_query($link, "SELECT COUNT(*) FROM tuqoa_gcproject");
    $db_time = (microtime(true) - $db_start) * 1000; // 转换为毫秒
    
    // 文件I/O性能测试
    $io_start = microtime(true);
    $test_file = '/tmp/performance_test.txt';
    file_put_contents($test_file, str_repeat('test', 1000));
    $content = file_get_contents($test_file);
    unlink($test_file);
    $io_time = (microtime(true) - $io_start) * 1000;
    
    $total_time = (microtime(true) - $start_time) * 1000;
    
    return [
        'database_query_time' => round($db_time, 2),
        'file_io_time' => round($io_time, 2),
        'total_benchmark_time' => round($total_time, 2)
    ];
}
?&gt;
            </div>
        </div>

        <div class="diagnostic-card success">
            <h3>3. 应用层健康检查</h3>
            
            <h4>关键功能验证</h4>
            <div class="code-snippet php-code">
&lt;?php
// 应用功能健康检查
function checkApplicationHealth() {
    global $link;
    $checks = [];
    
    // 检查核心数据表
    $core_tables = [
        'tuqoa_gcproject',
        'tuqoa_htgl', 
        'tuqoa_xmcztjb',
        'tuqoa_htsf',
        'tuqoa_userinfo'
    ];
    
    foreach ($core_tables as $table) {
        try {
            $result = mysqli_query($link, "SELECT COUNT(*) as count FROM `$table`");
            if ($result && $row = mysqli_fetch_assoc($result)) {
                $checks["table_$table"] = [
                    'status' => 'ok',
                    'record_count' => $row['count']
                ];
            } else {
                $checks["table_$table"] = [
                    'status' => 'error',
                    'error' => mysqli_error($link)
                ];
            }
        } catch (Exception $e) {
            $checks["table_$table"] = [
                'status' => 'error',
                'error' => $e->getMessage()
            ];
        }
    }
    
    // 检查关键目录权限
    $directories = [
        '/var/www/tuqoa/storage' => 'writable',
        '/var/www/tuqoa/logs' => 'writable',
        '/var/www/tuqoa/config' => 'readable'
    ];
    
    foreach ($directories as $dir => $required_permission) {
        if (is_dir($dir)) {
            $writable = is_writable($dir);
            $readable = is_readable($dir);
            
            $status = 'ok';
            if ($required_permission === 'writable' && !$writable) {
                $status = 'error';
            } elseif ($required_permission === 'readable' && !$readable) {
                $status = 'error';
            }
            
            $checks["dir_" . basename($dir)] = [
                'status' => $status,
                'path' => $dir,
                'writable' => $writable,
                'readable' => $readable
            ];
        } else {
            $checks["dir_" . basename($dir)] = [
                'status' => 'error',
                'error' => 'Directory not found'
            ];
        }
    }
    
    return $checks;
}
?&gt;
            </div>
        </div>

        <h2>📈 实时监控仪表板</h2>

        <div class="diagnostic-card info">
            <h3>监控指标展示</h3>
            
            <div class="metric-grid">
                <div class="metric-card">
                    <h4>数据库连接</h4>
                    <div class="metric-value" id="db-connections">--</div>
                    <p>活跃连接数</p>
                </div>
                <div class="metric-card">
                    <h4>系统负载</h4>
                    <div class="metric-value" id="system-load">--</div>
                    <p>1分钟平均负载</p>
                </div>
                <div class="metric-card">
                    <h4>内存使用</h4>
                    <div class="metric-value" id="memory-usage">--</div>
                    <p>内存使用率</p>
                </div>
                <div class="metric-card">
                    <h4>磁盘空间</h4>
                    <div class="metric-value" id="disk-usage">--</div>
                    <p>磁盘使用率</p>
                </div>
            </div>
            
            <h4>JavaScript实时更新</h4>
            <div class="code-snippet">
// 实时监控更新脚本
function updateMetrics() {
    fetch('/api/system/metrics')
        .then(response => response.json())
        .then(data => {
            document.getElementById('db-connections').textContent = 
                data.database.active_connections + '/' + data.database.max_connections;
            
            document.getElementById('system-load').textContent = 
                data.system.cpu_load_1min.toFixed(2);
            
            const memoryPercent = (data.system.memory_usage / data.system.memory_limit * 100).toFixed(1);
            document.getElementById('memory-usage').textContent = memoryPercent + '%';
            
            document.getElementById('disk-usage').textContent = 
                data.system.disk_usage_percent + '%';
            
            // 更新状态指示器
            updateStatusIndicators(data);
        })
        .catch(error => {
            console.error('Failed to update metrics:', error);
        });
}

// 每30秒更新一次
setInterval(updateMetrics, 30000);

// 页面加载时立即更新
document.addEventListener('DOMContentLoaded', updateMetrics);
            </div>
        </div>

        <h2>🚨 告警系统</h2>

        <div class="diagnostic-card critical">
            <h3>自动告警配置</h3>
            
            <table class="table">
                <thead>
                    <tr>
                        <th>监控项目</th>
                        <th>警告阈值</th>
                        <th>严重阈值</th>
                        <th>检查频率</th>
                        <th>通知方式</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>数据库连接数</td>
                        <td>&gt; 80%</td>
                        <td>&gt; 95%</td>
                        <td>1分钟</td>
                        <td>邮件+短信</td>
                    </tr>
                    <tr>
                        <td>系统负载</td>
                        <td>&gt; 2.0</td>
                        <td>&gt; 4.0</td>
                        <td>1分钟</td>
                        <td>邮件</td>
                    </tr>
                    <tr>
                        <td>内存使用率</td>
                        <td>&gt; 85%</td>
                        <td>&gt; 95%</td>
                        <td>5分钟</td>
                        <td>邮件</td>
                    </tr>
                    <tr>
                        <td>磁盘使用率</td>
                        <td>&gt; 80%</td>
                        <td>&gt; 90%</td>
                        <td>10分钟</td>
                        <td>邮件+短信</td>
                    </tr>
                    <tr>
                        <td>慢查询数量</td>
                        <td>&gt; 10/小时</td>
                        <td>&gt; 50/小时</td>
                        <td>1小时</td>
                        <td>邮件</td>
                    </tr>
                </tbody>
            </table>
            
            <h4>告警通知实现</h4>
            <div class="code-snippet php-code">
&lt;?php
// 告警系统
class AlertSystem {
    private $thresholds;
    private $notification_channels;
    
    public function __construct() {
        $this->thresholds = [
            'db_connections_warning' => 80,
            'db_connections_critical' => 95,
            'memory_usage_warning' => 85,
            'memory_usage_critical' => 95,
            'disk_usage_warning' => 80,
            'disk_usage_critical' => 90
        ];
        
        $this->notification_channels = [
            'email' => '<EMAIL>',
            'sms' => '+86-138-xxxx-xxxx'
        ];
    }
    
    public function checkAndAlert($metrics) {
        $alerts = [];
        
        // 检查数据库连接
        $db_usage = ($metrics['database']['active_connections'] / 
                    $metrics['database']['max_connections']) * 100;
        
        if ($db_usage > $this->thresholds['db_connections_critical']) {
            $alerts[] = [
                'level' => 'critical',
                'message' => "数据库连接使用率达到 {$db_usage}%",
                'metric' => 'database_connections'
            ];
        } elseif ($db_usage > $this->thresholds['db_connections_warning']) {
            $alerts[] = [
                'level' => 'warning',
                'message' => "数据库连接使用率达到 {$db_usage}%",
                'metric' => 'database_connections'
            ];
        }
        
        // 检查内存使用
        $memory_usage = ($metrics['system']['memory_usage'] / 
                        $this->parseMemoryLimit($metrics['system']['memory_limit'])) * 100;
        
        if ($memory_usage > $this->thresholds['memory_usage_critical']) {
            $alerts[] = [
                'level' => 'critical',
                'message' => "内存使用率达到 {$memory_usage}%",
                'metric' => 'memory_usage'
            ];
        }
        
        // 发送告警
        foreach ($alerts as $alert) {
            $this->sendAlert($alert);
        }
        
        return $alerts;
    }
    
    private function sendAlert($alert) {
        $subject = "[系统告警] " . strtoupper($alert['level']) . " - " . $alert['metric'];
        $message = $alert['message'] . "\n\n时间: " . date('Y-m-d H:i:s');
        
        // 发送邮件
        mail($this->notification_channels['email'], $subject, $message);
        
        // 记录告警日志
        error_log("[ALERT] " . $alert['level'] . ": " . $alert['message']);
    }
    
    private function parseMemoryLimit($limit) {
        $unit = strtolower(substr($limit, -1));
        $value = (int)$limit;
        
        switch ($unit) {
            case 'g': return $value * 1024 * 1024 * 1024;
            case 'm': return $value * 1024 * 1024;
            case 'k': return $value * 1024;
            default: return $value;
        }
    }
}
?&gt;
            </div>
        </div>

        <h2>📊 历史趋势分析</h2>

        <div class="diagnostic-card info">
            <h3>性能趋势记录</h3>
            
            <div class="code-snippet sql-code">
-- 创建监控数据表
CREATE TABLE system_metrics_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    metric_type VARCHAR(50) NOT NULL,
    metric_value DECIMAL(10,2) NOT NULL,
    additional_data JSON,
    INDEX idx_timestamp (timestamp),
    INDEX idx_metric_type (metric_type)
);

-- 插入监控数据
INSERT INTO system_metrics_history (metric_type, metric_value, additional_data) 
VALUES 
('db_connections', 45, '{"max_connections": 100, "usage_percent": 45}'),
('memory_usage', 78.5, '{"total_memory": "8GB", "used_memory": "6.28GB"}'),
('disk_usage', 65.2, '{"total_space": "100GB", "free_space": "34.8GB"}');
            </div>
            
            <div class="tip-box">
                <strong>💡 趋势分析建议：</strong>
                <ul>
                    <li>每5分钟记录一次关键指标</li>
                    <li>保留30天的详细数据</li>
                    <li>保留1年的每小时汇总数据</li>
                    <li>定期生成性能趋势报告</li>
                </ul>
            </div>
        </div>

        <h2>🔧 实施建议</h2>

        <div class="diagnostic-card success">
            <h3>部署步骤</h3>
            <ol>
                <li><strong>升级现有diagnose.php：</strong>集成新的监控功能</li>
                <li><strong>创建监控数据表：</strong>用于存储历史数据</li>
                <li><strong>配置告警系统：</strong>设置通知渠道和阈值</li>
                <li><strong>部署前端界面：</strong>实时监控仪表板</li>
                <li><strong>设置定时任务：</strong>自动化监控和告警</li>
                <li><strong>测试验证：</strong>确保所有功能正常工作</li>
            </ol>
            
            <div class="tip-box">
                <strong>🎯 预期效果：</strong>
                <ul>
                    <li>实时掌握系统健康状况</li>
                    <li>提前发现潜在问题</li>
                    <li>减少系统故障时间</li>
                    <li>提升运维效率</li>
                </ul>
            </div>
        </div>

        <div style="text-align: center; margin-top: 40px; color: #7f8c8d;">
            <p>📅 方案版本：v1.0</p>
            <p>🔄 最后更新：2025年8月7日</p>
            <p>📧 技术支持：<EMAIL></p>
        </div>
    </div>
</body>
</html>
