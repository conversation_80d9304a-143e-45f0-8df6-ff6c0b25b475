<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>问题工作汇总分析</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 10px;
            font-size: 2.5rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .subtitle {
            text-align: center;
            color: #7f8c8d;
            margin-bottom: 30px;
            font-size: 1.1rem;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }
        h3 {
            color: #2980b9;
            margin-top: 20px;
        }
        .analysis-card {
            background: #f8f9fa;
            padding: 20px;
            margin: 15px 0;
            border-radius: 10px;
            border-left: 4px solid #3498db;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .data-source { border-left-color: #28a745; }
        .feature { border-left-color: #ffc107; }
        .chart { border-left-color: #dc3545; }
        .business { border-left-color: #6f42c1; }
        .code-snippet {
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 10px 0;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .table th, .table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
            font-size: 14px;
        }
        .table th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            font-weight: 600;
        }
        .highlight-box {
            background: #e7f3ff;
            border: 1px solid #b8daff;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .tag {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            margin-right: 5px;
        }
        .tag-query { background: #28a745; color: white; }
        .tag-calc { background: #ffc107; color: #212529; }
        .tag-issue { background: #dc3545; color: white; }
        .tag-trend { background: #6f42c1; color: white; }
        .issue-flow {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .flow-step {
            background: white;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }
        .metric-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 问题工作汇总分析</h1>
        <p class="subtitle">wtgz.php - 工程项目问题跟踪和解决情况综合分析</p>
        
        <h2>📋 页面功能概述</h2>
        
        <div class="analysis-card business">
            <h3>核心功能</h3>
            <p>问题工作汇总页面是工程项目质量管理的重要工具，主要功能包括：</p>
            <ul>
                <li><strong>问题统计分析：</strong>全面统计工程、安全、质量等各类问题</li>
                <li><strong>解决进度跟踪：</strong>实时跟踪问题解决情况和处理进度</li>
                <li><strong>趋势分析：</strong>问题发现和解决的时间趋势分析</li>
                <li><strong>责任人工作量：</strong>各责任人的问题处理工作量统计</li>
            </ul>
            
            <div class="highlight-box">
                <strong>💡 管理价值：</strong>
                <ul>
                    <li>为项目管理提供问题处理效率数据</li>
                    <li>帮助识别问题高发区域和类型</li>
                    <li>支持质量管理决策和资源配置</li>
                    <li>提升项目整体质量管理水平</li>
                </ul>
            </div>
        </div>

        <h2>🗄️ 多源数据整合分析</h2>

        <div class="analysis-card data-source">
            <h3>核心数据表体系</h3>
            
            <h4><span class="tag tag-query">项目</span>tuqoa_gcproject - 工程项目表</h4>
            <div class="code-snippet">
-- 获取项目基本信息
SELECT gcname FROM tuqoa_gcproject WHERE id = '$gcid'

-- 获取活跃项目列表
SELECT id, gcname FROM `tuqoa_gcproject` 
WHERE `xmzt` not in ('完工项目','完工已结算','合同终止') 
order by id desc
            </div>
            <p><strong>用途：</strong>获取项目基本信息，作为问题分析的项目维度</p>
            
            <h4><span class="tag tag-query">监理</span>tuqoa_jlrz - 监理日志表</h4>
            <div class="code-snippet">
-- 统计监理问题解决情况
SELECT COUNT(*) as count FROM tuqoa_jlrz
WHERE DATE(kssj) = '$date'
AND rzlx REGEXP '(整改|完成|合格|解决|修复|处理完|已处理|通过|验收)' $project_filter
            </div>
            <p><strong>用途：</strong>监理日志中的问题记录，是问题统计的主要数据源</p>
            
            <h4><span class="tag tag-query">安全</span>tuqoa_aqjc - 安全检查表</h4>
            <div class="code-snippet">
-- 统计安全问题解决情况
SELECT COUNT(*) as count FROM tuqoa_aqjc
WHERE DATE(jcsj) = '$date'
AND (jcjg LIKE '%合格%' OR jcjg LIKE '%通过%' OR jcjg LIKE '%正常%') $project_filter
            </div>
            <p><strong>用途：</strong>安全检查记录，统计安全相关问题</p>
            
            <h4><span class="tag tag-query">质量</span>tuqoa_gcysjyx - 工程验收检验表</h4>
            <div class="code-snippet">
-- 统计质量问题解决情况
SELECT COUNT(*) as count FROM tuqoa_gcysjyx
WHERE DATE(optdt) = '$date'
AND (jcjg LIKE '%合格%' OR jcjg LIKE '%通过%' OR jcjg LIKE '%优秀%') $project_filter
            </div>
            <p><strong>用途：</strong>工程验收记录，统计质量相关问题</p>
        </div>

        <h2>📊 问题分析逻辑</h2>

        <div class="analysis-card feature">
            <h3>多维度问题统计</h3>
            
            <div class="issue-flow">
                <h4>问题统计流程</h4>
                
                <div class="flow-step">
                    <h5><span class="tag tag-calc">步骤1</span>基础问题统计</h5>
                    <div class="code-snippet">
// 初始化统计数据
$total_issues = 0;      // 总问题数
$resolved_issues = 0;   // 已解决问题数
$pending_issues = 0;    // 待处理问题数
$critical_issues = 0;   // 严重问题数

// 分类统计
$supervision_stats = ['total' => 0, 'resolved' => 0, 'pending' => 0, 'critical' => 0];
$safety_stats = ['total' => 0, 'resolved' => 0, 'pending' => 0, 'critical' => 0];
$quality_stats = ['total' => 0, 'resolved' => 0, 'pending' => 0, 'critical' => 0];
                    </div>
                </div>
                
                <div class="flow-step">
                    <h5><span class="tag tag-calc">步骤2</span>问题类型分布</h5>
                    <div class="code-snippet">
// 问题类型统计
$issue_types = [];
$issue_type_counts = [];

// 基于监理日志的问题分类
// 通过关键词匹配识别问题类型：
// - 工程问题：施工、材料、工艺等
// - 安全问题：安全、防护、隐患等  
// - 质量问题：质量、缺陷、不合格等
// - 施工管理：进度、协调、管理等
                    </div>
                </div>
                
                <div class="flow-step">
                    <h5><span class="tag tag-calc">步骤3</span>解决趋势分析</h5>
                    <div class="code-snippet">
// 最近30天的问题解决趋势
$resolution_trend_dates = [];
$resolution_trend_data = [];

for ($i = 29; $i >= 0; $i--) {
    $date = date('Y-m-d', strtotime("-$i days"));
    $resolution_trend_dates[] = date('m-d', strtotime($date));
    
    // 统计当天解决的各类问题
    $daily_total = 0;
    $daily_total += getDailySupervisionResolved($date, $project_filter);
    $daily_total += getDailySafetyResolved($date, $project_filter);
    $daily_total += getDailyQualityResolved($date, $project_filter);
    
    $resolution_trend_data[] = $daily_total;
}
                    </div>
                </div>
                
                <div class="flow-step">
                    <h5><span class="tag tag-calc">步骤4</span>责任人工作量</h5>
                    <div class="code-snippet">
// 各责任人的问题处理工作量
$personnel_workload = [];

// 基于监理日志统计各责任人处理的问题数量
// 通过监理人员字段或责任人字段进行统计
// 可以按照问题类型、解决状态等维度细分
                    </div>
                </div>
            </div>
        </div>

        <h2>📈 数据可视化特色</h2>

        <div class="analysis-card chart">
            <h3>多维度图表展示</h3>
            
            <div class="metric-grid">
                <div class="metric-card">
                    <h4>总问题数</h4>
                    <div class="metric-value">65</div>
                    <p>累计发现问题</p>
                </div>
                <div class="metric-card">
                    <h4>已解决</h4>
                    <div class="metric-value">38</div>
                    <p>问题解决数量</p>
                </div>
                <div class="metric-card">
                    <h4>待处理</h4>
                    <div class="metric-value">21</div>
                    <p>待处理问题</p>
                </div>
                <div class="metric-card">
                    <h4>严重问题</h4>
                    <div class="metric-value">6</div>
                    <p>需重点关注</p>
                </div>
            </div>
            
            <h4><span class="tag tag-issue">图表</span>问题类型分布饼图</h4>
            <div class="code-snippet">
// 问题类型分布饼图
var issueTypeChart = new Chart(ctx, {
    type: 'pie',
    data: {
        labels: ['工程问题', '安全问题', '质量问题', '施工管理'],
        datasets: [{
            data: [28, 18, 12, 7],
            backgroundColor: [
                'rgba(255, 99, 132, 0.8)',
                'rgba(54, 162, 235, 0.8)',
                'rgba(255, 205, 86, 0.8)',
                'rgba(75, 192, 192, 0.8)'
            ],
            borderColor: [
                'rgba(255, 99, 132, 1)',
                'rgba(54, 162, 235, 1)',
                'rgba(255, 205, 86, 1)',
                'rgba(75, 192, 192, 1)'
            ],
            borderWidth: 2
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        var percentage = ((context.parsed / totalIssues) * 100).toFixed(1);
                        return context.label + ': ' + context.parsed + 
                               ' (' + percentage + '%)';
                    }
                }
            }
        }
    }
});
            </div>
            
            <h4><span class="tag tag-trend">图表</span>问题解决趋势线图</h4>
            <div class="code-snippet">
// 30天问题解决趋势图
var trendChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: resolutionTrendDates,
        datasets: [{
            label: '每日解决问题数',
            data: resolutionTrendData,
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1,
            fill: true
        }]
    },
    options: {
        responsive: true,
        interaction: {
            mode: 'index',
            intersect: false,
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    stepSize: 1
                }
            }
        },
        plugins: {
            tooltip: {
                callbacks: {
                    title: function(context) {
                        return '日期: ' + context[0].label;
                    },
                    label: function(context) {
                        return '解决问题: ' + context.parsed.y + ' 个';
                    }
                }
            }
        }
    }
});
            </div>
        </div>

        <h2>💼 业务逻辑特色</h2>

        <div class="analysis-card business">
            <h3>智能问题识别</h3>
            
            <h4><span class="tag tag-issue">识别</span>问题状态判断逻辑</h4>
            <div class="code-snippet">
// 基于关键词的问题解决状态识别
$resolved_keywords = '(整改|完成|合格|解决|修复|处理完|已处理|通过|验收)';
$safety_resolved_keywords = '(合格|通过|正常)';
$quality_resolved_keywords = '(合格|通过|优秀)';

// 监理日志问题解决判断
$supervision_resolved_sql = "SELECT COUNT(*) as count FROM tuqoa_jlrz
                            WHERE rzlx REGEXP '$resolved_keywords' $project_filter";

// 安全问题解决判断
$safety_resolved_sql = "SELECT COUNT(*) as count FROM tuqoa_aqjc
                        WHERE (jcjg LIKE '%合格%' OR jcjg LIKE '%通过%' OR jcjg LIKE '%正常%') 
                        $project_filter";

// 质量问题解决判断
$quality_resolved_sql = "SELECT COUNT(*) as count FROM tuqoa_gcysjyx
                         WHERE (jcjg LIKE '%合格%' OR jcjg LIKE '%通过%' OR jcjg LIKE '%优秀%') 
                         $project_filter";
            </div>
            
            <h4>问题严重程度分类</h4>
            <table class="table">
                <thead>
                    <tr>
                        <th>严重程度</th>
                        <th>判断标准</th>
                        <th>处理优先级</th>
                        <th>预期处理时间</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>严重</td>
                        <td>影响安全、质量重大缺陷</td>
                        <td>最高</td>
                        <td>24小时内</td>
                    </tr>
                    <tr>
                        <td>中等</td>
                        <td>影响进度、一般质量问题</td>
                        <td>高</td>
                        <td>3天内</td>
                    </tr>
                    <tr>
                        <td>轻微</td>
                        <td>小问题、改进建议</td>
                        <td>中</td>
                        <td>1周内</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <h2>🎯 技术实现特点</h2>

        <div class="analysis-card">
            <h3>代码架构特色</h3>
            
            <h4>数据处理特点</h4>
            <ul>
                <li><strong>多表整合：</strong>整合监理、安全、质量三个维度的数据</li>
                <li><strong>正则匹配：</strong>使用REGEXP进行智能关键词匹配</li>
                <li><strong>时间序列：</strong>支持30天趋势分析</li>
                <li><strong>默认数据：</strong>无数据时提供合理的示例数据</li>
            </ul>
            
            <h4>查询优化特色</h4>
            <ul>
                <li><strong>条件筛选：</strong>支持项目维度的数据筛选</li>
                <li><strong>日期处理：</strong>灵活的日期范围查询</li>
                <li><strong>状态识别：</strong>基于内容的智能状态判断</li>
                <li><strong>聚合统计：</strong>多维度的COUNT统计</li>
            </ul>
            
            <h4>用户体验特色</h4>
            <ul>
                <li><strong>项目选择：</strong>支持特定项目的问题分析</li>
                <li><strong>实时更新：</strong>基于当前数据的实时统计</li>
                <li><strong>可视化丰富：</strong>多种图表类型展示</li>
                <li><strong>数据完整：</strong>确保页面始终有数据展示</li>
            </ul>
            
            <div class="warning-box">
                <strong>⚠️ 技术注意事项：</strong>
                <ul>
                    <li>正则表达式查询可能影响性能，建议优化</li>
                    <li>LIKE查询较多，建议添加全文索引</li>
                    <li>默认数据机制确保页面可用性</li>
                    <li>多表查询需要注意数据一致性</li>
                </ul>
            </div>
        </div>

        <h2>🚀 优化建议</h2>

        <div class="analysis-card">
            <h3>性能优化</h3>
            <ul>
                <li>为时间字段（kssj、jcsj、optdt）添加索引</li>
                <li>优化正则表达式查询，考虑使用状态字段</li>
                <li>为问题类型和状态字段添加索引</li>
                <li>实现查询结果缓存机制</li>
            </ul>
            
            <h3>功能增强</h3>
            <ul>
                <li>添加问题详情钻取功能</li>
                <li>支持问题处理流程跟踪</li>
                <li>增加问题预警和提醒功能</li>
                <li>添加问题处理效率分析</li>
            </ul>
            
            <h3>数据质量</h3>
            <ul>
                <li>标准化问题状态字段</li>
                <li>建立问题分类标准</li>
                <li>完善问题严重程度判断规则</li>
                <li>加强数据录入规范</li>
            </ul>
        </div>

        <div style="text-align: center; margin-top: 40px; color: #7f8c8d;">
            <p>📅 分析日期：2025年8月7日</p>
            <p>📊 页面重要性：⭐⭐⭐⭐ (质量管理核心)</p>
            <p>🔄 建议更新频率：实时</p>
        </div>
    </div>
</body>
</html>
