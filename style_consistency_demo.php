<?php
include '../config.php';
error_reporting(E_ALL);
ini_set('display_errors', 1);
session_start();

// 设置测试用的session变量
$_SESSION['xinhu_adminid'] = 1;
$_SESSION['xinhu_projectid'] = 1;
$_SESSION['xinhu_project'] = '测试项目';

// 获取项目列表
$projectListSql = "SELECT id, gcname FROM tuqoa_gcproject ORDER BY id DESC LIMIT 3";
$projectListResult = mysqli_query($link, $projectListSql);
$projectList = [];
if ($projectListResult) {
    while ($projectRow = mysqli_fetch_assoc($projectListResult)) {
        $projectList[] = $projectRow;
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>样式一致性展示</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/boxicons@2.0.7/css/boxicons.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            color: #2d3748;
            line-height: 1.6;
        }
        
        .navbar {
            background: #ffffff;
            border-bottom: 1px solid #e2e8f0;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            padding: 1rem 0;
        }
        
        .navbar-brand {
            font-weight: 600;
            font-size: 1.4rem;
            color: #1a202c !important;
            letter-spacing: -0.025em;
        }
        
        .navbar-brand i {
            color: #3182ce;
            margin-right: 8px;
        }
        
        .demo-card {
            background: #ffffff;
            border-radius: 12px;
            padding: 24px;
            margin: 20px 0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
            border: 1px solid #e2e8f0;
            animation: fadeInUp 0.6s ease-out;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        .comparison-item {
            background: #f8fafc;
            border-radius: 8px;
            padding: 20px;
            border: 1px solid #e2e8f0;
        }
        
        .demo-link {
            display: inline-block;
            padding: 10px 20px;
            background: #3182ce;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.2s ease;
            margin: 8px;
        }
        
        .demo-link:hover {
            background: #2c5aa0;
            color: white;
            text-decoration: none;
            transform: translateY(-1px);
        }
        
        .demo-link.success {
            background: #059669;
        }
        
        .demo-link.success:hover {
            background: #047857;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            align-items: center;
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
        
        .feature-list i {
            color: #059669;
            margin-right: 10px;
            font-size: 16px;
        }
        
        .color-palette {
            display: flex;
            gap: 10px;
            margin: 15px 0;
            flex-wrap: wrap;
        }
        
        .color-item {
            width: 60px;
            height: 60px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: 600;
            text-align: center;
            border: 1px solid #e2e8f0;
        }
        
        .highlight-box {
            background: linear-gradient(135deg, #ebf8ff 0%, #e6fffa 100%);
            border: 1px solid #bee3f8;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
        }
        
        .code-snippet {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 12px;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
            font-size: 13px;
            color: #2d3748;
            overflow-x: auto;
        }
        
        @media (max-width: 768px) {
            .comparison-grid {
                grid-template-columns: 1fr;
            }
            
            .color-palette {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg sticky-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bx bx-palette"></i>
                样式一致性展示
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text">
                    <i class="bx bx-time me-1"></i>
                    <?php echo date('Y-m-d H:i:s'); ?>
                </span>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="demo-card">
            <h1 class="text-center mb-4">🎨 样式一致性优化完成</h1>
            <p class="text-center lead text-muted">
                现在两个页面采用完全一致的设计风格和配色方案
            </p>
        </div>

        <div class="demo-card">
            <h2>✅ 统一的设计元素</h2>
            <div class="comparison-grid">
                <div class="comparison-item">
                    <h5>🎨 色彩方案</h5>
                    <div class="color-palette">
                        <div class="color-item" style="background: #f8fafc; color: #2d3748;">背景</div>
                        <div class="color-item" style="background: #ffffff; color: #2d3748;">卡片</div>
                        <div class="color-item" style="background: #3182ce; color: white;">主色</div>
                        <div class="color-item" style="background: #f7fafc; color: #2d3748;">表头</div>
                    </div>
                </div>
                <div class="comparison-item">
                    <h5>📝 字体系统</h5>
                    <div class="code-snippet">
-apple-system, BlinkMacSystemFont, 
'Segoe UI', 'PingFang SC', 
'Hiragino Sans GB', 'Microsoft YaHei'
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-card">
            <h2>🔄 一致性对比</h2>
            <div class="comparison-grid">
                <div class="comparison-item">
                    <h5>📊 myxmfymx.php - 项目费用明细</h5>
                    <ul class="feature-list">
                        <li><i class="bx bx-check"></i>统一的浅色背景</li>
                        <li><i class="bx bx-check"></i>一致的导航栏样式</li>
                        <li><i class="bx bx-check"></i>相同的表格设计</li>
                        <li><i class="bx bx-check"></i>统一的按钮风格</li>
                        <li><i class="bx bx-check"></i>一致的表单控件</li>
                    </ul>
                </div>
                <div class="comparison-item">
                    <h5>📈 myxmcbmx.php - 项目成本明细</h5>
                    <ul class="feature-list">
                        <li><i class="bx bx-check"></i>统一的浅色背景</li>
                        <li><i class="bx bx-check"></i>一致的导航栏样式</li>
                        <li><i class="bx bx-check"></i>相同的表格设计</li>
                        <li><i class="bx bx-check"></i>统一的按钮风格</li>
                        <li><i class="bx bx-check"></i>一致的表单控件</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="demo-card">
            <h2>🎯 核心改进内容</h2>
            <div class="row">
                <div class="col-md-6">
                    <h5>🎨 视觉统一</h5>
                    <ul>
                        <li><strong>背景色彩：</strong>统一的浅色渐变背景</li>
                        <li><strong>卡片设计：</strong>相同的圆角和阴影效果</li>
                        <li><strong>色彩搭配：</strong>一致的主色调和强调色</li>
                        <li><strong>间距系统：</strong>统一的内外边距规范</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h5>📝 交互一致</h5>
                    <ul>
                        <li><strong>按钮样式：</strong>相同的尺寸和悬停效果</li>
                        <li><strong>表单控件：</strong>统一的边框和焦点状态</li>
                        <li><strong>表格设计：</strong>一致的表头和行样式</li>
                        <li><strong>动画效果：</strong>相同的过渡和动画</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="demo-card">
            <h2>📋 技术实现细节</h2>
            <div class="highlight-box">
                <h6>主要修改内容：</h6>
                <ul class="mb-0">
                    <li>✅ 统一了背景色彩方案</li>
                    <li>✅ 同步了字体系统设置</li>
                    <li>✅ 一致化了表格样式</li>
                    <li>✅ 统一了按钮和表单控件</li>
                    <li>✅ 同步了响应式断点</li>
                    <li>✅ 一致化了动画效果</li>
                </ul>
            </div>
        </div>

        <div class="demo-card">
            <h2>🔗 查看统一效果</h2>
            <div class="text-center">
                <p class="mb-4">体验完全一致的设计风格：</p>
                
                <?php if (count($projectList) > 0): ?>
                    <?php $firstProject = $projectList[0]; ?>
                    <a href="myxmfymx.php?projectid=<?php echo $firstProject['id']; ?>" 
                       class="demo-link" target="_blank">
                        <i class="bx bx-money me-2"></i>项目费用明细表
                    </a>
                    <a href="myxmcbmx.php?projectid=<?php echo $firstProject['id']; ?>" 
                       class="demo-link success" target="_blank">
                        <i class="bx bx-calculator me-2"></i>项目成本明细表
                    </a>
                <?php else: ?>
                    <div class="alert alert-info">
                        <i class="bx bx-info-circle me-2"></i>
                        暂无可用项目数据，请确保数据库中有项目记录
                    </div>
                <?php endif; ?>
                
                <div class="mt-4">
                    <a href="myxmfymx.php" class="demo-link" target="_blank">
                        <i class="bx bx-money me-2"></i>费用明细（无参数）
                    </a>
                    <a href="myxmcbmx.php" class="demo-link success" target="_blank">
                        <i class="bx bx-calculator me-2"></i>成本明细（无参数）
                    </a>
                </div>
            </div>
        </div>

        <div class="demo-card">
            <h2>🎨 设计规范</h2>
            <div class="row">
                <div class="col-md-4">
                    <h6>色彩规范</h6>
                    <div class="code-snippet">
/* 主要颜色 */
--primary: #3182ce;
--background: #f8fafc;
--surface: #ffffff;
--text: #2d3748;
--border: #e2e8f0;
                    </div>
                </div>
                <div class="col-md-4">
                    <h6>间距规范</h6>
                    <div class="code-snippet">
/* 间距系统 */
--space-1: 4px;
--space-2: 8px;
--space-3: 12px;
--space-4: 16px;
--space-5: 20px;
--space-6: 24px;
                    </div>
                </div>
                <div class="col-md-4">
                    <h6>圆角规范</h6>
                    <div class="code-snippet">
/* 圆角系统 */
--radius-sm: 6px;
--radius-md: 8px;
--radius-lg: 12px;
--radius-xl: 16px;
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-card">
            <h2>✅ 优化成果</h2>
            <div class="highlight-box">
                <h5 class="text-center mb-3">🎉 样式一致性优化完成！</h5>
                <div class="row text-center">
                    <div class="col-md-3">
                        <h6>视觉统一</h6>
                        <p class="small text-muted">完全一致的设计风格</p>
                    </div>
                    <div class="col-md-3">
                        <h6>交互一致</h6>
                        <p class="small text-muted">统一的用户体验</p>
                    </div>
                    <div class="col-md-3">
                        <h6>代码规范</h6>
                        <p class="small text-muted">标准化的样式代码</p>
                    </div>
                    <div class="col-md-3">
                        <h6>维护性</h6>
                        <p class="small text-muted">易于维护和扩展</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
