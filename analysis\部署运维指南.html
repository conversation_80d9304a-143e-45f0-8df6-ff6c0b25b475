<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>部署运维指南</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 10px;
            font-size: 2.5rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .subtitle {
            text-align: center;
            color: #7f8c8d;
            margin-bottom: 30px;
            font-size: 1.1rem;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }
        h3 {
            color: #2980b9;
            margin-top: 20px;
        }
        .guide-section {
            background: #f8f9fa;
            padding: 20px;
            margin: 15px 0;
            border-radius: 10px;
            border-left: 4px solid #3498db;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .deployment { border-left-color: #28a745; }
        .monitoring { border-left-color: #ffc107; }
        .maintenance { border-left-color: #dc3545; }
        .security { border-left-color: #6f42c1; }
        .code-snippet {
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 10px 0;
        }
        .bash { background: #1e1e1e; }
        .config { background: #2d3748; }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .table th, .table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
            font-size: 14px;
        }
        .table th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            font-weight: 600;
        }
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .success-box {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .info-box {
            background: #e7f3ff;
            border: 1px solid #b8daff;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .checklist {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .checklist ul {
            list-style-type: none;
            padding-left: 0;
        }
        .checklist li {
            padding: 5px 0;
        }
        .checklist li:before {
            content: "☐ ";
            font-weight: bold;
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 部署运维指南</h1>
        <p class="subtitle">系统部署、监控和维护的完整指南</p>
        
        <h2>📋 系统要求</h2>
        
        <table class="table">
            <thead>
                <tr>
                    <th>组件</th>
                    <th>最低要求</th>
                    <th>推荐配置</th>
                    <th>说明</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>操作系统</td>
                    <td>Linux/Windows Server</td>
                    <td>Ubuntu 20.04 LTS</td>
                    <td>支持主流Linux发行版</td>
                </tr>
                <tr>
                    <td>Web服务器</td>
                    <td>Apache 2.4+</td>
                    <td>Nginx 1.18+</td>
                    <td>推荐使用Nginx</td>
                </tr>
                <tr>
                    <td>PHP</td>
                    <td>PHP 7.4+</td>
                    <td>PHP 8.1+</td>
                    <td>需要mysqli扩展</td>
                </tr>
                <tr>
                    <td>数据库</td>
                    <td>MySQL 5.7+</td>
                    <td>MySQL 8.0+</td>
                    <td>或MariaDB 10.3+</td>
                </tr>
                <tr>
                    <td>内存</td>
                    <td>4GB</td>
                    <td>8GB+</td>
                    <td>根据并发用户数调整</td>
                </tr>
                <tr>
                    <td>存储</td>
                    <td>50GB</td>
                    <td>100GB+ SSD</td>
                    <td>包含日志和备份空间</td>
                </tr>
            </tbody>
        </table>

        <h2>🏗️ 部署步骤</h2>

        <div class="guide-section deployment">
            <h3>1. 环境准备</h3>
            
            <h4>安装基础软件</h4>
            <div class="code-snippet bash">
# Ubuntu/Debian
sudo apt update
sudo apt install nginx mysql-server php8.1-fpm php8.1-mysqli php8.1-mbstring php8.1-xml

# CentOS/RHEL
sudo yum install epel-release
sudo yum install nginx mysql-server php-fpm php-mysqli php-mbstring php-xml

# 启动服务
sudo systemctl enable nginx mysql php8.1-fpm
sudo systemctl start nginx mysql php8.1-fpm
            </div>
            
            <h4>配置MySQL</h4>
            <div class="code-snippet bash">
# 安全配置
sudo mysql_secure_installation

# 创建数据库和用户
mysql -u root -p
CREATE DATABASE tuqoa CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'tuqoa_user'@'localhost' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON tuqoa.* TO 'tuqoa_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
            </div>
        </div>

        <div class="guide-section deployment">
            <h3>2. 应用部署</h3>
            
            <h4>代码部署</h4>
            <div class="code-snippet bash">
# 创建应用目录
sudo mkdir -p /var/www/tuqoa
sudo chown -R www-data:www-data /var/www/tuqoa

# 部署代码（示例）
cd /var/www/tuqoa
sudo git clone https://github.com/company/tuqoa-system.git .
# 或者上传代码包
sudo unzip tuqoa-system.zip

# 设置权限
sudo chown -R www-data:www-data /var/www/tuqoa
sudo chmod -R 755 /var/www/tuqoa
sudo chmod -R 777 /var/www/tuqoa/storage
sudo chmod -R 777 /var/www/tuqoa/logs
            </div>
            
            <h4>Nginx配置</h4>
            <div class="code-snippet config">
# /etc/nginx/sites-available/tuqoa
server {
    listen 80;
    server_name tuqoa.company.com;
    root /var/www/tuqoa/public;
    index index.php index.html;

    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;

    # PHP处理
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # 静态文件缓存
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # 安全配置
    location ~ /\. {
        deny all;
    }
    
    location ~ /(config|logs|storage)/ {
        deny all;
    }
}
            </div>
            
            <div class="code-snippet bash">
# 启用站点
sudo ln -s /etc/nginx/sites-available/tuqoa /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
            </div>
        </div>

        <div class="guide-section deployment">
            <h3>3. 数据库初始化</h3>
            
            <div class="code-snippet bash">
# 导入数据库结构
mysql -u tuqoa_user -p tuqoa < database/schema.sql

# 导入初始数据（如果有）
mysql -u tuqoa_user -p tuqoa < database/initial_data.sql

# 创建建议的索引
mysql -u tuqoa_user -p tuqoa < database/indexes.sql
            </div>
            
            <h4>索引优化脚本</h4>
            <div class="code-snippet config">
-- database/indexes.sql
-- 核心表索引优化
CREATE INDEX idx_gcproject_xmzt ON tuqoa_gcproject(xmzt);
CREATE INDEX idx_gcproject_qdsj ON tuqoa_gcproject(qdsj);
CREATE INDEX idx_htgl_projectid ON tuqoa_htgl(projectid);
CREATE INDEX idx_htgl_qdsj ON tuqoa_htgl(qdsj);
CREATE INDEX idx_xmcztjb_projectid ON tuqoa_xmcztjb(projectid);
CREATE INDEX idx_xmcztjb_sbrq ON tuqoa_xmcztjb(sbrq);
CREATE INDEX idx_htsf_projectid ON tuqoa_htsf(projectid);
CREATE INDEX idx_htsf_yjsj ON tuqoa_htsf(yjsj);
CREATE INDEX idx_htsf_sksj ON tuqoa_htsf(sksj);
            </div>
        </div>

        <h2>📊 监控配置</h2>

        <div class="guide-section monitoring">
            <h3>1. 系统监控</h3>
            
            <h4>安装监控工具</h4>
            <div class="code-snippet bash">
# 安装Prometheus和Grafana
wget https://github.com/prometheus/prometheus/releases/download/v2.40.0/prometheus-2.40.0.linux-amd64.tar.gz
tar xvfz prometheus-*.tar.gz
sudo mv prometheus-* /opt/prometheus

# 安装Node Exporter
wget https://github.com/prometheus/node_exporter/releases/download/v1.5.0/node_exporter-1.5.0.linux-amd64.tar.gz
tar xvfz node_exporter-*.tar.gz
sudo mv node_exporter-* /opt/node_exporter
            </div>
            
            <h4>Prometheus配置</h4>
            <div class="code-snippet config">
# /opt/prometheus/prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'node'
    static_configs:
      - targets: ['localhost:9100']
  
  - job_name: 'mysql'
    static_configs:
      - targets: ['localhost:9104']
  
  - job_name: 'nginx'
    static_configs:
      - targets: ['localhost:9113']
            </div>
        </div>

        <div class="guide-section monitoring">
            <h3>2. 应用监控</h3>
            
            <h4>PHP错误日志监控</h4>
            <div class="code-snippet bash">
# 配置PHP错误日志
sudo nano /etc/php/8.1/fpm/php.ini

# 添加配置
log_errors = On
error_log = /var/log/php/error.log
error_reporting = E_ALL & ~E_DEPRECATED & ~E_STRICT

# 创建日志目录
sudo mkdir -p /var/log/php
sudo chown www-data:www-data /var/log/php
            </div>
            
            <h4>数据库性能监控</h4>
            <div class="code-snippet config">
-- 启用慢查询日志
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 2;
SET GLOBAL slow_query_log_file = '/var/log/mysql/slow.log';

-- 监控查询
SELECT * FROM information_schema.processlist 
WHERE command != 'Sleep' AND time > 5;

-- 查看慢查询
SELECT * FROM mysql.slow_log 
WHERE start_time > DATE_SUB(NOW(), INTERVAL 1 HOUR)
ORDER BY query_time DESC;
            </div>
        </div>

        <h2>🔧 日常维护</h2>

        <div class="guide-section maintenance">
            <h3>1. 备份策略</h3>
            
            <h4>数据库备份脚本</h4>
            <div class="code-snippet bash">
#!/bin/bash
# /opt/scripts/backup_database.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/mysql"
DB_NAME="tuqoa"
DB_USER="tuqoa_user"
DB_PASS="secure_password"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 执行备份
mysqldump -u$DB_USER -p$DB_PASS $DB_NAME > $BACKUP_DIR/tuqoa_$DATE.sql

# 压缩备份文件
gzip $BACKUP_DIR/tuqoa_$DATE.sql

# 删除7天前的备份
find $BACKUP_DIR -name "tuqoa_*.sql.gz" -mtime +7 -delete

echo "Database backup completed: tuqoa_$DATE.sql.gz"
            </div>
            
            <h4>设置定时备份</h4>
            <div class="code-snippet bash">
# 添加到crontab
sudo crontab -e

# 每天凌晨2点执行备份
0 2 * * * /opt/scripts/backup_database.sh >> /var/log/backup.log 2>&1

# 每周日凌晨3点执行完整备份
0 3 * * 0 /opt/scripts/full_backup.sh >> /var/log/backup.log 2>&1
            </div>
        </div>

        <div class="guide-section maintenance">
            <h3>2. 日志管理</h3>
            
            <h4>日志轮转配置</h4>
            <div class="code-snippet config">
# /etc/logrotate.d/tuqoa
/var/log/tuqoa/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        systemctl reload nginx
        systemctl reload php8.1-fpm
    endscript
}
            </div>
            
            <h4>清理脚本</h4>
            <div class="code-snippet bash">
#!/bin/bash
# /opt/scripts/cleanup.sh

# 清理临时文件
find /tmp -name "php*" -mtime +1 -delete
find /var/tmp -name "sess_*" -mtime +1 -delete

# 清理应用缓存
rm -rf /var/www/tuqoa/storage/cache/*
rm -rf /var/www/tuqoa/storage/logs/*.log

# 清理Nginx访问日志（保留30天）
find /var/log/nginx -name "*.log" -mtime +30 -delete

echo "Cleanup completed at $(date)"
            </div>
        </div>

        <h2>🔒 安全配置</h2>

        <div class="guide-section security">
            <h3>1. 防火墙配置</h3>
            
            <div class="code-snippet bash">
# 配置UFW防火墙
sudo ufw enable
sudo ufw default deny incoming
sudo ufw default allow outgoing

# 允许必要端口
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS

# 限制SSH连接
sudo ufw limit 22/tcp

# 查看状态
sudo ufw status verbose
            </div>
        </div>

        <div class="guide-section security">
            <h3>2. SSL证书配置</h3>
            
            <div class="code-snippet bash">
# 安装Certbot
sudo apt install certbot python3-certbot-nginx

# 获取SSL证书
sudo certbot --nginx -d tuqoa.company.com

# 自动续期
sudo crontab -e
0 12 * * * /usr/bin/certbot renew --quiet
            </div>
        </div>

        <h2>📋 运维检查清单</h2>

        <div class="checklist">
            <h3>每日检查</h3>
            <ul>
                <li>检查系统资源使用情况（CPU、内存、磁盘）</li>
                <li>查看应用错误日志</li>
                <li>检查数据库连接状态</li>
                <li>验证备份是否正常执行</li>
                <li>检查网站可访问性</li>
            </ul>
        </div>

        <div class="checklist">
            <h3>每周检查</h3>
            <ul>
                <li>分析慢查询日志</li>
                <li>检查磁盘空间使用情况</li>
                <li>更新系统安全补丁</li>
                <li>检查SSL证书有效期</li>
                <li>清理临时文件和日志</li>
            </ul>
        </div>

        <div class="checklist">
            <h3>每月检查</h3>
            <ul>
                <li>数据库性能优化</li>
                <li>备份恢复测试</li>
                <li>安全扫描和漏洞检查</li>
                <li>系统性能基准测试</li>
                <li>用户访问日志分析</li>
            </ul>
        </div>

        <h2>🚨 故障排除</h2>

        <div class="guide-section">
            <h3>常见问题及解决方案</h3>
            
            <table class="table">
                <thead>
                    <tr>
                        <th>问题</th>
                        <th>可能原因</th>
                        <th>解决方案</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>页面加载缓慢</td>
                        <td>数据库查询慢</td>
                        <td>检查慢查询日志，优化SQL</td>
                    </tr>
                    <tr>
                        <td>500错误</td>
                        <td>PHP错误</td>
                        <td>查看PHP错误日志</td>
                    </tr>
                    <tr>
                        <td>数据库连接失败</td>
                        <td>连接数超限</td>
                        <td>增加max_connections参数</td>
                    </tr>
                    <tr>
                        <td>磁盘空间不足</td>
                        <td>日志文件过大</td>
                        <td>清理日志，配置轮转</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="warning-box">
            <h3>⚠️ 重要提醒</h3>
            <ul>
                <li>生产环境操作前务必备份</li>
                <li>重要配置修改需要测试验证</li>
                <li>定期检查监控告警</li>
                <li>保持系统和软件更新</li>
            </ul>
        </div>

        <h2>📱 快速命令参考</h2>

        <div class="guide-section">
            <h3>常用运维命令</h3>
            <div class="code-snippet bash">
# 查看系统状态
sudo systemctl status nginx mysql php8.1-fpm

# 重启服务
sudo systemctl restart nginx
sudo systemctl restart mysql
sudo systemctl restart php8.1-fpm

# 查看日志
sudo tail -f /var/log/nginx/error.log
sudo tail -f /var/log/mysql/error.log
sudo tail -f /var/log/php/error.log

# 数据库操作
mysql -u tuqoa_user -p tuqoa
SHOW PROCESSLIST;
SHOW STATUS LIKE 'Threads_connected';

# 磁盘空间检查
df -h
du -sh /var/www/tuqoa
du -sh /var/log/*

# 性能监控
top
htop
iotop
            </div>
        </div>

        <div style="text-align: center; margin-top: 40px; color: #7f8c8d;">
            <p>📅 文档版本：v1.0</p>
            <p>🔄 最后更新：2025年8月7日</p>
            <p>📞 紧急联系：<EMAIL></p>
        </div>
    </div>
</body>
</html>
