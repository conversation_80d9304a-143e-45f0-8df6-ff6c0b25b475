<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目合同到账明细分析</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 10px;
            font-size: 2.5rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .subtitle {
            text-align: center;
            color: #7f8c8d;
            margin-bottom: 30px;
            font-size: 1.1rem;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }
        h3 {
            color: #2980b9;
            margin-top: 20px;
        }
        .analysis-card {
            background: #f8f9fa;
            padding: 20px;
            margin: 15px 0;
            border-radius: 10px;
            border-left: 4px solid #3498db;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .data-source { border-left-color: #28a745; }
        .feature { border-left-color: #ffc107; }
        .chart { border-left-color: #dc3545; }
        .business { border-left-color: #6f42c1; }
        .code-snippet {
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 10px 0;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .table th, .table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
            font-size: 14px;
        }
        .table th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            font-weight: 600;
        }
        .highlight-box {
            background: #e7f3ff;
            border: 1px solid #b8daff;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .tag {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            margin-right: 5px;
        }
        .tag-query { background: #28a745; color: white; }
        .tag-calc { background: #ffc107; color: #212529; }
        .tag-chart { background: #dc3545; color: white; }
        .tag-filter { background: #6f42c1; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <h1>💰 项目合同到账明细分析</h1>
        <p class="subtitle">xmhtdzmx.php - 项目收款情况详细分析页面</p>
        
        <h2>📋 页面功能概述</h2>
        
        <div class="analysis-card business">
            <h3>核心功能</h3>
            <p>项目合同到账明细页面是财务管理的重要工具，主要用于：</p>
            <ul>
                <li><strong>项目收款分析：</strong>查看具体项目的收款计划和实际到账情况</li>
                <li><strong>月度收款对比：</strong>对比预计收款与实际收款的差异</li>
                <li><strong>收款进度监控：</strong>跟踪项目收款的完成进度</li>
                <li><strong>合同执行分析：</strong>分析合同的执行和收款情况</li>
            </ul>
            
            <div class="highlight-box">
                <strong>💡 业务价值：</strong>
                <ul>
                    <li>帮助财务部门监控项目收款进度</li>
                    <li>为现金流管理提供数据支撑</li>
                    <li>识别收款风险和延期项目</li>
                    <li>支持财务决策和资金规划</li>
                </ul>
            </div>
        </div>

        <h2>🗄️ 数据来源分析</h2>

        <div class="analysis-card data-source">
            <h3>主要数据表</h3>
            
            <h4><span class="tag tag-query">核心</span>tuqoa_gcproject - 工程项目表</h4>
            <div class="code-snippet">
-- 获取活跃项目列表
SELECT * FROM `tuqoa_gcproject` 
WHERE `xmzt` not in ('完工项目','完工已结算','合同终止') 
order by id desc

-- 获取特定项目信息
SELECT * FROM `tuqoa_gcproject` WHERE `id` = $gcid
            </div>
            <p><strong>用途：</strong>获取项目基本信息，包括项目名称、状态、签订时间等</p>
            
            <h4><span class="tag tag-query">核心</span>tuqoa_htgl - 合同管理表</h4>
            <div class="code-snippet">
-- 计算合同总额
SELECT IFNULL(SUM(fwf), 0) as total 
FROM `tuqoa_htgl` 
WHERE `projectid` = $gcid

-- 获取项目合同详情
SELECT * FROM `tuqoa_htgl` WHERE `projectid`=$gcid
            </div>
            <p><strong>用途：</strong>获取项目的合同信息和服务费总额</p>
            
            <h4><span class="tag tag-query">核心</span>tuqoa_htsf - 合同收费表</h4>
            <div class="code-snippet">
-- 计算已收款总额
SELECT IFNULL(SUM(ysje), 0) as total 
FROM `tuqoa_htsf` 
WHERE `projectid` = $gcid

-- 查询月度预计收款
SELECT IFNULL(SUM(yjje), 0) as amount 
FROM `tuqoa_htsf`
WHERE `projectid` = $gcid AND DATE_FORMAT(yjsj, '%m') = '$month'

-- 查询月度实际收款
SELECT IFNULL(SUM(ysje), 0) as amount 
FROM `tuqoa_htsf`
WHERE `projectid` = $gcid AND DATE_FORMAT(sksj, '%m') = '$month'
            </div>
            <p><strong>用途：</strong>核心收费数据，包括预计收款、实际收款、收款时间等</p>
        </div>

        <h2>📊 数据处理逻辑</h2>

        <div class="analysis-card feature">
            <h3>收款统计计算</h3>
            
            <h4><span class="tag tag-calc">计算</span>收款完成率</h4>
            <div class="code-snippet">
// PHP计算逻辑
$completion_rate = 0;
if ($contract_total > 0) {
    $completion_rate = round(($received_total / $contract_total) * 100, 2);
}

// 状态判断
if ($completion_rate >= 100) {
    $status = "已完成";
    $status_class = "success";
} elseif ($completion_rate >= 80) {
    $status = "接近完成";
    $status_class = "warning";
} else {
    $status = "进行中";
    $status_class = "info";
}
            </div>
            
            <h4><span class="tag tag-calc">计算</span>月度收款分析</h4>
            <div class="code-snippet">
// 12个月的收款数据循环
for ($i = 1; $i <= 12; $i++) {
    $month = str_pad($i, 2, '0', STR_PAD_LEFT);
    
    // 查询当月预计收款
    $planned_sql = "SELECT IFNULL(SUM(yjje), 0) as amount FROM `tuqoa_htsf`
                   WHERE `projectid` = $gcid AND DATE_FORMAT(yjsj, '%m') = '$month'";
    
    // 查询当月实际收款
    $actual_sql = "SELECT IFNULL(SUM(ysje), 0) as amount FROM `tuqoa_htsf`
                  WHERE `projectid` = $gcid AND DATE_FORMAT(sksj, '%m') = '$month'";
    
    // 计算完成率
    $monthly_rate = ($planned > 0) ? round(($actual / $planned) * 100, 2) : 0;
}
            </div>
        </div>

        <h2>📈 图表和可视化</h2>

        <div class="analysis-card chart">
            <h3>Chart.js图表实现</h3>
            
            <h4><span class="tag tag-chart">图表</span>月度收款对比图</h4>
            <div class="code-snippet">
// 月度收款对比柱状图
var ctx = document.getElementById('monthlyChart').getContext('2d');
var monthlyChart = new Chart(ctx, {
    type: 'bar',
    data: {
        labels: ['1月', '2月', '3月', '4月', '5月', '6月', 
                '7月', '8月', '9月', '10月', '11月', '12月'],
        datasets: [{
            label: '预计收款',
            data: plannedData,
            backgroundColor: 'rgba(54, 162, 235, 0.6)',
            borderColor: 'rgba(54, 162, 235, 1)',
            borderWidth: 1
        }, {
            label: '实际收款',
            data: actualData,
            backgroundColor: 'rgba(75, 192, 192, 0.6)',
            borderColor: 'rgba(75, 192, 192, 1)',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return '¥' + value.toLocaleString();
                    }
                }
            }
        },
        plugins: {
            tooltip: {
                callbacks: {
                    label: function(context) {
                        return context.dataset.label + ': ¥' + 
                               context.parsed.y.toLocaleString();
                    }
                }
            }
        }
    }
});
            </div>
            
            <h4><span class="tag tag-chart">图表</span>收款进度环形图</h4>
            <div class="code-snippet">
// 收款进度环形图
var progressCtx = document.getElementById('progressChart').getContext('2d');
var progressChart = new Chart(progressCtx, {
    type: 'doughnut',
    data: {
        labels: ['已收款', '未收款'],
        datasets: [{
            data: [receivedAmount, remainingAmount],
            backgroundColor: [
                'rgba(75, 192, 192, 0.8)',
                'rgba(255, 99, 132, 0.8)'
            ],
            borderColor: [
                'rgba(75, 192, 192, 1)',
                'rgba(255, 99, 132, 1)'
            ],
            borderWidth: 2
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        var percentage = ((context.parsed / totalAmount) * 100).toFixed(1);
                        return context.label + ': ¥' + context.parsed.toLocaleString() + 
                               ' (' + percentage + '%)';
                    }
                }
            }
        }
    }
});
            </div>
        </div>

        <h2>🔍 页面交互功能</h2>

        <div class="analysis-card feature">
            <h3>用户交互特性</h3>
            
            <table class="table">
                <thead>
                    <tr>
                        <th>功能模块</th>
                        <th>交互方式</th>
                        <th>实现技术</th>
                        <th>用户体验</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>项目选择</td>
                        <td>下拉选择框</td>
                        <td>Bootstrap Select</td>
                        <td>支持搜索和筛选</td>
                    </tr>
                    <tr>
                        <td>数据展示</td>
                        <td>响应式表格</td>
                        <td>Bootstrap Table</td>
                        <td>自适应屏幕大小</td>
                    </tr>
                    <tr>
                        <td>图表交互</td>
                        <td>悬停提示</td>
                        <td>Chart.js Tooltips</td>
                        <td>详细数据展示</td>
                    </tr>
                    <tr>
                        <td>状态指示</td>
                        <td>颜色编码</td>
                        <td>Bootstrap Colors</td>
                        <td>直观的状态识别</td>
                    </tr>
                </tbody>
            </table>
            
            <h4><span class="tag tag-filter">筛选</span>项目筛选逻辑</h4>
            <div class="code-snippet">
// 项目状态筛选
WHERE `xmzt` not in ('完工项目','完工已结算','合同终止')

// 排除的状态说明：
// - 完工项目：已经完工的项目
// - 完工已结算：完工且已完成结算的项目  
// - 合同终止：合同已终止的项目

// 只显示活跃项目，便于收款管理
            </div>
        </div>

        <h2>💼 业务逻辑分析</h2>

        <div class="analysis-card business">
            <h3>收款管理流程</h3>
            
            <h4>1. 项目收款计划</h4>
            <ul>
                <li>基于合同约定制定收款计划</li>
                <li>按月度分解收款目标</li>
                <li>设置收款里程碑和节点</li>
            </ul>
            
            <h4>2. 收款执行监控</h4>
            <ul>
                <li>实时跟踪实际收款情况</li>
                <li>对比计划与实际的差异</li>
                <li>识别收款延期和风险</li>
            </ul>
            
            <h4>3. 收款分析报告</h4>
            <ul>
                <li>生成月度收款分析报告</li>
                <li>计算收款完成率和趋势</li>
                <li>为管理决策提供数据支撑</li>
            </ul>
            
            <div class="warning-box">
                <strong>⚠️ 关键业务规则：</strong>
                <ul>
                    <li>只统计活跃项目的收款情况</li>
                    <li>预计收款时间以yjsj字段为准</li>
                    <li>实际收款时间以sksj字段为准</li>
                    <li>收款金额以ysje字段为准</li>
                </ul>
            </div>
        </div>

        <h2>🎯 技术特点</h2>

        <div class="analysis-card">
            <h3>代码特色</h3>
            
            <h4>数据处理优势</h4>
            <ul>
                <li><strong>聚合查询：</strong>使用SUM和IFNULL函数确保数据准确性</li>
                <li><strong>时间处理：</strong>使用DATE_FORMAT进行月份筛选</li>
                <li><strong>条件筛选：</strong>灵活的项目状态筛选逻辑</li>
                <li><strong>数据安全：</strong>使用参数化查询防止SQL注入</li>
            </ul>
            
            <h4>前端展示特色</h4>
            <ul>
                <li><strong>响应式设计：</strong>适配各种设备屏幕</li>
                <li><strong>图表可视化：</strong>直观的数据展示</li>
                <li><strong>交互友好：</strong>良好的用户体验</li>
                <li><strong>状态指示：</strong>清晰的视觉反馈</li>
            </ul>
        </div>

        <h2>🚀 优化建议</h2>

        <div class="analysis-card">
            <h3>性能优化</h3>
            <ul>
                <li>为tuqoa_htsf表的projectid和yjsj字段添加复合索引</li>
                <li>优化月度查询，考虑使用预计算的月度汇总表</li>
                <li>实现查询结果缓存，减少重复计算</li>
            </ul>
            
            <h3>功能增强</h3>
            <ul>
                <li>添加收款预警功能，提醒逾期收款</li>
                <li>支持批量导出收款明细报表</li>
                <li>增加收款趋势预测功能</li>
                <li>添加收款风险评估指标</li>
            </ul>
            
            <h3>用户体验</h3>
            <ul>
                <li>添加数据加载动画</li>
                <li>支持表格数据排序和筛选</li>
                <li>增加打印友好的报表格式</li>
                <li>添加数据导出功能</li>
            </ul>
        </div>

        <div style="text-align: center; margin-top: 40px; color: #7f8c8d;">
            <p>📅 分析日期：2025年8月7日</p>
            <p>📊 页面重要性：⭐⭐⭐⭐⭐ (财务核心功能)</p>
            <p>🔄 建议更新频率：实时</p>
        </div>
    </div>
</body>
</html>
