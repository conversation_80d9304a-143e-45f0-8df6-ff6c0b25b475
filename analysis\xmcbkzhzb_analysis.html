<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目成本控制汇总表分析</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 10px;
            font-size: 2.5rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .subtitle {
            text-align: center;
            color: #7f8c8d;
            margin-bottom: 30px;
            font-size: 1.1rem;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }
        h3 {
            color: #2980b9;
            margin-top: 20px;
        }
        .analysis-card {
            background: #f8f9fa;
            padding: 20px;
            margin: 15px 0;
            border-radius: 10px;
            border-left: 4px solid #3498db;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .data-source { border-left-color: #28a745; }
        .feature { border-left-color: #ffc107; }
        .chart { border-left-color: #dc3545; }
        .business { border-left-color: #6f42c1; }
        .code-snippet {
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 10px 0;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .table th, .table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
            font-size: 14px;
        }
        .table th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            font-weight: 600;
        }
        .highlight-box {
            background: #e7f3ff;
            border: 1px solid #b8daff;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .tag {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            margin-right: 5px;
        }
        .tag-query { background: #28a745; color: white; }
        .tag-calc { background: #ffc107; color: #212529; }
        .tag-cost { background: #dc3545; color: white; }
        .tag-control { background: #6f42c1; color: white; }
        .cost-flow {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .flow-step {
            background: white;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 项目成本控制汇总表分析</h1>
        <p class="subtitle">xmcbkzhzb.php - 企业项目成本全面监控和控制中心</p>
        
        <h2>📋 页面功能概述</h2>
        
        <div class="analysis-card business">
            <h3>核心功能</h3>
            <p>项目成本控制汇总表是企业成本管理的核心工具，提供全方位的成本监控：</p>
            <ul>
                <li><strong>多维度成本统计：</strong>月度、年度、累计成本的全面统计</li>
                <li><strong>成本结构分析：</strong>人工费、管理费、业务费等详细分解</li>
                <li><strong>收支对比分析：</strong>预计收款与实际收款的对比</li>
                <li><strong>成本控制监控：</strong>实时监控项目成本控制情况</li>
            </ul>
            
            <div class="highlight-box">
                <strong>💡 管理价值：</strong>
                <ul>
                    <li>为项目经理提供成本控制依据</li>
                    <li>帮助财务部门监控项目盈利能力</li>
                    <li>支持管理层进行成本决策</li>
                    <li>识别成本异常和风险项目</li>
                </ul>
            </div>
        </div>

        <h2>🗄️ 复杂数据来源分析</h2>

        <div class="analysis-card data-source">
            <h3>核心数据表体系</h3>
            
            <h4><span class="tag tag-query">主表</span>tuqoa_gcproject - 工程项目表</h4>
            <div class="code-snippet">
-- 获取活跃项目列表
SELECT * FROM `tuqoa_gcproject` 
WHERE xmzt in ('新开工项目','在建项目','完工未结算') 
order by id desc
            </div>
            <p><strong>用途：</strong>获取需要成本控制的活跃项目列表</p>
            
            <h4><span class="tag tag-query">成本</span>tuqoa_xmcztjb - 项目成本统计表</h4>
            <div class="code-snippet">
-- 月度成本查询
SELECT * FROM `tuqoa_xmcztjb` 
where projectid=$projectId and `sbrq` like '$selectedMonth%' 
order by id desc

-- 年度成本汇总
select IFNULL(SUM(wccz), 0) as wccznd 
from tuqoa_xmcztjb 
where projectid=$projectId and sbrq like '$year%'

-- 累计成本统计
select IFNULL(sum(wccz),0) as wcczlj 
from tuqoa_xmcztjb 
where projectid=$projectId
            </div>
            <p><strong>用途：</strong>核心成本数据，支持月度、年度、累计三个维度统计</p>
            
            <h4><span class="tag tag-query">人员</span>tuqoa_rydp - 人员配置表</h4>
            <div class="code-snippet">
-- 获取项目全职人员
SELECT * FROM `tuqoa_rydp` 
WHERE `drxmid`=$projectId and `sfqz`='全职' and `state`='在职'
            </div>
            <p><strong>用途：</strong>获取项目人员配置，计算人工成本</p>
            
            <h4><span class="tag tag-query">工资</span>tuqoa_hrsalary - 人力资源工资表</h4>
            <div class="code-snippet">
-- 查询员工月度工资
SELECT * FROM `tuqoa_hrsalary` 
WHERE `uname`='$employeeName' and `month`='$selectedMonth'
            </div>
            <p><strong>用途：</strong>获取具体员工的工资数据，计算人工费用</p>
            
            <h4><span class="tag tag-query">社保</span>tuqoa_xmsjbxmx - 项目社保明细表</h4>
            <div class="code-snippet">
-- 项目社保费用统计
SELECT ifnull(sum(sjje),0) as hj 
FROM `tuqoa_xmsjbxmx` 
WHERE `projectid`=$projectId and `ys` like '$selectedMonth%'
            </div>
            <p><strong>用途：</strong>统计项目的社保等福利费用</p>
            
            <h4><span class="tag tag-query">核算</span>tuqoa_xmhstjzl - 项目核算统计资料表</h4>
            <div class="code-snippet">
-- 管理费和业务费查询
SELECT * FROM `tuqoa_xmhstjzl` 
WHERE `projectid`=$projectId and `sbrq` like '$selectedMonth%'
            </div>
            <p><strong>用途：</strong>获取管理费、业务费等间接成本</p>
            
            <h4><span class="tag tag-query">收费</span>tuqoa_htsf - 合同收费表</h4>
            <div class="code-snippet">
-- 预计收款统计
SELECT ifnull(sum(yjje),0) as yjjehj 
FROM `tuqoa_htsf` 
WHERE projectid=$projectId and `yjsj` like '$selectedMonth%'

-- 实际收款统计
SELECT ifnull(sum(ysje),0) as ysjehj 
FROM `tuqoa_htsf` 
WHERE projectid=$projectId and `sksj` like '$selectedMonth%'
            </div>
            <p><strong>用途：</strong>统计项目的收款情况，计算收支比</p>
        </div>

        <h2>💰 成本计算逻辑</h2>

        <div class="analysis-card feature">
            <h3>复杂成本计算体系</h3>
            
            <div class="cost-flow">
                <h4>成本计算流程</h4>
                
                <div class="flow-step">
                    <h5><span class="tag tag-calc">步骤1</span>基础成本统计</h5>
                    <div class="code-snippet">
// 月度完成成本
$wccz = 0; // 从tuqoa_xmcztjb表获取
while ($row1 = mysqli_fetch_assoc($result1)) {
    $wccz += $row1["wccz"];
}

// 年度累计成本
$wccznd = 0; // 年度成本汇总

// 项目累计成本  
$wcczlj = 0; // 项目开始至今的总成本
                    </div>
                </div>
                
                <div class="flow-step">
                    <h5><span class="tag tag-calc">步骤2</span>人工成本计算</h5>
                    <div class="code-snippet">
// 获取项目全职人员
$total_salary = 0;
$sql1 = "SELECT * FROM `tuqoa_rydp` WHERE `drxmid`=$projectId 
         and `sfqz`='全职' and `state`='在职'";

// 循环计算每个员工的工资
foreach ($employees as $employee) {
    $sql2 = "SELECT * FROM `tuqoa_hrsalary` 
             WHERE `uname`='{$employee['dpryxm']}' and `month`='$selectedMonth'";
    
    if ($salaryData) {
        $total_salary += $salaryData['sfgz']; // 实发工资
    }
}
                    </div>
                </div>
                
                <div class="flow-step">
                    <h5><span class="tag tag-calc">步骤3</span>福利费用计算</h5>
                    <div class="code-snippet">
// 社保费用统计
$social_insurance_total = 0;
$sql1 = "SELECT ifnull(sum(sjje),0) as hj FROM `tuqoa_xmsjbxmx` 
         WHERE `projectid`=$projectId and `ys` like '$selectedMonth%'";

// 其他福利费用
$welfare_cost = $social_insurance_total; // 可扩展其他福利项目
                    </div>
                </div>
                
                <div class="flow-step">
                    <h5><span class="tag tag-calc">步骤4</span>管理费用计算</h5>
                    <div class="code-snippet">
// 管理费和业务费
$management_fee = 0;
$business_fee = 0;

$sql1 = "SELECT * FROM `tuqoa_xmhstjzl` 
         WHERE `projectid`=$projectId and `sbrq` like '$selectedMonth%'";

while ($row1 = mysqli_fetch_assoc($result1)) {
    $management_fee += $row1["glf"];  // 管理费
    $business_fee += $row1["ywf"];    // 业务费
}

// 总间接费用
$indirect_cost = $management_fee + $business_fee;
                    </div>
                </div>
                
                <div class="flow-step">
                    <h5><span class="tag tag-calc">步骤5</span>收支对比计算</h5>
                    <div class="code-snippet">
// 预计收款
$expected_payment = 0;
$sql1 = "SELECT ifnull(sum(yjje),0) as yjjehj FROM `tuqoa_htsf` 
         WHERE projectid=$projectId and `yjsj` like '$selectedMonth%'";

// 实际收款
$actual_payment = 0;
$sql1 = "SELECT ifnull(sum(ysje),0) as ysjehj FROM `tuqoa_htsf` 
         WHERE projectid=$projectId and `sksj` like '$selectedMonth%'";

// 收款完成率
$payment_rate = ($expected_payment > 0) ? 
                ($actual_payment / $expected_payment) * 100 : 0;
                    </div>
                </div>
            </div>
        </div>

        <h2>📊 成本控制指标</h2>

        <div class="analysis-card chart">
            <h3>关键控制指标体系</h3>
            
            <table class="table">
                <thead>
                    <tr>
                        <th>指标类别</th>
                        <th>具体指标</th>
                        <th>计算方法</th>
                        <th>控制标准</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td rowspan="3">成本控制</td>
                        <td>月度成本</td>
                        <td>当月完成成本</td>
                        <td>≤ 预算成本</td>
                    </tr>
                    <tr>
                        <td>年度成本</td>
                        <td>年初至今累计成本</td>
                        <td>≤ 年度预算</td>
                    </tr>
                    <tr>
                        <td>累计成本</td>
                        <td>项目开始至今总成本</td>
                        <td>≤ 合同总额80%</td>
                    </tr>
                    <tr>
                        <td rowspan="3">人工成本</td>
                        <td>人工费用</td>
                        <td>全职员工工资总和</td>
                        <td>≤ 总成本60%</td>
                    </tr>
                    <tr>
                        <td>福利费用</td>
                        <td>社保等福利支出</td>
                        <td>≤ 人工费20%</td>
                    </tr>
                    <tr>
                        <td>人均成本</td>
                        <td>总人工成本/人数</td>
                        <td>行业标准对比</td>
                    </tr>
                    <tr>
                        <td rowspan="2">间接费用</td>
                        <td>管理费</td>
                        <td>项目管理费用</td>
                        <td>≤ 总成本15%</td>
                    </tr>
                    <tr>
                        <td>业务费</td>
                        <td>业务相关费用</td>
                        <td>≤ 总成本10%</td>
                    </tr>
                    <tr>
                        <td rowspan="2">收支控制</td>
                        <td>收款及时性</td>
                        <td>实际收款/预计收款</td>
                        <td>≥ 90%</td>
                    </tr>
                    <tr>
                        <td>成本收入比</td>
                        <td>总成本/总收入</td>
                        <td>≤ 85%</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <h2>🎯 成本控制预警机制</h2>

        <div class="analysis-card business">
            <h3>多级预警体系</h3>
            
            <h4><span class="tag tag-control">预警</span>成本超支预警</h4>
            <div class="code-snippet">
// 成本控制预警逻辑
function checkCostWarning($projectData) {
    $warnings = [];
    
    // 月度成本预警
    if ($projectData['monthly_cost'] > $projectData['monthly_budget']) {
        $overrun = $projectData['monthly_cost'] - $projectData['monthly_budget'];
        $warnings[] = [
            'level' => 'warning',
            'type' => '月度成本超支',
            'message' => "超支 {$overrun} 万元",
            'suggestion' => '检查成本构成，控制非必要支出'
        ];
    }
    
    // 人工成本占比预警
    $labor_ratio = ($projectData['labor_cost'] / $projectData['total_cost']) * 100;
    if ($labor_ratio > 65) {
        $warnings[] = [
            'level' => 'critical',
            'type' => '人工成本占比过高',
            'message' => "人工成本占比 {$labor_ratio}%",
            'suggestion' => '优化人员配置，提高工作效率'
        ];
    }
    
    // 收款延迟预警
    if ($projectData['payment_rate'] < 80) {
        $warnings[] = [
            'level' => 'urgent',
            'type' => '收款进度滞后',
            'message' => "收款完成率 {$projectData['payment_rate']}%",
            'suggestion' => '加强收款管理，及时跟进客户'
        ];
    }
    
    return $warnings;
}
            </div>
            
            <h4>预警等级说明</h4>
            <ul>
                <li><strong>🟢 正常：</strong>所有指标在控制范围内</li>
                <li><strong>🟡 注意：</strong>个别指标接近预警线</li>
                <li><strong>🟠 警告：</strong>重要指标超出正常范围</li>
                <li><strong>🔴 严重：</strong>多项指标异常，需立即处理</li>
            </ul>
        </div>

        <h2>📈 数据可视化特色</h2>

        <div class="analysis-card chart">
            <h3>多维度图表展示</h3>
            
            <h4><span class="tag tag-cost">图表</span>成本结构饼图</h4>
            <div class="code-snippet">
// 成本结构分析饼图
var costStructureChart = new Chart(ctx, {
    type: 'pie',
    data: {
        labels: ['人工费', '管理费', '业务费', '福利费', '其他费用'],
        datasets: [{
            data: [laborCost, managementFee, businessFee, welfareCost, otherCost],
            backgroundColor: [
                'rgba(255, 99, 132, 0.8)',
                'rgba(54, 162, 235, 0.8)',
                'rgba(255, 205, 86, 0.8)',
                'rgba(75, 192, 192, 0.8)',
                'rgba(153, 102, 255, 0.8)'
            ]
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        var percentage = ((context.parsed / totalCost) * 100).toFixed(1);
                        return context.label + ': ¥' + context.parsed.toLocaleString() + 
                               ' (' + percentage + '%)';
                    }
                }
            }
        }
    }
});
            </div>
            
            <h4><span class="tag tag-cost">图表</span>成本趋势线图</h4>
            <div class="code-snippet">
// 月度成本趋势图
var trendChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: monthLabels,
        datasets: [{
            label: '实际成本',
            data: actualCostData,
            borderColor: 'rgb(255, 99, 132)',
            backgroundColor: 'rgba(255, 99, 132, 0.2)',
            tension: 0.1
        }, {
            label: '预算成本',
            data: budgetCostData,
            borderColor: 'rgb(54, 162, 235)',
            backgroundColor: 'rgba(54, 162, 235, 0.2)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        interaction: {
            mode: 'index',
            intersect: false,
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return '¥' + (value/10000).toFixed(1) + '万';
                    }
                }
            }
        }
    }
});
            </div>
        </div>

        <h2>🎯 技术特点</h2>

        <div class="analysis-card">
            <h3>复杂查询优化</h3>
            
            <h4>查询性能特点</h4>
            <ul>
                <li><strong>多表关联：</strong>涉及6个核心业务表的复杂关联</li>
                <li><strong>嵌套查询：</strong>项目-人员-工资的三层嵌套查询</li>
                <li><strong>聚合计算：</strong>大量的SUM、COUNT聚合操作</li>
                <li><strong>时间筛选：</strong>灵活的月度、年度时间维度筛选</li>
            </ul>
            
            <h4>数据处理特色</h4>
            <ul>
                <li><strong>实时计算：</strong>页面加载时实时计算各项指标</li>
                <li><strong>多维统计：</strong>支持月度、年度、累计多个维度</li>
                <li><strong>异常处理：</strong>使用IFNULL处理空值情况</li>
                <li><strong>数据验证：</strong>确保计算结果的准确性</li>
            </ul>
            
            <div class="warning-box">
                <strong>⚠️ 性能注意事项：</strong>
                <ul>
                    <li>嵌套查询可能影响页面加载速度</li>
                    <li>建议为时间字段添加索引优化</li>
                    <li>考虑使用缓存机制减少重复计算</li>
                    <li>大项目数量时建议添加分页功能</li>
                </ul>
            </div>
        </div>

        <h2>🚀 优化建议</h2>

        <div class="analysis-card">
            <h3>性能优化</h3>
            <ul>
                <li>为sbrq、yjsj、sksj等时间字段添加索引</li>
                <li>优化嵌套查询，考虑使用JOIN替代</li>
                <li>实现成本数据的预计算和缓存</li>
                <li>添加数据分页，避免一次加载过多项目</li>
            </ul>
            
            <h3>功能增强</h3>
            <ul>
                <li>添加成本预算设置和对比功能</li>
                <li>实现自动化的成本预警通知</li>
                <li>支持成本数据的导出和报表生成</li>
                <li>增加成本分析的钻取功能</li>
            </ul>
            
            <h3>用户体验</h3>
            <ul>
                <li>添加数据加载进度指示</li>
                <li>支持表格数据的排序和筛选</li>
                <li>增加成本异常的高亮显示</li>
                <li>添加移动端适配</li>
            </ul>
        </div>

        <div style="text-align: center; margin-top: 40px; color: #7f8c8d;">
            <p>📅 分析日期：2025年8月7日</p>
            <p>📊 页面重要性：⭐⭐⭐⭐⭐ (成本控制核心)</p>
            <p>🔄 建议更新频率：实时</p>
        </div>
    </div>
</body>
</html>
