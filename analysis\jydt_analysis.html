<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>jydt.php 数据来源分析</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }
        h3 {
            color: #2980b9;
            margin-top: 20px;
        }
        .data-source {
            background: #ecf0f1;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #3498db;
        }
        .static-data {
            background: #fff3cd;
            border-left-color: #ffc107;
        }
        .dynamic-data {
            background: #d1ecf1;
            border-left-color: #17a2b8;
        }
        .table-info {
            background: #d4edda;
            border-left-color: #28a745;
        }
        .chart-info {
            background: #f8d7da;
            border-left-color: #dc3545;
        }
        .code-snippet {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        .tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            margin-right: 5px;
        }
        .tag-static { background: #ffc107; color: #212529; }
        .tag-dynamic { background: #17a2b8; color: white; }
        .tag-table { background: #28a745; color: white; }
        .tag-chart { background: #dc3545; color: white; }
        .tag-form { background: #fd7e14; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <h1>jydt.php 页面数据来源分析</h1>
        
        <h2>页面概述</h2>
        <p>jydt.php 是一个经营动态监控页面，用于实时展示公司的经营状况，包括合同签订情况、员工动态、业务趋势等关键经营指标的动态监控。</p>
        
        <h2>数据来源分析</h2>
        
        <h3>1. 用户输入数据</h3>
        
        <div class="data-source static-data">
            <h4><span class="tag tag-form">表单</span>日期范围选择</h4>
            <p><strong>数据内容：</strong>查询的开始日期和结束日期</p>
            <p><strong>来源：</strong>用户通过表单输入，带严格的日期格式验证</p>
            <div class="code-snippet">
$startDate = isset($_POST['start-date']) ? $_POST['start-date'] : $firstDayOfMonth;
$endDate = isset($_POST['end-date']) ? $_POST['end-date'] : $lastDayOfMonth;

// 严格的日期格式验证
if (DateTime::createFromFormat('Y-m-d', $_POST['start-date']) !== false) {
    $startDate = $_POST['start-date'];
}
            </div>
            <p><strong>默认值：</strong>当月第一天到最后一天</p>
            <p><strong>特点：</strong>包含日期范围合理性检查和错误提示</p>
        </div>
        
        <h3>2. 动态数据查询</h3>
        
        <div class="data-source dynamic-data">
            <h4><span class="tag tag-dynamic">动态</span><span class="tag tag-table">tuqoa_htgl</span>合同统计数据</h4>
            <p><strong>数据内容：</strong>合同数量、总金额、平均金额</p>
            <p><strong>来源：</strong>tuqoa_htgl（合同管理表）</p>
            <div class="code-snippet">
SELECT COUNT(*) as count, SUM(fwf) as total, AVG(fwf) as avg 
FROM `tuqoa_htgl` 
WHERE `qdsj`>='$startDate' and `qdsj`<='$endDate'
            </div>
            <p><strong>说明：</strong>统计指定时间范围内签订的合同基本信息</p>
        </div>
        
        <div class="data-source dynamic-data">
            <h4><span class="tag tag-dynamic">动态</span><span class="tag tag-table">tuqoa_userinfo</span>员工统计数据</h4>
            <p><strong>数据内容：</strong>员工总数和新入职员工数</p>
            <p><strong>来源：</strong>tuqoa_userinfo（用户信息表）</p>
            <div class="code-snippet">
// 员工总数（排除离职员工）
SELECT COUNT(*) as total FROM `tuqoa_userinfo` WHERE state<>5

// 新入职员工数
SELECT COUNT(*) as new_count FROM `tuqoa_userinfo` 
WHERE `workdate`>='$startDate' and `workdate`<='$endDate'
            </div>
            <p><strong>说明：</strong>state<>5 排除离职员工，workdate统计新入职</p>
        </div>
        
        <div class="data-source dynamic-data">
            <h4><span class="tag tag-dynamic">动态</span><span class="tag tag-table">tuqoa_htgl</span>合同数据检查</h4>
            <p><strong>数据内容：</strong>检查是否有合同数据用于图表显示</p>
            <p><strong>来源：</strong>tuqoa_htgl表</p>
            <div class="code-snippet">
SELECT COUNT(*) as total_contracts FROM `tuqoa_htgl` 
WHERE `qdsj`>='$startDate' and `qdsj`<='$endDate'
            </div>
            <p><strong>说明：</strong>用于判断是否显示图表，避免空数据图表</p>
        </div>
        
        <div class="data-source dynamic-data">
            <h4><span class="tag tag-dynamic">动态</span><span class="tag tag-table">tuqoa_htgl</span>最大合同金额查询</h4>
            <p><strong>数据内容：</strong>期间内最大的合同金额</p>
            <p><strong>来源：</strong>tuqoa_htgl表</p>
            <div class="code-snippet">
SELECT MAX(fwf) as max_amount FROM `tuqoa_htgl` 
WHERE `qdsj`>='$startDate' and `qdsj`<='$endDate'
            </div>
            <p><strong>说明：</strong>用于图表Y轴范围设置和数据分析</p>
        </div>
        
        <h3>3. 表格数据</h3>
        
        <div class="data-source table-info">
            <h4><span class="tag tag-table">表格</span>最新合同列表</h4>
            <p><strong>数据内容：</strong>最近签订的合同详细信息</p>
            <p><strong>来源：</strong>tuqoa_htgl表</p>
            <div class="code-snippet">
SELECT * FROM `tuqoa_htgl` 
WHERE `qdsj`>='$startDate' and `qdsj`<='$endDate' 
order by qdsj desc 
LIMIT 10
            </div>
            <p><strong>显示字段：</strong></p>
            <ul>
                <li>合同编号（htbh）</li>
                <li>客户名称（khmc）</li>
                <li>合同金额（fwf）</li>
                <li>签订日期（qdsj）</li>
            </ul>
            <p><strong>说明：</strong>按签订日期降序排列，显示最新的10条记录</p>
        </div>
        
        <h3>4. 图表数据</h3>
        
        <div class="data-source chart-info">
            <h4><span class="tag tag-chart">图表</span>合同趋势分析</h4>
            <p><strong>数据内容：</strong>按时间段的合同金额趋势</p>
            <p><strong>来源：</strong>tuqoa_htgl表按时间分组统计</p>
            <div class="code-snippet">
// 循环生成时间段数据
for ($i = 0; $i < $periods; $i++) {
    $periodStart = date('Y-m-d', strtotime($startDate . " +$i days"));
    $periodEnd = date('Y-m-d', strtotime($periodStart . " +$periodLength days"));
    
    $sql = "SELECT COALESCE(SUM(fwf), 0) as period_amount 
            FROM `tuqoa_htgl`
            WHERE `qdsj` >= '$periodStart' AND `qdsj` <= '$periodEnd'";
}
            </div>
            <p><strong>说明：</strong>根据查询时间范围动态计算时间段，生成趋势图数据</p>
        </div>
        
        <h2>数据表结构</h2>
        
        <div class="table-info">
            <h4>主要数据表及关键字段</h4>
            <ul>
                <li><strong>tuqoa_htgl：</strong>合同管理表
                    <ul>
                        <li>htbh：合同编号</li>
                        <li>khmc：客户名称</li>
                        <li>fwf：服务费/合同金额</li>
                        <li>qdsj：签订时间</li>
                    </ul>
                </li>
                <li><strong>tuqoa_userinfo：</strong>用户信息表
                    <ul>
                        <li>state：员工状态（5=离职）</li>
                        <li>workdate：入职日期</li>
                    </ul>
                </li>
            </ul>
        </div>
        
        <h2>页面特点</h2>
        
        <h3>1. 实时监控</h3>
        <ul>
            <li>动态显示经营关键指标</li>
            <li>实时合同签订情况</li>
            <li>员工动态变化监控</li>
        </ul>
        
        <h3>2. 数据验证</h3>
        <ul>
            <li>严格的日期格式验证</li>
            <li>日期范围合理性检查</li>
            <li>数据存在性检查</li>
        </ul>
        
        <h3>3. 智能展示</h3>
        <ul>
            <li>无数据时显示友好提示</li>
            <li>根据数据情况决定是否显示图表</li>
            <li>动态计算图表参数</li>
        </ul>
        
        <h3>4. 用户体验</h3>
        <ul>
            <li>响应式设计适配不同设备</li>
            <li>合同金额的特殊样式展示</li>
            <li>部门进度的交互效果</li>
            <li>表格数据的合理布局</li>
        </ul>
        
        <h3>5. 容错处理</h3>
        <ul>
            <li>数据库查询错误处理</li>
            <li>空数据的默认显示</li>
            <li>日期验证失败的回退机制</li>
        </ul>
        
        <h2>业务逻辑</h2>
        
        <div class="data-source">
            <h4>经营动态监控逻辑</h4>
            <ul>
                <li><strong>合同监控：</strong>统计合同数量、金额、平均值</li>
                <li><strong>人员监控：</strong>跟踪员工总数和新入职情况</li>
                <li><strong>趋势分析：</strong>按时间段分析合同签订趋势</li>
                <li><strong>最新动态：</strong>展示最近的合同签订情况</li>
            </ul>
        </div>
        
        <h2>技术特点</h2>
        
        <div class="data-source">
            <h4>前端技术</h4>
            <ul>
                <li>Bootstrap 5.1.3 响应式框架</li>
                <li>Chart.js 图表库</li>
                <li>Font Awesome 和 Boxicons 图标</li>
                <li>自定义CSS动画效果</li>
            </ul>
            
            <h4>后端技术</h4>
            <ul>
                <li>PHP 服务器端处理</li>
                <li>MySQL 数据库查询</li>
                <li>DateTime 日期验证</li>
                <li>MySQLi 数据库连接</li>
            </ul>
        </div>
        
        <h2>总结</h2>
        <p>jydt.php是一个专注于经营动态监控的页面，数据来源包括：</p>
        <ul>
            <li><strong>用户输入：</strong>日期范围选择（带严格验证）</li>
            <li><strong>动态数据：</strong>从tuqoa_htgl和tuqoa_userinfo表查询的实时经营数据</li>
            <li><strong>统计分析：</strong>合同数量、金额、员工变化等关键指标</li>
            <li><strong>趋势图表：</strong>基于时间维度的合同签订趋势分析</li>
            <li><strong>明细展示：</strong>最新合同的详细信息列表</li>
        </ul>
        <p>该页面通过简洁的界面和实时的数据展示，为管理层提供了快速了解公司经营状况的工具，具有很强的实用性和时效性。</p>
    </div>
</body>
</html>
