<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>经营统计详细分析</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 10px;
            font-size: 2.5rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .subtitle {
            text-align: center;
            color: #7f8c8d;
            margin-bottom: 30px;
            font-size: 1.1rem;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }
        h3 {
            color: #2980b9;
            margin-top: 20px;
        }
        .analysis-card {
            background: #f8f9fa;
            padding: 20px;
            margin: 15px 0;
            border-radius: 10px;
            border-left: 4px solid #3498db;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .data-source { border-left-color: #28a745; }
        .feature { border-left-color: #ffc107; }
        .chart { border-left-color: #dc3545; }
        .business { border-left-color: #6f42c1; }
        .code-snippet {
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 10px 0;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .table th, .table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
            font-size: 14px;
        }
        .table th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            font-weight: 600;
        }
        .highlight-box {
            background: #e7f3ff;
            border: 1px solid #b8daff;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .tag {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            margin-right: 5px;
        }
        .tag-query { background: #28a745; color: white; }
        .tag-calc { background: #ffc107; color: #212529; }
        .tag-chart { background: #dc3545; color: white; }
        .tag-trend { background: #6f42c1; color: white; }
        .metric-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 经营统计详细分析</h1>
        <p class="subtitle">jytj.php - 企业经营状况综合统计和趋势分析</p>
        
        <h2>📋 页面功能概述</h2>
        
        <div class="analysis-card business">
            <h3>核心功能</h3>
            <p>经营统计页面是企业经营管理的综合分析工具，提供全面的经营数据统计：</p>
            <ul>
                <li><strong>核心指标统计：</strong>服务费、预计收费、实际回款等关键指标</li>
                <li><strong>时间趋势分析：</strong>支持季度和月度的趋势对比分析</li>
                <li><strong>收款效率分析：</strong>预计收款与实际收款的对比分析</li>
                <li><strong>经营健康度：</strong>通过多维度指标评估经营状况</li>
            </ul>
            
            <div class="highlight-box">
                <strong>💡 管理价值：</strong>
                <ul>
                    <li>为管理层提供经营决策数据支撑</li>
                    <li>监控企业收入和回款情况</li>
                    <li>识别经营趋势和潜在风险</li>
                    <li>支持年度经营计划制定和调整</li>
                </ul>
            </div>
        </div>

        <h2>🗄️ 核心数据来源分析</h2>

        <div class="analysis-card data-source">
            <h3>主要数据表</h3>
            
            <h4><span class="tag tag-query">合同</span>tuqoa_htgl - 合同管理表</h4>
            <div class="code-snippet">
-- 计算服务费合计
SELECT sum(fwf) fwfhj FROM `tuqoa_htgl` 
WHERE `qdsj`>='$startDate' and `qdsj`<='$endDate'

-- 季度/月度服务费统计
SELECT IFNULL(SUM(fwf), 0) as total FROM tuqoa_htgl 
WHERE qdsj >= '$periodStart' AND qdsj <= '$periodEnd'
            </div>
            <p><strong>用途：</strong>统计合同签订金额，反映企业业务规模</p>
            
            <h4><span class="tag tag-query">收费</span>tuqoa_htsf - 合同收费表</h4>
            <div class="code-snippet">
-- 计算预计收费合计
SELECT sum(yjje) as yjjehj FROM `tuqoa_htsf` 
WHERE `yjsj`>='$startDate' and `yjsj`<='$endDate'

-- 计算实际回款合计
SELECT sum(ysje) as ysjehj FROM `tuqoa_htsf` 
WHERE `sksj`>='$startDate' and `sksj`<='$endDate'
            </div>
            <p><strong>用途：</strong>统计预计收款和实际收款，分析收款效率</p>
        </div>

        <h2>📈 多维度趋势分析</h2>

        <div class="analysis-card feature">
            <h3>双重时间维度统计</h3>
            
            <h4><span class="tag tag-calc">季度</span>季度趋势分析</h4>
            <div class="code-snippet">
// 季度数据统计逻辑
for ($quarter = 1; $quarter <= 4; $quarter++) {
    // 计算季度起止日期
    $quarterStart = date('Y') . '-' . sprintf('%02d', ($quarter - 1) * 3 + 1) . '-01';
    $quarterEnd = date('Y-m-t', strtotime($quarterStart . ' +2 months'));
    
    // 查询季度服务费
    $sql = "SELECT IFNULL(SUM(fwf), 0) as total FROM tuqoa_htgl 
            WHERE qdsj >= '$quarterStart' AND qdsj <= '$quarterEnd'";
    
    // 查询季度预计收费和实际收费
    $sql_yj = "SELECT IFNULL(SUM(yjje), 0) as total FROM tuqoa_htsf 
               WHERE yjsj >= '$quarterStart' AND yjsj <= '$quarterEnd'";
    
    $sql_ys = "SELECT IFNULL(SUM(ysje), 0) as total FROM tuqoa_htsf 
               WHERE sksj >= '$quarterStart' AND sksj <= '$quarterEnd'";
}
            </div>
            
            <h4><span class="tag tag-calc">月度</span>月度趋势分析</h4>
            <div class="code-snippet">
// 月度数据统计逻辑
for ($month = 1; $month <= 12; $month++) {
    // 计算月度起止日期
    $monthStart = date('Y') . '-' . sprintf('%02d', $month) . '-01';
    $monthEnd = date('Y-m-t', strtotime($monthStart));
    
    // 查询月度各项指标
    // 服务费、预计收费、实际收费的月度统计
}
            </div>
        </div>

        <h2>📊 关键经营指标</h2>

        <div class="analysis-card chart">
            <h3>核心经营指标体系</h3>
            
            <div class="metric-grid">
                <div class="metric-card">
                    <h4>服务费合计</h4>
                    <div class="metric-value">¥X.XX万</div>
                    <p>合同签订总额</p>
                </div>
                <div class="metric-card">
                    <h4>预计收费</h4>
                    <div class="metric-value">¥X.XX万</div>
                    <p>计划收款金额</p>
                </div>
                <div class="metric-card">
                    <h4>实际回款</h4>
                    <div class="metric-value">¥X.XX万</div>
                    <p>实际到账金额</p>
                </div>
                <div class="metric-card">
                    <h4>回款率</h4>
                    <div class="metric-value">XX%</div>
                    <p>收款执行效率</p>
                </div>
            </div>
            
            <table class="table">
                <thead>
                    <tr>
                        <th>指标类别</th>
                        <th>计算公式</th>
                        <th>业务意义</th>
                        <th>健康标准</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>服务费合计</td>
                        <td>SUM(tuqoa_htgl.fwf)</td>
                        <td>业务规模指标</td>
                        <td>同比增长 > 10%</td>
                    </tr>
                    <tr>
                        <td>预计收费</td>
                        <td>SUM(tuqoa_htsf.yjje)</td>
                        <td>收款计划指标</td>
                        <td>与服务费匹配</td>
                    </tr>
                    <tr>
                        <td>实际回款</td>
                        <td>SUM(tuqoa_htsf.ysje)</td>
                        <td>现金流指标</td>
                        <td>及时到账</td>
                    </tr>
                    <tr>
                        <td>回款率</td>
                        <td>实际回款/预计收费</td>
                        <td>收款效率指标</td>
                        <td>> 90%</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <h2>🎯 技术实现特点</h2>

        <div class="analysis-card">
            <h3>代码架构特色</h3>
            
            <h4>查询优化特点</h4>
            <ul>
                <li><strong>时间范围查询：</strong>灵活的日期范围筛选</li>
                <li><strong>聚合统计：</strong>使用SUM函数进行金额汇总</li>
                <li><strong>循环统计：</strong>通过循环生成季度和月度数据</li>
                <li><strong>空值处理：</strong>使用IFNULL确保数据完整性</li>
            </ul>
            
            <h4>数据处理特色</h4>
            <ul>
                <li><strong>双重维度：</strong>同时支持季度和月度分析</li>
                <li><strong>实时计算：</strong>页面加载时实时计算统计数据</li>
                <li><strong>数据转换：</strong>将查询结果转换为图表数据</li>
                <li><strong>异常处理：</strong>确保查询失败时的数据安全</li>
            </ul>
            
            <div class="warning-box">
                <strong>⚠️ 技术注意事项：</strong>
                <ul>
                    <li>循环查询可能影响性能，建议优化为批量查询</li>
                    <li>时间范围计算需要注意月份边界处理</li>
                    <li>大数据量时建议添加缓存机制</li>
                    <li>图表数据需要确保格式正确</li>
                </ul>
            </div>
        </div>

        <h2>🚀 优化建议</h2>

        <div class="analysis-card">
            <h3>性能优化</h3>
            <ul>
                <li>优化循环查询，使用单次查询获取所有时间段数据</li>
                <li>为时间字段（qdsj、yjsj、sksj）添加索引</li>
                <li>实现统计数据的缓存机制</li>
                <li>使用数据库视图预计算常用统计</li>
            </ul>
            
            <h3>功能增强</h3>
            <ul>
                <li>添加同比、环比分析功能</li>
                <li>支持自定义时间范围分析</li>
                <li>增加经营预警和提醒功能</li>
                <li>添加数据导出和报表生成</li>
            </ul>
        </div>

        <div style="text-align: center; margin-top: 40px; color: #7f8c8d;">
            <p>📅 分析日期：2025年8月7日</p>
            <p>📊 页面重要性：⭐⭐⭐⭐⭐ (经营分析核心)</p>
            <p>🔄 建议更新频率：实时</p>
        </div>
    </div>
</body>
</html>
