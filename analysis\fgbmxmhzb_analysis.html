<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>fgbmxmhzb.php 数据来源分析</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }
        h3 {
            color: #2980b9;
            margin-top: 20px;
        }
        .data-source {
            background: #ecf0f1;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #3498db;
        }
        .static-data {
            background: #fff3cd;
            border-left-color: #ffc107;
        }
        .dynamic-data {
            background: #d1ecf1;
            border-left-color: #17a2b8;
        }
        .table-info {
            background: #d4edda;
            border-left-color: #28a745;
        }
        .chart-info {
            background: #f8d7da;
            border-left-color: #dc3545;
        }
        .code-snippet {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        .tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            margin-right: 5px;
        }
        .tag-static { background: #ffc107; color: #212529; }
        .tag-dynamic { background: #17a2b8; color: white; }
        .tag-table { background: #28a745; color: white; }
        .tag-chart { background: #dc3545; color: white; }
        .tag-form { background: #fd7e14; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <h1>fgbmxmhzb.php 页面数据来源分析</h1>
        
        <h2>页面概述</h2>
        <p>fgbmxmhzb.php 是一个分管部门项目汇总报表页面，用于展示各部门的年度财务数据、人员配置、成本结构等综合信息，提供多维度的部门绩效分析。</p>
        
        <h2>数据来源分析</h2>
        
        <h3>1. 用户输入数据</h3>
        
        <div class="data-source static-data">
            <h4><span class="tag tag-form">表单</span>年份选择</h4>
            <p><strong>数据内容：</strong>用户选择的查询年份</p>
            <p><strong>来源：</strong>下拉选择框</p>
            <div class="code-snippet">
&lt;select id="year-select"&gt;
    &lt;option value="2024" selected&gt;2024年&lt;/option&gt;
    &lt;option value="2023"&gt;2023年&lt;/option&gt;
    &lt;option value="2022"&gt;2022年&lt;/option&gt;
&lt;/select&gt;
            </div>
            <p><strong>说明：</strong>提供2022-2024年的年份选择，默认选择2024年</p>
        </div>
        
        <h3>2. 静态数据（硬编码）</h3>
        
        <div class="data-source static-data">
            <h4><span class="tag tag-static">静态</span>统计卡片数据</h4>
            <p><strong>数据内容：</strong>年度收费总额、到账总额、差额、人均产值</p>
            <p><strong>来源：</strong>HTML中硬编码的数值</p>
            <div class="code-snippet">
年度收费总额: 5,800万
年度到账总额: 4,500万  
年度差额: 1,300万
年度人均产值: 120万
            </div>
            <p><strong>说明：</strong>这些数据直接写在HTML中，包含同比增长百分比</p>
        </div>
        
        <div class="data-source static-data">
            <h4><span class="tag tag-static">静态</span>部门年度汇总表数据</h4>
            <p><strong>数据内容：</strong>各部门的详细财务和人员数据</p>
            <p><strong>来源：</strong>HTML表格中硬编码</p>
            <div class="code-snippet">
市政工程部: 收费2,100万, 到账1,800万, 25人, 工资600万, 成本1,200万
建筑工程部: 收费2,500万, 到账2,000万, 30人, 工资750万, 成本1,500万  
装修工程部: 收费1,200万, 到账700万, 15人, 工资300万, 成本600万
            </div>
            <p><strong>说明：</strong>包含收费、到账、人数、工资、成本等完整信息</p>
        </div>
        
        <div class="data-source static-data">
            <h4><span class="tag tag-static">静态</span>部门季度数据</h4>
            <p><strong>数据内容：</strong>各部门按季度的收费和到账数据</p>
            <p><strong>来源：</strong>HTML表格中硬编码</p>
            <div class="code-snippet">
各部门四个季度的收费总额、到账总额、差额、月均人数等数据
            </div>
        </div>
        
        <h3>3. 图表数据（JavaScript静态数据）</h3>
        
        <div class="data-source chart-info">
            <h4><span class="tag tag-chart">图表</span>部门收费与到账趋势图</h4>
            <p><strong>数据内容：</strong>各部门的收费和到账趋势对比</p>
            <p><strong>来源：</strong>JavaScript中硬编码的数组数据</p>
            <div class="code-snippet">
datasets: [
    {
        label: '市政工程部收费',
        data: [500, 600, 550, 450]
    },
    {
        label: '建筑工程部收费', 
        data: [600, 700, 650, 550]
    }
]
            </div>
            <p><strong>说明：</strong>使用Chart.js创建折线图，显示各部门趋势</p>
        </div>
        
        <div class="data-source chart-info">
            <h4><span class="tag tag-chart">图表</span>部门人均产值对比图</h4>
            <p><strong>数据内容：</strong>各部门人均产值的柱状图对比</p>
            <p><strong>来源：</strong>JavaScript中硬编码</p>
            <div class="code-snippet">
data: {
    labels: ['市政工程部', '建筑工程部', '装修工程部'],
    datasets: [{
        label: '人均产值',
        data: [84, 83.3, 80]
    }]
}
            </div>
        </div>
        
        <div class="data-source chart-info">
            <h4><span class="tag tag-chart">图表</span>部门占比饼图</h4>
            <p><strong>数据内容：</strong>部门收费占比和人员占比</p>
            <p><strong>来源：</strong>JavaScript中硬编码</p>
            <div class="code-snippet">
// 收费占比
data: [2100, 2500, 1200]

// 人员占比  
data: [25, 30, 15]
            </div>
        </div>
        
        <div class="data-source chart-info">
            <h4><span class="tag tag-chart">图表</span>季度趋势图</h4>
            <p><strong>数据内容：</strong>各部门季度收费和到账趋势</p>
            <p><strong>来源：</strong>JavaScript中硬编码</p>
            <div class="code-snippet">
labels: ['第一季度', '第二季度', '第三季度', '第四季度']
// 各部门每季度的收费和到账数据
            </div>
        </div>
        
        <div class="data-source chart-info">
            <h4><span class="tag tag-chart">图表</span>月度人员变化图</h4>
            <p><strong>数据内容：</strong>各部门12个月的人员数量变化</p>
            <p><strong>来源：</strong>JavaScript中硬编码</p>
            <div class="code-snippet">
labels: ['1月', '2月', '3月', ..., '12月']
// 各部门每月人员数量数据
            </div>
        </div>
        
        <div class="data-source chart-info">
            <h4><span class="tag tag-chart">图表</span>成本结构占比图</h4>
            <p><strong>数据内容：</strong>各类成本的占比分布</p>
            <p><strong>来源：</strong>JavaScript中硬编码</p>
            <div class="code-snippet">
labels: ['工资成本', '材料成本', '设备成本', '差旅成本', '其他成本']
data: [1650, 850, 550, 125, 125]
            </div>
        </div>
        
        <h2>页面特点</h2>
        
        <h3>1. 数据展示方式</h3>
        <ul>
            <li><strong>统计卡片：</strong>4个关键指标的概览展示</li>
            <li><strong>汇总表格：</strong>部门年度和季度数据的详细表格</li>
            <li><strong>多种图表：</strong>折线图、柱状图、饼图等多种可视化方式</li>
        </ul>
        
        <h3>2. 分析维度</h3>
        <ul>
            <li><strong>部门维度：</strong>市政工程部、建筑工程部、装修工程部</li>
            <li><strong>时间维度：</strong>年度、季度、月度</li>
            <li><strong>指标维度：</strong>收费、到账、人员、成本、产值</li>
        </ul>
        
        <h3>3. 交互功能</h3>
        <ul>
            <li>年份选择器（支持2022-2024年）</li>
            <li>图表交互（悬停显示详情）</li>
            <li>实时时间更新</li>
        </ul>
        
        <h2>数据结构</h2>
        
        <div class="table-info">
            <h4>主要数据字段</h4>
            <ul>
                <li><strong>财务数据：</strong>年度收费总额、年度到账总额、年度差额</li>
                <li><strong>人员数据：</strong>年度月均人数、各月人员变化</li>
                <li><strong>成本数据：</strong>年度工资总额、年度成本总额、成本结构分布</li>
                <li><strong>效率数据：</strong>人均产值、工资占比、成本占比</li>
                <li><strong>时间数据：</strong>季度数据、月度数据</li>
            </ul>
        </div>
        
        <h2>总结</h2>
        <p>fgbmxmhzb.php页面是一个完全基于静态数据的报表页面，数据来源包括：</p>
        <ul>
            <li><strong>用户输入：</strong>年份选择（目前仅作为界面元素，未连接后端）</li>
            <li><strong>静态数据：</strong>所有统计数据、表格数据都硬编码在HTML中</li>
            <li><strong>图表数据：</strong>所有图表数据都硬编码在JavaScript中</li>
        </ul>
        <p>该页面提供了丰富的数据可视化展示，但所有数据都是静态的示例数据，没有连接到实际的数据库。页面主要用于展示报表的设计效果和功能布局。</p>
        
        <h2>建议</h2>
        <p>如需实现动态数据展示，建议：</p>
        <ul>
            <li>连接数据库，从相关表中查询实际的部门财务数据</li>
            <li>实现年份选择器的后端查询功能</li>
            <li>添加数据更新和刷新机制</li>
            <li>考虑添加数据导出功能</li>
        </ul>
    </div>
</body>
</html>
