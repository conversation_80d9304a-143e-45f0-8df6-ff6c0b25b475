<?php
include '../config.php';
error_reporting(E_ALL);
ini_set('display_errors', 1);
session_start();

// 设置测试用的session变量
$_SESSION['xinhu_adminid'] = 1;
$_SESSION['xinhu_projectid'] = 1;
$_SESSION['xinhu_project'] = '测试项目';

// 获取项目列表
$projectListSql = "SELECT id, gcname FROM tuqoa_gcproject ORDER BY id DESC LIMIT 3";
$projectListResult = mysqli_query($link, $projectListSql);
$projectList = [];
if ($projectListResult) {
    while ($projectRow = mysqli_fetch_assoc($projectListResult)) {
        $projectList[] = $projectRow;
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>页面布局优化展示</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/boxicons@2.0.7/css/boxicons.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            min-height: 100vh;
        }
        
        .demo-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 25px;
            margin: 20px 0;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            animation: fadeInUp 0.6s ease-out;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .navbar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: #2d3748 !important;
            display: flex;
            align-items: center;
        }
        
        .navbar-brand i {
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-size: 1.8rem;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .feature-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .feature-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        
        .feature-icon {
            font-size: 3rem;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 15px;
        }
        
        .demo-link {
            display: inline-block;
            padding: 12px 24px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            border-radius: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 10px;
        }
        
        .demo-link:hover {
            color: white;
            text-decoration: none;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
        
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        .before-after {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
        }
        
        .before {
            border-left: 4px solid #ef4444;
        }
        
        .after {
            border-left: 4px solid #10b981;
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            margin: 5px;
        }
        
        .badge-improved {
            background: linear-gradient(45deg, #10b981, #059669);
            color: white;
        }
        
        .badge-new {
            background: linear-gradient(45deg, #3b82f6, #2563eb);
            color: white;
        }
        
        .badge-enhanced {
            background: linear-gradient(45deg, #8b5cf6, #7c3aed);
            color: white;
        }
        
        @media (max-width: 768px) {
            .comparison-grid {
                grid-template-columns: 1fr;
            }
            
            .feature-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg sticky-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bx bx-palette me-2"></i>
                页面布局优化展示
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text">
                    <i class="bx bx-time me-1"></i>
                    演示时间: <span id="demo-time"><?php echo date('Y-m-d H:i:s'); ?></span>
                </span>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="demo-card">
            <h1 class="text-center mb-4">🎨 页面布局与风格优化完成</h1>
            <p class="text-center lead">
                我们对 <code>myxmfymx.php</code> 和 <code>myxmcbmx.php</code> 进行了全面的视觉和交互优化
            </p>
        </div>

        <div class="demo-card">
            <h2>🚀 优化亮点</h2>
            <div class="feature-grid">
                <div class="feature-item">
                    <i class="bx bx-palette feature-icon"></i>
                    <h5>现代化设计</h5>
                    <p>采用渐变背景、毛玻璃效果和圆角设计，提升视觉体验</p>
                    <span class="status-badge badge-improved">全面改进</span>
                </div>
                <div class="feature-item">
                    <i class="bx bx-mobile feature-icon"></i>
                    <h5>响应式布局</h5>
                    <p>完美适配桌面、平板和手机等不同设备</p>
                    <span class="status-badge badge-enhanced">体验增强</span>
                </div>
                <div class="feature-item">
                    <i class="bx bx-mouse feature-icon"></i>
                    <h5>交互动效</h5>
                    <p>悬停效果、平滑过渡和加载动画</p>
                    <span class="status-badge badge-new">新增功能</span>
                </div>
                <div class="feature-item">
                    <i class="bx bx-color-fill feature-icon"></i>
                    <h5>视觉层次</h5>
                    <p>清晰的信息层级和视觉引导</p>
                    <span class="status-badge badge-improved">显著提升</span>
                </div>
            </div>
        </div>

        <div class="demo-card">
            <h2>📊 优化对比</h2>
            <div class="comparison-grid">
                <div class="before-after before">
                    <h5>🔴 优化前</h5>
                    <ul class="text-start">
                        <li>简单的表格布局</li>
                        <li>基础的Bootstrap样式</li>
                        <li>缺少视觉层次</li>
                        <li>单调的色彩方案</li>
                        <li>有限的交互反馈</li>
                    </ul>
                </div>
                <div class="before-after after">
                    <h5>🟢 优化后</h5>
                    <ul class="text-start">
                        <li>现代化毛玻璃设计</li>
                        <li>渐变色彩和圆角元素</li>
                        <li>清晰的信息层级</li>
                        <li>丰富的视觉效果</li>
                        <li>流畅的交互动画</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="demo-card">
            <h2>🎯 核心改进</h2>
            <div class="row">
                <div class="col-md-6">
                    <h5>🎨 视觉设计</h5>
                    <ul>
                        <li><strong>渐变背景：</strong>紫蓝色渐变营造专业感</li>
                        <li><strong>毛玻璃效果：</strong>现代化的半透明设计</li>
                        <li><strong>圆角设计：</strong>16px圆角提升亲和力</li>
                        <li><strong>阴影效果：</strong>多层阴影增加立体感</li>
                        <li><strong>色彩系统：</strong>统一的品牌色彩方案</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h5>⚡ 交互体验</h5>
                    <ul>
                        <li><strong>悬停效果：</strong>按钮和表格行的动态反馈</li>
                        <li><strong>加载动画：</strong>fadeInUp进入动画</li>
                        <li><strong>平滑过渡：</strong>0.3s缓动过渡效果</li>
                        <li><strong>焦点状态：</strong>表单元素的焦点高亮</li>
                        <li><strong>滚动条美化：</strong>自定义滚动条样式</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="demo-card">
            <h2>🔗 查看优化效果</h2>
            <div class="text-center">
                <p class="mb-4">点击下方链接体验优化后的页面效果：</p>
                
                <?php if (count($projectList) > 0): ?>
                    <?php $firstProject = $projectList[0]; ?>
                    <a href="myxmfymx.php?projectid=<?php echo $firstProject['id']; ?>" 
                       class="demo-link" target="_blank">
                        <i class="bx bx-money me-2"></i>项目费用明细表
                    </a>
                    <a href="myxmcbmx.php?projectid=<?php echo $firstProject['id']; ?>" 
                       class="demo-link" target="_blank">
                        <i class="bx bx-calculator me-2"></i>项目成本明细表
                    </a>
                <?php else: ?>
                    <div class="alert alert-info">
                        <i class="bx bx-info-circle me-2"></i>
                        暂无可用项目数据，请确保数据库中有项目记录
                    </div>
                <?php endif; ?>
                
                <div class="mt-4">
                    <a href="myxmfymx.php" class="demo-link" target="_blank">
                        <i class="bx bx-money me-2"></i>费用明细（无参数）
                    </a>
                    <a href="myxmcbmx.php" class="demo-link" target="_blank">
                        <i class="bx bx-calculator me-2"></i>成本明细（无参数）
                    </a>
                </div>
            </div>
        </div>

        <div class="demo-card">
            <h2>📱 技术特性</h2>
            <div class="row">
                <div class="col-md-4">
                    <h6>🎨 CSS3 特性</h6>
                    <ul class="small">
                        <li>backdrop-filter 毛玻璃</li>
                        <li>linear-gradient 渐变</li>
                        <li>transform 变换</li>
                        <li>transition 过渡</li>
                        <li>animation 动画</li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h6>📐 布局技术</h6>
                    <ul class="small">
                        <li>Flexbox 弹性布局</li>
                        <li>Grid 网格布局</li>
                        <li>Sticky 粘性定位</li>
                        <li>媒体查询响应式</li>
                        <li>Bootstrap 5 组件</li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h6>🎯 用户体验</h6>
                    <ul class="small">
                        <li>加载动画反馈</li>
                        <li>悬停状态提示</li>
                        <li>焦点状态高亮</li>
                        <li>平滑滚动体验</li>
                        <li>触摸友好设计</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="demo-card">
            <h2>✅ 优化总结</h2>
            <div class="alert alert-success">
                <h5 class="alert-heading">🎉 优化完成！</h5>
                <p class="mb-0">
                    两个页面现在具备了现代化的视觉设计和流畅的交互体验，
                    包括渐变背景、毛玻璃效果、响应式布局、动画效果等特性，
                    大幅提升了用户体验和视觉吸引力。
                </p>
            </div>
        </div>
    </div>

    <script>
        // 更新演示时间
        function updateDemoTime() {
            const now = new Date();
            const formattedDate = now.getFullYear() + '-' +
                                 String(now.getMonth() + 1).padStart(2, '0') + '-' +
                                 String(now.getDate()).padStart(2, '0') + ' ' +
                                 String(now.getHours()).padStart(2, '0') + ':' +
                                 String(now.getMinutes()).padStart(2, '0') + ':' +
                                 String(now.getSeconds()).padStart(2, '0');
            document.getElementById('demo-time').textContent = formattedDate;
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateDemoTime();
            setInterval(updateDemoTime, 1000);
        });
    </script>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
