<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统诊断功能增强建议</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 10px;
            font-size: 2.5rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .subtitle {
            text-align: center;
            color: #7f8c8d;
            margin-bottom: 30px;
            font-size: 1.1rem;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }
        h3 {
            color: #2980b9;
            margin-top: 20px;
        }
        .enhancement-card {
            background: #f8f9fa;
            padding: 20px;
            margin: 15px 0;
            border-radius: 10px;
            border-left: 4px solid #3498db;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .current { border-left-color: #28a745; }
        .proposed { border-left-color: #ffc107; }
        .advanced { border-left-color: #dc3545; }
        .code-snippet {
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 10px 0;
        }
        .php-code { background: #4a5568; }
        .js-code { background: #2d3748; }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .table th, .table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
            font-size: 14px;
        }
        .table th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            font-weight: 600;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .before {
            background: #f8d7da;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #dc3545;
        }
        .after {
            background: #d4edda;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #28a745;
        }
        .feature-list {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .priority-high { border-left: 4px solid #dc3545; }
        .priority-medium { border-left: 4px solid #ffc107; }
        .priority-low { border-left: 4px solid #28a745; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 系统诊断功能增强建议</h1>
        <p class="subtitle">基于现有diagnose.php的功能分析和改进方案</p>
        
        <h2>📊 现有功能分析</h2>
        
        <div class="enhancement-card current">
            <h3>当前diagnose.php功能特点</h3>
            
            <h4>✅ 已实现功能</h4>
            <ul>
                <li><strong>数据库连接检查：</strong>验证MySQL连接状态</li>
                <li><strong>数据表验证：</strong>检查核心业务表的存在性和记录数</li>
                <li><strong>函数完整性：</strong>检查必要的自定义函数（getconfig, c函数等）</li>
                <li><strong>样本数据查询：</strong>测试项目、合同、成本数据的查询</li>
                <li><strong>可视化界面：</strong>Bootstrap风格的诊断结果展示</li>
                <li><strong>统计汇总：</strong>通过率、警告项、失败项的统计</li>
            </ul>
            
            <h4>🎨 界面特色</h4>
            <ul>
                <li>响应式设计，支持移动端访问</li>
                <li>状态图标和颜色编码</li>
                <li>实时时间显示</li>
                <li>动画效果增强用户体验</li>
            </ul>
        </div>

        <h2>🚀 功能增强建议</h2>

        <div class="enhancement-card proposed priority-high">
            <h3>高优先级增强 - 性能监控</h3>
            
            <h4>数据库性能诊断</h4>
            <div class="code-snippet php-code">
// 增强的数据库性能检查
function getDatabasePerformance() {
    global $link;
    $performance = [];
    
    // 连接数监控
    $result = mysqli_query($link, "SHOW STATUS LIKE 'Threads_connected'");
    $connected = mysqli_fetch_assoc($result)['Value'];
    
    $result = mysqli_query($link, "SHOW VARIABLES LIKE 'max_connections'");
    $max_connections = mysqli_fetch_assoc($result)['Value'];
    
    $performance['connection_usage'] = round(($connected / $max_connections) * 100, 2);
    
    // 慢查询监控
    $result = mysqli_query($link, "SHOW STATUS LIKE 'Slow_queries'");
    $performance['slow_queries'] = mysqli_fetch_assoc($result)['Value'];
    
    // 查询缓存命中率
    $result = mysqli_query($link, "SHOW STATUS LIKE 'Qcache_hits'");
    $hits = mysqli_fetch_assoc($result)['Value'];
    
    $result = mysqli_query($link, "SHOW STATUS LIKE 'Qcache_inserts'");
    $inserts = mysqli_fetch_assoc($result)['Value'];
    
    if (($hits + $inserts) > 0) {
        $performance['cache_hit_rate'] = round(($hits / ($hits + $inserts)) * 100, 2);
    }
    
    // InnoDB缓冲池使用率
    $result = mysqli_query($link, "SHOW STATUS LIKE 'Innodb_buffer_pool_pages_total'");
    $total_pages = mysqli_fetch_assoc($result)['Value'];
    
    $result = mysqli_query($link, "SHOW STATUS LIKE 'Innodb_buffer_pool_pages_free'");
    $free_pages = mysqli_fetch_assoc($result)['Value'];
    
    $performance['buffer_pool_usage'] = round(((($total_pages - $free_pages) / $total_pages) * 100), 2);
    
    return $performance;
}
            </div>
            
            <div class="comparison">
                <div class="before">
                    <h5>🔴 当前状态</h5>
                    <p>只检查数据库连接是否成功，缺乏性能指标监控</p>
                </div>
                <div class="after">
                    <h5>🟢 增强后</h5>
                    <p>全面监控连接数、慢查询、缓存命中率等关键性能指标</p>
                </div>
            </div>
        </div>

        <div class="enhancement-card proposed priority-high">
            <h3>高优先级增强 - 系统资源监控</h3>
            
            <h4>服务器资源检查</h4>
            <div class="code-snippet php-code">
// 系统资源监控
function getSystemResources() {
    $resources = [];
    
    // CPU负载
    if (function_exists('sys_getloadavg')) {
        $load = sys_getloadavg();
        $resources['cpu_load'] = [
            '1min' => $load[0],
            '5min' => $load[1],
            '15min' => $load[2]
        ];
    }
    
    // 内存使用
    $resources['memory'] = [
        'current_usage' => memory_get_usage(true),
        'peak_usage' => memory_get_peak_usage(true),
        'limit' => ini_get('memory_limit')
    ];
    
    // 磁盘空间
    $resources['disk'] = [
        'free_space' => disk_free_space('/'),
        'total_space' => disk_total_space('/'),
        'usage_percent' => round((1 - (disk_free_space('/') / disk_total_space('/'))) * 100, 2)
    ];
    
    // PHP配置检查
    $resources['php_config'] = [
        'version' => PHP_VERSION,
        'max_execution_time' => ini_get('max_execution_time'),
        'upload_max_filesize' => ini_get('upload_max_filesize'),
        'post_max_size' => ini_get('post_max_size'),
        'extensions' => get_loaded_extensions()
    ];
    
    return $resources;
}
            </div>
        </div>

        <div class="enhancement-card proposed priority-medium">
            <h3>中优先级增强 - 实时监控</h3>
            
            <h4>AJAX实时更新</h4>
            <div class="code-snippet js-code">
// 实时监控更新
class DiagnosticMonitor {
    constructor() {
        this.updateInterval = 30000; // 30秒更新一次
        this.isRunning = false;
    }
    
    start() {
        if (this.isRunning) return;
        
        this.isRunning = true;
        this.update();
        this.intervalId = setInterval(() => this.update(), this.updateInterval);
        
        // 更新UI状态
        document.getElementById('monitor-status').textContent = '监控中...';
        document.getElementById('monitor-toggle').textContent = '停止监控';
    }
    
    stop() {
        if (!this.isRunning) return;
        
        this.isRunning = false;
        clearInterval(this.intervalId);
        
        // 更新UI状态
        document.getElementById('monitor-status').textContent = '已停止';
        document.getElementById('monitor-toggle').textContent = '开始监控';
    }
    
    async update() {
        try {
            const response = await fetch('/api/diagnostic/realtime');
            const data = await response.json();
            
            this.updateMetrics(data);
            this.updateTimestamp();
            
        } catch (error) {
            console.error('监控更新失败:', error);
            this.handleError(error);
        }
    }
    
    updateMetrics(data) {
        // 更新数据库连接数
        const connectionElement = document.getElementById('db-connections');
        if (connectionElement) {
            connectionElement.textContent = `${data.database.connected}/${data.database.max_connections}`;
            
            // 根据使用率更新颜色
            const usage = (data.database.connected / data.database.max_connections) * 100;
            connectionElement.className = usage > 80 ? 'text-danger' : usage > 60 ? 'text-warning' : 'text-success';
        }
        
        // 更新系统负载
        const loadElement = document.getElementById('system-load');
        if (loadElement) {
            loadElement.textContent = data.system.cpu_load['1min'].toFixed(2);
            loadElement.className = data.system.cpu_load['1min'] > 2 ? 'text-danger' : 'text-success';
        }
        
        // 更新内存使用
        const memoryElement = document.getElementById('memory-usage');
        if (memoryElement) {
            const memoryPercent = (data.system.memory.current_usage / data.system.memory.limit * 100).toFixed(1);
            memoryElement.textContent = `${memoryPercent}%`;
            memoryElement.className = memoryPercent > 85 ? 'text-danger' : memoryPercent > 70 ? 'text-warning' : 'text-success';
        }
    }
    
    updateTimestamp() {
        const timestampElement = document.getElementById('last-update');
        if (timestampElement) {
            timestampElement.textContent = new Date().toLocaleString();
        }
    }
    
    handleError(error) {
        // 显示错误提示
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-warning alert-dismissible fade show';
        alertDiv.innerHTML = `
            <strong>监控更新失败:</strong> ${error.message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.querySelector('.container-fluid').prepend(alertDiv);
        
        // 3秒后自动关闭
        setTimeout(() => {
            alertDiv.remove();
        }, 3000);
    }
}

// 初始化监控
const monitor = new DiagnosticMonitor();

// 页面加载完成后启动
document.addEventListener('DOMContentLoaded', function() {
    // 添加监控控制按钮
    const controlPanel = document.createElement('div');
    controlPanel.className = 'alert alert-info';
    controlPanel.innerHTML = `
        <div class="d-flex justify-content-between align-items-center">
            <span>实时监控状态: <span id="monitor-status">已停止</span></span>
            <div>
                <span class="me-3">最后更新: <span id="last-update">--</span></span>
                <button id="monitor-toggle" class="btn btn-sm btn-primary" onclick="toggleMonitor()">开始监控</button>
            </div>
        </div>
    `;
    
    document.querySelector('.container-fluid').prepend(controlPanel);
});

function toggleMonitor() {
    if (monitor.isRunning) {
        monitor.stop();
    } else {
        monitor.start();
    }
}
            </div>
        </div>

        <div class="enhancement-card advanced priority-medium">
            <h3>中优先级增强 - 历史趋势分析</h3>
            
            <h4>性能历史记录</h4>
            <div class="code-snippet php-code">
// 创建监控历史表
$create_history_table = "
CREATE TABLE IF NOT EXISTS diagnostic_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    metric_type VARCHAR(50) NOT NULL,
    metric_value DECIMAL(10,2) NOT NULL,
    additional_data JSON,
    status ENUM('normal', 'warning', 'critical') DEFAULT 'normal',
    INDEX idx_timestamp (timestamp),
    INDEX idx_metric_type (metric_type),
    INDEX idx_status (status)
)";

// 记录监控数据
function recordDiagnosticData($metrics) {
    global $link;
    
    foreach ($metrics as $type => $data) {
        if (is_array($data)) {
            foreach ($data as $subtype => $value) {
                if (is_numeric($value)) {
                    $metric_type = $type . '_' . $subtype;
                    $status = determineStatus($metric_type, $value);
                    
                    $sql = "INSERT INTO diagnostic_history (metric_type, metric_value, additional_data, status) 
                            VALUES (?, ?, ?, ?)";
                    
                    $stmt = mysqli_prepare($link, $sql);
                    $additional_json = json_encode($data);
                    mysqli_stmt_bind_param($stmt, 'sdss', $metric_type, $value, $additional_json, $status);
                    mysqli_stmt_execute($stmt);
                }
            }
        }
    }
}

// 获取历史趋势数据
function getHistoricalTrends($metric_type, $hours = 24) {
    global $link;
    
    $sql = "SELECT 
                DATE_FORMAT(timestamp, '%H:%i') as time_label,
                AVG(metric_value) as avg_value,
                MIN(metric_value) as min_value,
                MAX(metric_value) as max_value,
                COUNT(*) as sample_count
            FROM diagnostic_history 
            WHERE metric_type = ? 
            AND timestamp >= DATE_SUB(NOW(), INTERVAL ? HOUR)
            GROUP BY DATE_FORMAT(timestamp, '%Y-%m-%d %H')
            ORDER BY timestamp";
    
    $stmt = mysqli_prepare($link, $sql);
    mysqli_stmt_bind_param($stmt, 'si', $metric_type, $hours);
    mysqli_stmt_execute($stmt);
    
    return mysqli_stmt_get_result($stmt);
}
            </div>
        </div>

        <h2>📈 增强功能对比表</h2>

        <table class="table">
            <thead>
                <tr>
                    <th>功能模块</th>
                    <th>当前状态</th>
                    <th>增强后</th>
                    <th>优先级</th>
                    <th>实施难度</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>数据库监控</td>
                    <td>基础连接检查</td>
                    <td>性能指标、连接池、慢查询监控</td>
                    <td>高</td>
                    <td>中</td>
                </tr>
                <tr>
                    <td>系统资源</td>
                    <td>无</td>
                    <td>CPU、内存、磁盘监控</td>
                    <td>高</td>
                    <td>低</td>
                </tr>
                <tr>
                    <td>实时更新</td>
                    <td>手动刷新</td>
                    <td>AJAX自动更新</td>
                    <td>中</td>
                    <td>中</td>
                </tr>
                <tr>
                    <td>历史趋势</td>
                    <td>无</td>
                    <td>24小时趋势图表</td>
                    <td>中</td>
                    <td>高</td>
                </tr>
                <tr>
                    <td>告警系统</td>
                    <td>无</td>
                    <td>阈值告警、邮件通知</td>
                    <td>低</td>
                    <td>高</td>
                </tr>
                <tr>
                    <td>性能基准</td>
                    <td>无</td>
                    <td>查询性能测试</td>
                    <td>低</td>
                    <td>中</td>
                </tr>
            </tbody>
        </table>

        <h2>🎯 实施建议</h2>

        <div class="feature-list priority-high">
            <h3>第一阶段：核心功能增强（1-2周）</h3>
            <ul>
                <li>添加数据库性能监控指标</li>
                <li>实现系统资源检查</li>
                <li>优化现有界面显示</li>
                <li>添加更多诊断项目</li>
            </ul>
        </div>

        <div class="feature-list priority-medium">
            <h3>第二阶段：实时监控（2-3周）</h3>
            <ul>
                <li>实现AJAX实时更新</li>
                <li>添加监控控制面板</li>
                <li>创建API接口</li>
                <li>增强用户交互</li>
            </ul>
        </div>

        <div class="feature-list priority-low">
            <h3>第三阶段：高级功能（3-4周）</h3>
            <ul>
                <li>历史数据记录和分析</li>
                <li>趋势图表展示</li>
                <li>告警系统实现</li>
                <li>性能基准测试</li>
            </ul>
        </div>

        <h2>💡 技术要点</h2>

        <div class="enhancement-card">
            <h3>关键技术考虑</h3>
            
            <h4>性能优化</h4>
            <ul>
                <li>使用缓存减少重复查询</li>
                <li>异步加载减少页面阻塞</li>
                <li>合理设置更新频率</li>
            </ul>
            
            <h4>数据安全</h4>
            <ul>
                <li>限制敏感信息的显示</li>
                <li>添加访问权限控制</li>
                <li>记录操作日志</li>
            </ul>
            
            <h4>用户体验</h4>
            <ul>
                <li>响应式设计适配移动端</li>
                <li>清晰的状态指示</li>
                <li>友好的错误提示</li>
            </ul>
        </div>

        <div style="text-align: center; margin-top: 40px; color: #7f8c8d;">
            <p>📅 建议版本：v1.0</p>
            <p>🔄 最后更新：2025年8月7日</p>
            <p>📧 技术支持：<EMAIL></p>
        </div>
    </div>
</body>
</html>
