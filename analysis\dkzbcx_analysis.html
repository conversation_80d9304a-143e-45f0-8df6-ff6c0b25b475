<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>dkzbcx.php 数据来源分析</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }
        h3 {
            color: #2980b9;
            margin-top: 20px;
        }
        .data-source {
            background: #ecf0f1;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #3498db;
        }
        .static-data {
            background: #fff3cd;
            border-left-color: #ffc107;
        }
        .dynamic-data {
            background: #d1ecf1;
            border-left-color: #17a2b8;
        }
        .table-info {
            background: #d4edda;
            border-left-color: #28a745;
        }
        .code-snippet {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        .tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            margin-right: 5px;
        }
        .tag-static { background: #ffc107; color: #212529; }
        .tag-dynamic { background: #17a2b8; color: white; }
        .tag-table { background: #28a745; color: white; }
        .tag-function { background: #6f42c1; color: white; }
        .tag-form { background: #fd7e14; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <h1>dkzbcx.php 页面数据来源分析</h1>
        
        <h2>页面概述</h2>
        <p>dkzbcx.php 是一个代打卡监测系统页面，用于检测和分析员工打卡异常行为。通过分析同一设备串号被多个用户使用的情况，识别潜在的代打卡行为。</p>
        
        <h2>数据来源分析</h2>
        
        <h3>1. 用户输入数据</h3>
        
        <div class="data-source static-data">
            <h4><span class="tag tag-form">表单</span>日期范围选择</h4>
            <p><strong>数据内容：</strong>查询的开始日期和结束日期</p>
            <p><strong>来源：</strong>用户通过表单输入</p>
            <div class="code-snippet">
$startDate = isset($_POST['start-date']) ? $_POST['start-date'] : $firstDayOfMonth;
$endDate = isset($_POST['end-date']) ? $_POST['end-date'] : $lastDayOfMonth;
            </div>
            <p><strong>默认值：</strong>如果用户未输入，默认为当月第一天到最后一天</p>
        </div>
        
        <h3>2. 静态数据</h3>
        
        <div class="data-source static-data">
            <h4><span class="tag tag-static">静态</span>风险等级评估规则</h4>
            <p><strong>数据内容：</strong>根据打卡次数判断风险等级的规则</p>
            <p><strong>来源：</strong>硬编码在PHP代码中</p>
            <div class="code-snippet">
if ($count >= 20) {
    $risk_level = '高风险';
    $risk_class = 'bg-danger';
} elseif ($count >= 10) {
    $risk_level = '中风险';
    $risk_class = 'bg-warning';
} else {
    $risk_level = '低风险';
    $risk_class = 'bg-info';
}
            </div>
            <p><strong>说明：</strong>≥20次为高风险，10-19次为中风险，<10次为低风险</p>
        </div>
        
        <div class="data-source static-data">
            <h4><span class="tag tag-static">静态</span>时间计算</h4>
            <p><strong>数据内容：</strong>当月第一天和最后一天</p>
            <p><strong>来源：</strong>PHP date函数计算</p>
            <div class="code-snippet">
$firstDayOfMonth = date('Y-m-01');
$lastDayOfMonth = date('Y-m-t');
            </div>
            <p><strong>说明：</strong>用于设置默认查询范围</p>
        </div>
        
        <h3>3. 动态数据（数据库查询）</h3>
        
        <div class="data-source dynamic-data">
            <h4><span class="tag tag-dynamic">动态</span><span class="tag tag-table">tuqoa_kqdkjl</span>代打卡异常检测</h4>
            <p><strong>数据内容：</strong>检测同一设备被多个用户使用的打卡记录</p>
            <p><strong>来源：</strong>tuqoa_kqdkjl（考勤打卡记录表）</p>
            <div class="code-snippet">
SELECT
    t.uid AS 'id',
    t.device AS 'ch',
    COUNT(*) AS 'dkcs'
FROM
    tuqoa_kqdkjl t
JOIN (
    SELECT device
    FROM tuqoa_kqdkjl
    WHERE dkdt >= '$startDate' AND dkdt <= '$endDate'
    GROUP BY device
    HAVING COUNT(DISTINCT uid) > 1
) s ON t.device = s.device
WHERE
    t.dkdt >= '$startDate' AND t.dkdt <= '$endDate'
GROUP BY
    t.uid, t.device
ORDER BY
    t.device, COUNT(*) DESC
            </div>
            <p><strong>查询逻辑：</strong></p>
            <ul>
                <li>子查询：找出被多个用户使用的设备（COUNT(DISTINCT uid) > 1）</li>
                <li>主查询：统计每个用户在这些设备上的打卡次数</li>
                <li>按设备分组，按打卡次数降序排列</li>
            </ul>
        </div>
        
        <div class="data-source dynamic-data">
            <h4><span class="tag tag-dynamic">动态</span><span class="tag tag-table">tuqoa_userinfo</span>用户姓名查询</h4>
            <p><strong>数据内容：</strong>根据用户ID获取用户姓名</p>
            <p><strong>来源：</strong>tuqoa_userinfo（用户信息表）</p>
            <div class="code-snippet">
SELECT name FROM `tuqoa_userinfo` WHERE `id`=".$row["id"]
            </div>
            <p><strong>说明：</strong>为每个异常用户ID查询对应的姓名信息</p>
        </div>
        
        <h3>4. 计算数据</h3>
        
        <div class="data-source table-info">
            <h4><span class="tag tag-function">计算</span>统计汇总数据</h4>
            <p><strong>数据内容：</strong>异常记录总数、高风险用户数、检测天数</p>
            <p><strong>计算方式：</strong></p>
            <ul>
                <li><strong>异常记录总数：</strong>$total_records++（循环计数）</li>
                <li><strong>高风险用户数：</strong>$high_risk_count++（打卡次数≥20的用户）</li>
                <li><strong>检测天数：</strong>(strtotime($endDate) - strtotime($startDate)) / (60 * 60 * 24) + 1</li>
            </ul>
        </div>
        
        <h2>数据表结构</h2>
        
        <div class="table-info">
            <h4>主要数据表</h4>
            <ul>
                <li><strong>tuqoa_kqdkjl：</strong>考勤打卡记录表
                    <ul>
                        <li>uid：用户ID</li>
                        <li>device：设备串号</li>
                        <li>dkdt：打卡日期时间</li>
                    </ul>
                </li>
                <li><strong>tuqoa_userinfo：</strong>用户信息表
                    <ul>
                        <li>id：用户ID</li>
                        <li>name：用户姓名</li>
                    </ul>
                </li>
            </ul>
        </div>
        
        <h2>页面功能特点</h2>
        
        <h3>1. 表格展示</h3>
        <ul>
            <li>显示异常打卡用户的详细信息</li>
            <li>包含用户ID、姓名、设备串号、打卡次数、风险等级</li>
            <li>支持Excel导出功能</li>
        </ul>
        
        <h3>2. 统计卡片</h3>
        <ul>
            <li>异常记录总数统计</li>
            <li>高风险用户数统计</li>
            <li>检测天数显示</li>
        </ul>
        
        <h3>3. 交互功能</h3>
        <ul>
            <li>日期范围选择器</li>
            <li>数据查询和刷新</li>
            <li>Excel导出功能</li>
            <li>实时时间更新</li>
        </ul>
        
        <h2>检测算法</h2>
        
        <div class="data-source table-info">
            <h4>代打卡检测逻辑</h4>
            <p><strong>检测原理：</strong>通过分析同一设备串号被多个用户使用的情况来识别代打卡行为</p>
            <p><strong>检测步骤：</strong></p>
            <ol>
                <li>查找在指定时间范围内被多个不同用户使用的设备</li>
                <li>统计每个用户在这些"共享设备"上的打卡次数</li>
                <li>根据打卡次数评估风险等级</li>
                <li>按设备和打卡次数排序展示结果</li>
            </ol>
        </div>
        
        <h2>总结</h2>
        <p>dkzbcx.php页面主要用于代打卡监测，数据来源包括：</p>
        <ul>
            <li><strong>用户输入：</strong>查询日期范围</li>
            <li><strong>静态数据：</strong>风险等级评估规则、时间计算逻辑</li>
            <li><strong>动态数据：</strong>从tuqoa_kqdkjl和tuqoa_userinfo表查询的打卡记录和用户信息</li>
            <li><strong>计算数据：</strong>统计汇总的异常记录数、高风险用户数等</li>
        </ul>
        <p>该页面通过复杂的SQL查询实现代打卡检测算法，能够有效识别潜在的打卡异常行为。</p>
    </div>
</body>
</html>
