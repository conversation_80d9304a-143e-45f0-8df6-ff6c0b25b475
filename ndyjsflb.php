<?php
include '../config.php';
$defaultYear = date('Y');
$startDate = isset($_POST['year-select']) ? $_POST['year-select'] : $defaultYear;
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>年度预计收费列表</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/boxicons@2.0.7/css/boxicons.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="styles/main.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        /* 页面特定样式 */
        .date-range-container input[type="number"] {
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            width: 100px;
            margin-right: 10px;
        }

        .date-range-container input[type="number"]:focus {
            border-color: #86b7fe;
            outline: 0;
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bx bx-calendar me-2"></i>
                年度预计收费列表
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text text-white">
                    <i class="bx bx-time me-1"></i>
                    最后更新: <span id="last-update-time"><?php echo date('Y-m-d H:i:s'); ?></span>
                </span>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
            
        <!-- 年份选择器 -->
        <div class="date-range-container">
            <form method="post" action="">
                <div class="form-group">
                    <label for="year-select">
                        <i class="bx bx-calendar me-1"></i>选择年份:
                    </label>
                    <input type="number" id="year-select" name="year-select" min="2000" max="2100" value="<?php echo $startDate; ?>">
                    <button type="submit">
                        <i class="bx bx-search me-1"></i>查询
                    </button>
                </div>
            </form>
        </div>
            
        <!-- 统计卡片区域 -->
        <div class="row mb-4">
            <?php
            $expected_amount = 0;
            $received_amount = 0;
            $collection_rate = 0;
            $sql="SELECT ifnull(sum(yjje),0) as yjjehj,ifnull(sum(ysje),0) as  ysjehj FROM `tuqoa_htsf`  WHERE  `yjsj` like '$startDate%'";
            $result = mysqli_query($link, $sql);
            if ($result) {
                while ($row1 = mysqli_fetch_assoc($result)) {
                    $expected_amount=$row1["yjjehj"];
                    $received_amount=$row1["ysjehj"];
                    if($expected_amount>0){
                        $collection_rate=number_format(($received_amount/$expected_amount)*100,2);
                    }
                }
            }
            ?>
            <div class="col-md-3">
                <div class="card stat-card stat-card-primary">
                    <div class="card-body">
                        <i class="fas fa-file-invoice-dollar stat-icon"></i>
                        <h5 class="card-title">年度预计收费总额</h5>
                        <h2 class="card-text">¥<?php echo number_format($expected_amount, 1); ?></h2>
                        <p class="stat-info">万元</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card stat-card-success">
                    <div class="card-body">
                        <i class="fas fa-hand-holding-usd stat-icon"></i>
                        <h5 class="card-title">年度实际收款总额</h5>
                        <h2 class="card-text">¥<?php echo number_format($received_amount, 1); ?></h2>
                        <p class="stat-info">万元</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card stat-card-warning">
                    <div class="card-body">
                        <i class="fas fa-percentage stat-icon"></i>
                        <h5 class="card-title">年度收款率</h5>
                        <h2 class="card-text"><?php echo $collection_rate; ?>%</h2>
                        <p class="stat-info">完成率</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card stat-card stat-card-info">
                        <div class="card-body">
                            <i class="fas fa-file-invoice stat-icon"></i>
                            <h5 class="card-title">年度开票总额</h5>
                            <h2 class="card-text">¥17,500万</h2>
                            <p class="stat-info">开票金额</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <?php
                $month=[];
                $yjjehj=[];
                $ysjehj=[];

                // 使用循环查询每个月的数据，避免CTE兼容性问题
                for ($i = 1; $i <= 12; $i++) {
                    $monthStr = sprintf('%02d', $i);
                    $monthLabel = $monthStr . "月";
                    $month[] = $monthLabel;

                    $sql = "SELECT
                        IFNULL(SUM(yjje), 0) AS yjjehj,
                        IFNULL(SUM(ysje), 0) AS ysjehj
                    FROM tuqoa_htsf
                    WHERE DATE_FORMAT(yjsj, '%Y-%m') = '$startDate-$monthStr'";

                    $result = mysqli_query($link, $sql);
                    if ($result) {
                        $row = mysqli_fetch_assoc($result);
                        $yjjehj[] = (float)$row["yjjehj"];
                        $ysjehj[] = (float)$row["ysjehj"];
                    } else {
                        $yjjehj[] = 0;
                        $ysjehj[] = 0;
                    }
                }

                // 查询项目年度收款数据
                $projectNames = [];
                $projectAmounts = [];
                $projectSql = "SELECT
                    project,
                    SUM(ysje) as total_received
                FROM tuqoa_htsf
                WHERE yjsj LIKE '$startDate%'
                    AND ysje > 0
                GROUP BY project
                ORDER BY total_received DESC
                LIMIT 10";

                $projectResult = mysqli_query($link, $projectSql);
                if ($projectResult) {
                    while ($row = mysqli_fetch_assoc($projectResult)) {
                        $projectNames[] = $row["project"];
                        $projectAmounts[] = (float)$row["total_received"];
                    }
                }

                // 如果没有数据，提供默认数据
                if (empty($projectNames)) {
                    $projectNames = ['暂无数据'];
                    $projectAmounts = [1];
                }

                // 查询年度收款完成情况数据（按月统计）
                $monthLabels = [];
                $plannedAmounts = [];
                $actualAmounts = [];
                $completionRates = [];

                for ($i = 1; $i <= 12; $i++) {
                    $monthStr = sprintf('%02d', $i);
                    $monthLabels[] = $monthStr . "月";

                    // 查询当月预计收款金额
                    $plannedSql = "SELECT IFNULL(SUM(yjje), 0) AS planned
                                  FROM tuqoa_htsf
                                  WHERE DATE_FORMAT(yjsj, '%Y-%m') = '$startDate-$monthStr'";

                    // 查询当月实际收款金额
                    $actualSql = "SELECT IFNULL(SUM(ysje), 0) AS actual
                                 FROM tuqoa_htsf
                                 WHERE DATE_FORMAT(sksj, '%Y-%m') = '$startDate-$monthStr'";

                    $plannedResult = mysqli_query($link, $plannedSql);
                    $actualResult = mysqli_query($link, $actualSql);

                    $planned = 0;
                    $actual = 0;

                    if ($plannedResult) {
                        $row = mysqli_fetch_assoc($plannedResult);
                        $planned = (float)$row["planned"];
                    }

                    if ($actualResult) {
                        $row = mysqli_fetch_assoc($actualResult);
                        $actual = (float)$row["actual"];
                    }

                    $plannedAmounts[] = $planned;
                    $actualAmounts[] = $actual;

                    // 计算完成率
                    if ($planned > 0) {
                        $completionRates[] = round(($actual / $planned) * 100, 1);
                    } else {
                        $completionRates[] = 0;
                    }
                }
                ?>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">年度收款趋势</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="quarterlyPaymentChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">项目年度收款分布</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="projectTypeAnnualChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">项目收费明细</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>项目名称</th>
                                            <th>合同名称</th>
                                            <th>合同总额</th>
                                            <th>预计收费总额</th>
                                            <th>已收金额</th>
                                            <th>未收金额</th>
                                            <th>收款率</th>
                                            <th>收款日期</th>
                                            <th>最近开票日期</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        $sql="SELECT *,(select fwf from tuqoa_htgl where tuqoa_htgl.htmc=tuqoa_htsf.htmc LIMIT 1) fwf FROM `tuqoa_htsf`  WHERE  `yjsj` like '$startDate%'";
                                        $result = mysqli_query($link, $sql);
                                        if ($result) {
                                            while ($row = mysqli_fetch_assoc($result)) {
                                        ?>
                                        <tr>
                                            <td><?php echo $row["project"]; ?></td>
                                            <td><?php echo $row["htmc"]; ?></td>
                                            <td>¥<?php echo $row["fwf"]; ?>万</td>
                                            <td>¥<?php echo $row["yjje"]; ?>万</td>
                                            <td>¥<?php echo $row["ysje"]; ?>万</td>
                                            <td>¥<?php echo ($row["yjje"]-$row["ysje"]); ?>万</td>
                                            <td><?php echo number_format(($row["ysje"]/$row["yjje"])*100,2); ?>%</td>
                                            <td><?php echo $row["sksj"]; ?></td>
                                            <td>2024-03-10</td>
                                        </tr>
                                        <?php
                                            }
                                        } else {
                                            echo "<tr><td colspan='9'>数据库查询错误: " . mysqli_error($link) . "</td></tr>";
                                        }
                                        ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">年度收款计划明细</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>项目编号</th>
                                            <th>项目名称</th>
                                            <th>季度</th>
                                            <th>计划收款日期</th>
                                            <th>计划收款金额</th>
                                            <th>实际收款日期</th>
                                            <th>实际收款金额</th>
                                            <th>差额</th>
                                            <th>计划开票日期</th>
                                            <th>实际开票日期</th>
                                            <th>开票金额</th>
                                            <th>状态</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!--
                                        <tr>
                                            <td>P20240301</td>
                                            <td>某市政道路工程</td>
                                            <td>Q1</td>
                                            <td>2024-03-15</td>
                                            <td>¥200万</td>
                                            <td>2024-03-15</td>
                                            <td>¥200万</td>
                                            <td>¥0</td>
                                            <td>2024-03-10</td>
                                            <td>2024-03-10</td>
                                            <td>¥200万</td>
                                            <td><span class="badge bg-success">正常</span></td>
                                        </tr>-->
                                        
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">年度收款完成情况</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="quarterlyCompletionChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">项目年度收款占比</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="projectTypePercentageChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // 设置默认年份
        document.addEventListener('DOMContentLoaded', function() {
            const currentYear = new Date().getFullYear();
            //document.getElementById('year-select').value = currentYear;

            // 查询按钮点击事件（如果存在）
            const queryBtn = document.getElementById('query-btn');
            if (queryBtn) {
                queryBtn.addEventListener('click', function() {
                const selectedYear = document.getElementById('year-select').value;
                
                    // 这里可以添加查询逻辑，例如AJAX请求获取数据
                    console.log('查询年份:', selectedYear);
                    // 模拟数据刷新
                    //alert('已更新数据，年份: ' + selectedYear);
                });
            }

            // 等待页面完全加载后再初始化图表
            setTimeout(function() {
                initCharts();
            }, 100);
        });
        
        // 初始化图表
        function initCharts() {
            console.log('开始初始化图表...');

            // 季度收款趋势图表
            const quarterlyPaymentCtx = document.getElementById('quarterlyPaymentChart');
            if (!quarterlyPaymentCtx) {
                console.error('找不到 quarterlyPaymentChart 元素');
                return;
            }

            try {
                new Chart(quarterlyPaymentCtx.getContext('2d'), {
                type: 'line',
                data: {
                    labels: <?php echo json_encode($month); ?>,
                    datasets: [
                        {
                            label: '预计收款',
                            data: <?php echo json_encode($yjjehj); ?>,
                            borderColor: '#1e88e5',
                            backgroundColor: 'rgba(30, 136, 229, 0.1)',
                            tension: 0.3
                        },
                        {
                            label: '实际收款',
                            data: <?php echo json_encode($ysjehj); ?>,
                            borderColor: '#43a047',
                            backgroundColor: 'rgba(67, 160, 71, 0.1)',
                            tension: 0.3
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '金额（万元）'
                            }
                        }
                    }
                }
            });
            console.log('季度收款趋势图表初始化完成');
            } catch (error) {
                console.error('季度收款趋势图表初始化失败:', error);
            }

            // 项目年度收款分布图表
            const projectTypeAnnualCtx = document.getElementById('projectTypeAnnualChart');
            if (projectTypeAnnualCtx) {
                try {
                const projectNames = <?php echo json_encode($projectNames); ?>;
                const projectAmounts = <?php echo json_encode($projectAmounts); ?>;

                console.log('项目年度收款分布数据:', {
                    names: projectNames,
                    amounts: projectAmounts
                });

                // 生成动态颜色
                const distributionColors = generateProjectColors(projectNames.length);

                new Chart(projectTypeAnnualCtx.getContext('2d'), {
                    type: 'pie',
                    data: {
                        labels: projectNames,
                        datasets: [{
                            data: projectAmounts,
                            backgroundColor: distributionColors,
                            borderColor: '#ffffff',
                            borderWidth: 2,
                            hoverBorderWidth: 3
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    padding: 20,
                                    usePointStyle: true,
                                    font: {
                                        size: 12
                                    },
                                    generateLabels: function(chart) {
                                        const data = chart.data;
                                        if (data.labels.length && data.datasets.length) {
                                            return data.labels.map((label, i) => {
                                                const value = data.datasets[0].data[i];
                                                // 截断过长的项目名称
                                                const shortLabel = label.length > 15 ? label.substring(0, 15) + '...' : label;
                                                return {
                                                    text: `${shortLabel}: ¥${value}万`,
                                                    fillStyle: data.datasets[0].backgroundColor[i],
                                                    strokeStyle: data.datasets[0].borderColor,
                                                    lineWidth: data.datasets[0].borderWidth,
                                                    hidden: false,
                                                    index: i
                                                };
                                            });
                                        }
                                        return [];
                                    }
                                }
                            },
                            title: {
                                display: true,
                                text: '项目年度收款分布',
                                font: {
                                    size: 16,
                                    weight: 'bold'
                                },
                                padding: 20
                            },
                            tooltip: {
                                callbacks: {
                                    title: function(context) {
                                        return context[0].label;
                                    },
                                    label: function(context) {
                                        const value = context.parsed || 0;
                                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                        const percentage = ((value / total) * 100).toFixed(1);
                                        return [
                                            `收款金额: ¥${value}万`,
                                            `占比: ${percentage}%`
                                        ];
                                    }
                                }
                            }
                        },
                        animation: {
                            animateRotate: true,
                            duration: 1200
                        }
                    }
                });
                console.log('项目年度收款分布图表初始化完成');
                } catch (error) {
                    console.error('项目年度收款分布图表初始化失败:', error);
                }
            }

            // 年度收款完成情况图表
            const quarterlyCompletionCtx = document.getElementById('quarterlyCompletionChart');
            if (quarterlyCompletionCtx) {
                try {
                const monthLabels = <?php echo json_encode($monthLabels); ?>;
                const plannedAmounts = <?php echo json_encode($plannedAmounts); ?>;
                const actualAmounts = <?php echo json_encode($actualAmounts); ?>;
                const completionRates = <?php echo json_encode($completionRates); ?>;

                console.log('年度收款完成情况数据:', {
                    months: monthLabels,
                    planned: plannedAmounts,
                    actual: actualAmounts,
                    rates: completionRates
                });

                new Chart(quarterlyCompletionCtx.getContext('2d'), {
                    type: 'bar',
                    data: {
                        labels: monthLabels,
                        datasets: [
                            {
                                label: '预计收款',
                                data: plannedAmounts,
                                backgroundColor: 'rgba(30, 136, 229, 0.6)',
                                borderColor: '#1e88e5',
                                borderWidth: 1,
                                yAxisID: 'y'
                            },
                            {
                                label: '实际收款',
                                data: actualAmounts,
                                backgroundColor: 'rgba(67, 160, 71, 0.6)',
                                borderColor: '#43a047',
                                borderWidth: 1,
                                yAxisID: 'y'
                            },
                            {
                                label: '完成率',
                                data: completionRates,
                                type: 'line',
                                backgroundColor: 'rgba(229, 57, 53, 0.1)',
                                borderColor: '#e53935',
                                borderWidth: 2,
                                fill: false,
                                tension: 0.4,
                                yAxisID: 'y1',
                                pointBackgroundColor: '#e53935',
                                pointBorderColor: '#ffffff',
                                pointBorderWidth: 2,
                                pointRadius: 4
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        interaction: {
                            mode: 'index',
                            intersect: false,
                        },
                        plugins: {
                            title: {
                                display: true,
                                text: '年度收款完成情况',
                                font: {
                                    size: 16,
                                    weight: 'bold'
                                },
                                padding: 20
                            },
                            legend: {
                                position: 'top',
                                labels: {
                                    usePointStyle: true,
                                    padding: 20
                                }
                            },
                            tooltip: {
                                callbacks: {
                                    title: function(context) {
                                        return context[0].label;
                                    },
                                    label: function(context) {
                                        const datasetLabel = context.dataset.label;
                                        const value = context.parsed.y;

                                        if (datasetLabel === '完成率') {
                                            return `${datasetLabel}: ${value}%`;
                                        } else {
                                            return `${datasetLabel}: ¥${value}万`;
                                        }
                                    }
                                }
                            }
                        },
                        scales: {
                            x: {
                                title: {
                                    display: true,
                                    text: '月份'
                                }
                            },
                            y: {
                                type: 'linear',
                                display: true,
                                position: 'left',
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: '收款金额（万元）'
                                },
                                grid: {
                                    drawOnChartArea: true,
                                }
                            },
                            y1: {
                                type: 'linear',
                                display: true,
                                position: 'right',
                                beginAtZero: true,
                                max: 120,
                                title: {
                                    display: true,
                                    text: '完成率（%）'
                                },
                                grid: {
                                    drawOnChartArea: false,
                                }
                            }
                        }
                    }
                });
                console.log('年度收款完成情况图表初始化完成');
                } catch (error) {
                    console.error('年度收款完成情况图表初始化失败:', error);
                }
            }

            // 项目年度收款占比图表
            const projectTypePercentageCtx = document.getElementById('projectTypePercentageChart');
            if (projectTypePercentageCtx) {
                try {
                const projectNames = <?php echo json_encode($projectNames); ?>;
                const projectAmounts = <?php echo json_encode($projectAmounts); ?>;

                console.log('项目名称:', projectNames);
                console.log('项目收款金额:', projectAmounts);

                // 生成动态颜色
                const colors = generateProjectColors(projectNames.length);

                new Chart(projectTypePercentageCtx.getContext('2d'), {
                    type: 'doughnut',
                    data: {
                        labels: projectNames,
                        datasets: [{
                            data: projectAmounts,
                            backgroundColor: colors,
                            borderColor: '#ffffff',
                            borderWidth: 2,
                            hoverBorderWidth: 3
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        cutout: '50%',
                        plugins: {
                            legend: {
                                position: 'right',
                                labels: {
                                    padding: 15,
                                    usePointStyle: true,
                                    font: {
                                        size: 11
                                    },
                                    generateLabels: function(chart) {
                                        const data = chart.data;
                                        if (data.labels.length && data.datasets.length) {
                                            return data.labels.map((label, i) => {
                                                const value = data.datasets[0].data[i];
                                                const total = data.datasets[0].data.reduce((a, b) => a + b, 0);
                                                const percentage = ((value / total) * 100).toFixed(1);
                                                return {
                                                    text: `${label} (${percentage}%)`,
                                                    fillStyle: data.datasets[0].backgroundColor[i],
                                                    strokeStyle: data.datasets[0].borderColor,
                                                    lineWidth: data.datasets[0].borderWidth,
                                                    hidden: false,
                                                    index: i
                                                };
                                            });
                                        }
                                        return [];
                                    }
                                }
                            },
                            title: {
                                display: true,
                                text: '项目年度收款占比',
                                font: {
                                    size: 16,
                                    weight: 'bold'
                                },
                                padding: 20
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        const label = context.label || '';
                                        const value = context.parsed || 0;
                                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                        const percentage = ((value / total) * 100).toFixed(1);
                                        return `${label}: ¥${value}万 (${percentage}%)`;
                                    }
                                }
                            }
                        },
                        animation: {
                            animateRotate: true,
                            duration: 1000
                        }
                    }
                });
                console.log('项目年度收款占比图表初始化完成');
                } catch (error) {
                    console.error('项目年度收款占比图表初始化失败:', error);
                }
            }

            console.log('所有图表初始化完成');
        }

        // 生成项目颜色的辅助函数
        function generateProjectColors(count) {
            const baseColors = [
                '#1e88e5', '#e53935', '#43a047', '#ffb300',
                '#8e24aa', '#00acc1', '#6d4c41', '#757575',
                '#f44336', '#9c27b0', '#3f51b5', '#2196f3',
                '#009688', '#4caf50', '#8bc34a', '#cddc39',
                '#ffeb3b', '#ffc107', '#ff9800', '#ff5722'
            ];

            const colors = [];
            for (let i = 0; i < count; i++) {
                colors.push(baseColors[i % baseColors.length]);
            }
            return colors;
        }

        // 调试函数
        function debugProjectData() {
            console.log('=== 项目收款数据调试信息 ===');
            console.log('项目数据:', <?php echo json_encode(array_combine($projectNames, $projectAmounts)); ?>);
            console.log('年度总收款:', <?php echo json_encode($received_amount); ?>);
            console.log('Chart.js 版本:', Chart.version);
        }

        // 页面加载完成后调用调试函数
        window.addEventListener('load', function() {
            debugProjectData();
        });
    </script>
<?php mysqli_close($link); ?>
</body>
</html> 