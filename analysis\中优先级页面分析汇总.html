<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中优先级页面分析汇总</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }
        h3 {
            color: #2980b9;
            margin-top: 20px;
        }
        .page-analysis {
            background: #f8f9fa;
            padding: 20px;
            margin: 15px 0;
            border-radius: 8px;
            border-left: 4px solid #17a2b8;
        }
        .data-source {
            background: #e9ecef;
            padding: 10px;
            margin: 8px 0;
            border-radius: 5px;
        }
        .code-snippet {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 8px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            overflow-x: auto;
        }
        .tag {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 11px;
            font-weight: bold;
            margin-right: 3px;
        }
        .tag-dynamic { background: #17a2b8; color: white; }
        .tag-table { background: #28a745; color: white; }
        .tag-chart { background: #dc3545; color: white; }
        .tag-form { background: #fd7e14; color: white; }
        .summary-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .summary-table th, .summary-table td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: left;
        }
        .summary-table th {
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>中优先级页面分析汇总</h1>
        
        <h2>页面概览</h2>
        <table class="summary-table">
            <thead>
                <tr>
                    <th>页面文件</th>
                    <th>功能描述</th>
                    <th>主要数据源</th>
                    <th>分析状态</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>gztz.php</td>
                    <td>工作台账管理</td>
                    <td>11个监理业务表</td>
                    <td>✅ 详细分析完成</td>
                </tr>
                <tr>
                    <td>xmhtdzmx.php</td>
                    <td>项目合同到账明细</td>
                    <td>tuqoa_gcproject, tuqoa_htgl, tuqoa_htsf</td>
                    <td>🔄 快速分析</td>
                </tr>
                <tr>
                    <td>myxmcbmx.php</td>
                    <td>某月项目成本明细</td>
                    <td>tuqoa_xmcztjb, tuqoa_gcproject</td>
                    <td>🔄 快速分析</td>
                </tr>
                <tr>
                    <td>xmhthzfx.php</td>
                    <td>项目合同汇总分析</td>
                    <td>tuqoa_htgl, tuqoa_gcproject</td>
                    <td>🔄 快速分析</td>
                </tr>
            </tbody>
        </table>

        <h2>详细分析</h2>

        <div class="page-analysis">
            <h3>xmhtdzmx.php - 项目合同到账明细</h3>
            
            <h4>页面功能</h4>
            <p>专门用于查看特定项目的合同和收款明细，提供项目级别的财务数据分析。</p>
            
            <h4>主要数据源</h4>
            <div class="data-source">
                <h5><span class="tag tag-form">表单</span>项目选择</h5>
                <div class="code-snippet">
SELECT * FROM `tuqoa_gcproject` 
WHERE `xmzt` not in ('完工项目','完工已结算','合同终止') 
order by id desc
                </div>
                <p>生成项目下拉选择列表，排除已完工项目</p>
            </div>
            
            <div class="data-source">
                <h5><span class="tag tag-dynamic">动态</span>项目基本信息</h5>
                <div class="code-snippet">
SELECT * FROM `tuqoa_gcproject` WHERE `id` = $gcid
                </div>
            </div>
            
            <div class="data-source">
                <h5><span class="tag tag-dynamic">动态</span>合同总额统计</h5>
                <div class="code-snippet">
SELECT IFNULL(SUM(fwf), 0) as total FROM `tuqoa_htgl` 
WHERE `projectid` = $gcid
                </div>
            </div>
            
            <div class="data-source">
                <h5><span class="tag tag-dynamic">动态</span>已收款统计</h5>
                <div class="code-snippet">
SELECT IFNULL(SUM(ysje), 0) as total FROM `tuqoa_htsf` 
WHERE `projectid` = $gcid
                </div>
            </div>
            
            <div class="data-source">
                <h5><span class="tag tag-chart">图表</span>月度收款趋势</h5>
                <div class="code-snippet">
// 预计收款
SELECT IFNULL(SUM(yjje), 0) as amount FROM `tuqoa_htsf`
WHERE `projectid` = $gcid AND DATE_FORMAT(yjsj, '%m') = '$month'

// 实际收款
SELECT IFNULL(SUM(ysje), 0) as amount FROM `tuqoa_htsf`
WHERE `projectid` = $gcid AND DATE_FORMAT(sksj, '%m') = '$month'
                </div>
                <p>生成12个月的预计vs实际收款对比图</p>
            </div>
            
            <h4>特点</h4>
            <ul>
                <li>项目级别的详细财务分析</li>
                <li>预计收款与实际收款对比</li>
                <li>月度收款趋势分析</li>
                <li>支持项目筛选</li>
            </ul>
        </div>

        <div class="page-analysis">
            <h3>myxmcbmx.php - 某月项目成本明细</h3>
            
            <h4>页面功能</h4>
            <p>查看指定月份的项目成本详细信息，支持按月份和项目筛选。</p>
            
            <h4>预期数据源</h4>
            <div class="data-source">
                <h5><span class="tag tag-dynamic">动态</span>月度成本明细</h5>
                <div class="code-snippet">
SELECT * FROM `tuqoa_xmcztjb` 
WHERE DATE_FORMAT(sbrq, '%Y-%m') = '$selectedMonth'
AND projectid = '$projectId'
                </div>
            </div>
            
            <div class="data-source">
                <h5><span class="tag tag-dynamic">动态</span>项目信息关联</h5>
                <div class="code-snippet">
SELECT gcname FROM `tuqoa_gcproject` WHERE id = '$projectId'
                </div>
            </div>
            
            <h4>预期特点</h4>
            <ul>
                <li>按月份筛选成本数据</li>
                <li>项目成本明细展示</li>
                <li>成本类型分类统计</li>
                <li>支持导出功能</li>
            </ul>
        </div>

        <div class="page-analysis">
            <h3>xmhthzfx.php - 项目合同汇总分析</h3>
            
            <h4>页面功能</h4>
            <p>对项目合同进行汇总分析，提供合同签订情况的统计和趋势分析。</p>
            
            <h4>预期数据源</h4>
            <div class="data-source">
                <h5><span class="tag tag-dynamic">动态</span>合同汇总统计</h5>
                <div class="code-snippet">
SELECT COUNT(*) as count, SUM(fwf) as total, AVG(fwf) as avg
FROM `tuqoa_htgl` 
WHERE qdsj >= '$startDate' AND qdsj <= '$endDate'
                </div>
            </div>
            
            <div class="data-source">
                <h5><span class="tag tag-dynamic">动态</span>项目合同关联</h5>
                <div class="code-snippet">
SELECT h.*, g.gcname 
FROM `tuqoa_htgl` h
LEFT JOIN `tuqoa_gcproject` g ON h.projectid = g.id
WHERE h.qdsj >= '$startDate' AND h.qdsj <= '$endDate'
                </div>
            </div>
            
            <div class="data-source">
                <h5><span class="tag tag-chart">图表</span>合同分析图表</h5>
                <ul>
                    <li>合同金额分布图</li>
                    <li>签订时间趋势图</li>
                    <li>项目类型分布图</li>
                    <li>合同状态统计图</li>
                </ul>
            </div>
            
            <h4>预期特点</h4>
            <ul>
                <li>多维度合同分析</li>
                <li>时间趋势分析</li>
                <li>项目关联分析</li>
                <li>统计图表展示</li>
            </ul>
        </div>

        <h2>数据表使用频率统计</h2>
        
        <table class="summary-table">
            <thead>
                <tr>
                    <th>数据表</th>
                    <th>使用页面</th>
                    <th>主要用途</th>
                    <th>使用频率</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>tuqoa_gcproject</td>
                    <td>xmhtdzmx.php, myxmcbmx.php, xmhthzfx.php</td>
                    <td>项目基本信息</td>
                    <td>高</td>
                </tr>
                <tr>
                    <td>tuqoa_htgl</td>
                    <td>xmhtdzmx.php, xmhthzfx.php</td>
                    <td>合同管理数据</td>
                    <td>高</td>
                </tr>
                <tr>
                    <td>tuqoa_htsf</td>
                    <td>xmhtdzmx.php</td>
                    <td>收费数据</td>
                    <td>中</td>
                </tr>
                <tr>
                    <td>tuqoa_xmcztjb</td>
                    <td>myxmcbmx.php</td>
                    <td>成本数据</td>
                    <td>中</td>
                </tr>
                <tr>
                    <td>监理业务表(11个)</td>
                    <td>gztz.php</td>
                    <td>工作记录</td>
                    <td>专用</td>
                </tr>
            </tbody>
        </table>

        <h2>技术特点总结</h2>
        
        <div class="data-source">
            <h4>共同特点</h4>
            <ul>
                <li><strong>项目关联：</strong>大部分页面都支持按项目筛选</li>
                <li><strong>时间维度：</strong>支持按月份或日期范围查询</li>
                <li><strong>多表关联：</strong>通过projectid关联项目信息</li>
                <li><strong>数据聚合：</strong>使用SUM、COUNT等聚合函数</li>
                <li><strong>图表展示：</strong>提供趋势分析和分布统计</li>
            </ul>
        </div>
        
        <div class="data-source">
            <h4>差异化特点</h4>
            <ul>
                <li><strong>gztz.php：</strong>多表整合，单日聚焦，工作记录详细</li>
                <li><strong>xmhtdzmx.php：</strong>项目级别，财务明细，收款分析</li>
                <li><strong>myxmcbmx.php：</strong>月度维度，成本明细，支出分析</li>
                <li><strong>xmhthzfx.php：</strong>合同汇总，多维分析，趋势统计</li>
            </ul>
        </div>

        <h2>业务价值</h2>
        
        <div class="data-source">
            <h4>管理价值</h4>
            <ul>
                <li><strong>工作监控：</strong>gztz.php提供日常工作记录和统计</li>
                <li><strong>财务管理：</strong>xmhtdzmx.php提供项目收款明细</li>
                <li><strong>成本控制：</strong>myxmcbmx.php提供成本明细分析</li>
                <li><strong>合同分析：</strong>xmhthzfx.php提供合同汇总分析</li>
            </ul>
        </div>

        <h2>建议</h2>
        
        <div class="data-source">
            <h4>功能完善建议</h4>
            <ul>
                <li>增加数据导出功能</li>
                <li>添加更多筛选条件</li>
                <li>优化图表交互性</li>
                <li>增加数据钻取功能</li>
                <li>添加数据对比分析</li>
            </ul>
        </div>

        <h2>总结</h2>
        <p>中优先级页面主要专注于具体业务领域的详细分析：</p>
        <ul>
            <li><strong>工作管理：</strong>gztz.php提供全面的工作台账管理</li>
            <li><strong>财务明细：</strong>xmhtdzmx.php提供项目级财务分析</li>
            <li><strong>成本分析：</strong>myxmcbmx.php提供月度成本明细</li>
            <li><strong>合同分析：</strong>xmhthzfx.php提供合同汇总分析</li>
        </ul>
        <p>这些页面为业务管理提供了重要的数据支持和分析工具。</p>
    </div>
</body>
</html>
