<?php
include '../config.php';
error_reporting(E_ALL);
ini_set('display_errors', 1);
session_start();

// 设置测试用的session变量
$_SESSION['xinhu_adminid'] = 1;
$_SESSION['xinhu_projectid'] = 1;
$_SESSION['xinhu_project'] = '测试项目';

// 获取项目列表
$projectListSql = "SELECT id, gcname FROM tuqoa_gcproject ORDER BY id DESC LIMIT 3";
$projectListResult = mysqli_query($link, $projectListSql);
$projectList = [];
if ($projectListResult) {
    while ($projectRow = mysqli_fetch_assoc($projectListResult)) {
        $projectList[] = $projectRow;
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>清爽表格设计展示</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/boxicons@2.0.7/css/boxicons.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            color: #2d3748;
            line-height: 1.6;
        }
        
        .navbar {
            background: #ffffff;
            border-bottom: 1px solid #e2e8f0;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            padding: 1rem 0;
        }
        
        .navbar-brand {
            font-weight: 600;
            font-size: 1.4rem;
            color: #1a202c !important;
            letter-spacing: -0.025em;
        }
        
        .navbar-brand i {
            color: #3182ce;
            margin-right: 8px;
        }
        
        .demo-card {
            background: #ffffff;
            border-radius: 12px;
            padding: 24px;
            margin: 20px 0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
            border: 1px solid #e2e8f0;
            animation: fadeInUp 0.6s ease-out;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        .comparison-item {
            background: #f8fafc;
            border-radius: 8px;
            padding: 20px;
            border: 1px solid #e2e8f0;
        }
        
        .demo-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
            margin: 15px 0;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .demo-table th {
            background: #f7fafc;
            color: #2d3748;
            padding: 12px 16px;
            text-align: left;
            font-weight: 600;
            font-size: 13px;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .demo-table td {
            padding: 12px 16px;
            border-bottom: 1px solid #f1f5f9;
            color: #374151;
        }
        
        .old-style .category-header {
            background: #2d3748 !important;
            color: white !important;
        }
        
        .old-style .subcategory-header {
            background: #4a5568 !important;
            color: white !important;
        }
        
        .old-style .total-row {
            background: #edf2f7 !important;
        }
        
        .new-style .category-header {
            background: #ffffff !important;
            color: #1a202c !important;
            border-top: 2px solid #3182ce !important;
            border-left: 4px solid #3182ce !important;
            font-weight: 700;
        }
        
        .new-style .subcategory-header {
            background: #ffffff !important;
            color: #2d3748 !important;
            border-left: 3px solid #60a5fa !important;
            font-weight: 600;
        }
        
        .new-style .total-row {
            background: #ffffff !important;
            border-top: 2px solid #3182ce !important;
            border-bottom: 2px solid #3182ce !important;
            font-weight: 700;
        }
        
        .demo-link {
            display: inline-block;
            padding: 10px 20px;
            background: #3182ce;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.2s ease;
            margin: 8px;
        }
        
        .demo-link:hover {
            background: #2c5aa0;
            color: white;
            text-decoration: none;
            transform: translateY(-1px);
        }
        
        .demo-link.success {
            background: #059669;
        }
        
        .demo-link.success:hover {
            background: #047857;
        }
        
        .highlight-box {
            background: linear-gradient(135deg, #ebf8ff 0%, #e6fffa 100%);
            border: 1px solid #bee3f8;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            display: flex;
            align-items: center;
        }
        
        .feature-list i {
            color: #059669;
            margin-right: 10px;
            font-size: 16px;
        }
        
        @media (max-width: 768px) {
            .comparison-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg sticky-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bx bx-table"></i>
                清爽表格设计展示
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text">
                    <i class="bx bx-time me-1"></i>
                    <?php echo date('Y-m-d H:i:s'); ?>
                </span>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="demo-card">
            <h1 class="text-center mb-4">🎨 清爽表格设计</h1>
            <p class="text-center lead text-muted">
                去掉表格中的灰色和深灰色背景，打造更加简洁清爽的视觉效果
            </p>
        </div>

        <div class="demo-card">
            <h2>📊 设计对比</h2>
            <div class="comparison-grid">
                <div class="comparison-item">
                    <h5>🔴 修改前 - 深色背景</h5>
                    <table class="demo-table old-style">
                        <tr class="category-header">
                            <td colspan="2">项目费用明细 (深灰背景)</td>
                        </tr>
                        <tr class="subcategory-header">
                            <td colspan="2">直接费 (中灰背景)</td>
                        </tr>
                        <tr>
                            <td>实发工资</td>
                            <td>¥50,000.00</td>
                        </tr>
                        <tr class="total-row">
                            <td><strong>合计</strong></td>
                            <td><strong>¥50,000.00</strong></td>
                        </tr>
                    </table>
                </div>
                <div class="comparison-item">
                    <h5>🟢 修改后 - 清爽设计</h5>
                    <table class="demo-table new-style">
                        <tr class="category-header">
                            <td colspan="2">项目费用明细 (白色背景)</td>
                        </tr>
                        <tr class="subcategory-header">
                            <td colspan="2">直接费 (白色背景)</td>
                        </tr>
                        <tr>
                            <td>实发工资</td>
                            <td>¥50,000.00</td>
                        </tr>
                        <tr class="total-row">
                            <td><strong>合计</strong></td>
                            <td><strong>¥50,000.00</strong></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <div class="demo-card">
            <h2>✨ 主要改进</h2>
            <div class="row">
                <div class="col-md-6">
                    <h5>🎨 视觉优化</h5>
                    <ul class="feature-list">
                        <li><i class="bx bx-check"></i>去掉深灰色分类标题背景</li>
                        <li><i class="bx bx-check"></i>去掉中灰色子分类背景</li>
                        <li><i class="bx bx-check"></i>去掉浅灰色总计行背景</li>
                        <li><i class="bx bx-check"></i>统一使用白色背景</li>
                        <li><i class="bx bx-check"></i>增加蓝色边框强调</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h5>🔧 设计细节</h5>
                    <ul class="feature-list">
                        <li><i class="bx bx-check"></i>分类标题：蓝色顶部边框</li>
                        <li><i class="bx bx-check"></i>子分类：蓝色左侧边框</li>
                        <li><i class="bx bx-check"></i>总计行：蓝色上下边框</li>
                        <li><i class="bx bx-check"></i>悬停效果：淡蓝色背景</li>
                        <li><i class="bx bx-check"></i>字体加粗：增强层次感</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="demo-card">
            <h2>🎯 设计理念</h2>
            <div class="highlight-box">
                <h6>简洁清爽的设计原则：</h6>
                <ul class="mb-0">
                    <li><strong>减少视觉噪音：</strong>去掉不必要的背景色，让内容更突出</li>
                    <li><strong>增强可读性：</strong>使用高对比度的文字和边框</li>
                    <li><strong>保持层次感：</strong>通过字体粗细和边框颜色区分层级</li>
                    <li><strong>统一风格：</strong>采用一致的白色背景和蓝色强调色</li>
                    <li><strong>现代化设计：</strong>符合当前扁平化设计趋势</li>
                </ul>
            </div>
        </div>

        <div class="demo-card">
            <h2>🎨 新的样式规范</h2>
            <div class="row">
                <div class="col-md-4">
                    <h6>分类标题</h6>
                    <div style="background: #ffffff; border-top: 2px solid #3182ce; border-left: 4px solid #3182ce; padding: 12px; border-radius: 4px; margin: 10px 0;">
                        <strong>项目费用明细</strong>
                    </div>
                    <small class="text-muted">白色背景 + 蓝色边框</small>
                </div>
                <div class="col-md-4">
                    <h6>子分类标题</h6>
                    <div style="background: #ffffff; border-left: 3px solid #60a5fa; padding: 12px; border-radius: 4px; margin: 10px 0;">
                        <strong>直接费</strong>
                    </div>
                    <small class="text-muted">白色背景 + 浅蓝边框</small>
                </div>
                <div class="col-md-4">
                    <h6>总计行</h6>
                    <div style="background: #ffffff; border-top: 2px solid #3182ce; border-bottom: 2px solid #3182ce; padding: 12px; border-radius: 4px; margin: 10px 0;">
                        <strong>合计：¥50,000.00</strong>
                    </div>
                    <small class="text-muted">白色背景 + 蓝色上下边框</small>
                </div>
            </div>
        </div>

        <div class="demo-card">
            <h2>🔗 查看优化效果</h2>
            <div class="text-center">
                <p class="mb-4">体验全新的清爽表格设计：</p>
                
                <?php if (count($projectList) > 0): ?>
                    <?php $firstProject = $projectList[0]; ?>
                    <a href="myxmfymx.php?projectid=<?php echo $firstProject['id']; ?>" 
                       class="demo-link" target="_blank">
                        <i class="bx bx-money me-2"></i>项目费用明细表（清爽版）
                    </a>
                    <a href="myxmcbmx.php?projectid=<?php echo $firstProject['id']; ?>" 
                       class="demo-link success" target="_blank">
                        <i class="bx bx-calculator me-2"></i>项目成本明细表（对比）
                    </a>
                <?php else: ?>
                    <div class="alert alert-info">
                        <i class="bx bx-info-circle me-2"></i>
                        暂无可用项目数据，请确保数据库中有项目记录
                    </div>
                <?php endif; ?>
                
                <div class="mt-4">
                    <a href="myxmfymx.php" class="demo-link" target="_blank">
                        <i class="bx bx-money me-2"></i>费用明细（无参数）
                    </a>
                </div>
            </div>
        </div>

        <div class="demo-card">
            <h2>📱 技术实现</h2>
            <div class="row">
                <div class="col-md-6">
                    <h6>CSS 修改</h6>
                    <pre style="background: #f7fafc; padding: 12px; border-radius: 6px; font-size: 12px;">
.category-header {
    background: #ffffff !important;
    color: #1a202c !important;
    border-top: 2px solid #3182ce !important;
}

.subcategory-header {
    background: #ffffff !important;
    color: #2d3748 !important;
}

.total-row {
    background: #ffffff !important;
    border-top: 2px solid #3182ce !important;
}
                    </pre>
                </div>
                <div class="col-md-6">
                    <h6>设计优势</h6>
                    <ul class="small">
                        <li>✅ 减少视觉干扰</li>
                        <li>✅ 提高内容可读性</li>
                        <li>✅ 现代化设计风格</li>
                        <li>✅ 更好的打印效果</li>
                        <li>✅ 符合无障碍设计</li>
                        <li>✅ 减少墨水消耗</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="demo-card">
            <h2>✅ 优化成果</h2>
            <div class="highlight-box">
                <h5 class="text-center mb-3">🎉 清爽表格设计完成！</h5>
                <div class="row text-center">
                    <div class="col-md-3">
                        <h6>视觉清爽</h6>
                        <p class="small text-muted">去掉深色背景</p>
                    </div>
                    <div class="col-md-3">
                        <h6>层次清晰</h6>
                        <p class="small text-muted">边框区分层级</p>
                    </div>
                    <div class="col-md-3">
                        <h6>现代设计</h6>
                        <p class="small text-muted">符合设计趋势</p>
                    </div>
                    <div class="col-md-3">
                        <h6>易于阅读</h6>
                        <p class="small text-muted">提升用户体验</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
