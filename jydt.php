<?php
include '../config.php';

$firstDayOfMonth = date('Y-m-01');
$lastDayOfMonth = date('Y-m-t');
$startDate = isset($_POST['start-date']) ? $_POST['start-date'] : $firstDayOfMonth;
$endDate = isset($_POST['end-date']) ? $_POST['end-date'] : $lastDayOfMonth;

// 验证日期格式
if (isset($_POST['start-date']) && $_POST['start-date']) {
    // 验证日期格式是否正确
    if (DateTime::createFromFormat('Y-m-d', $_POST['start-date']) !== false) {
        $startDate = $_POST['start-date'];
    }
}
if (isset($_POST['end-date']) && $_POST['end-date']) {
    // 验证日期格式是否正确
    if (DateTime::createFromFormat('Y-m-d', $_POST['end-date']) !== false) {
        $endDate = $_POST['end-date'];
    }
}

// 初始化日期变量
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // 验证日期
    if (strtotime($startDate) > strtotime($endDate)) {
        echo '<div class="result" style="background-color: #fde8e8;">错误：开始日期不能晚于结束日期</div>';
    } else {
        // 格式化日期用于显示
        $displayStart = date('Y年m月d日', strtotime($startDate));
        $displayEnd = date('Y年m月d日', strtotime($endDate));
        $daysDiff = (strtotime($endDate) - strtotime($startDate)) / (60 * 60 * 24) + 1;

        // 添加本月信息
        $currentMonth = date('Y年m月');
        $monthDays = date('t', strtotime($firstDayOfMonth));
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>经营动态 - 公司数据总览系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/boxicons@2.0.7/css/boxicons.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="styles/main.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        /* 页面特定样式 - 只保留必要的自定义样式 */
        .department-progress {
            background: rgba(248, 249, 250, 0.5);
            padding: 12px 15px;
            border-radius: 10px;
            border: 1px solid rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .department-progress:hover {
            background: rgba(248, 249, 250, 0.8);
            transform: translateX(5px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .department-label {
            color: #495057;
            font-size: 0.95rem;
        }

        .department-percentage {
            font-size: 1rem;
            min-width: 40px;
            text-align: right;
        }



        /* 合同表格特定样式 */
        .contract-table {
            table-layout: fixed;
        }

        .contract-table th:nth-child(1) { width: 15%; } /* 合同编号 */
        .contract-table th:nth-child(2) { width: 45%; } /* 客户名称 */
        .contract-table th:nth-child(3) { width: 25%; } /* 合同金额 */
        .contract-table th:nth-child(4) { width: 15%; } /* 签订日期 */

        .contract-amount {
            white-space: nowrap;
        }

        .contract-amount .amount-value {
            font-weight: 700;
            color: #28a745;
            font-size: 1.1rem;
            background: linear-gradient(135deg, rgba(40, 167, 69, 0.1), rgba(40, 167, 69, 0.05));
            padding: 6px 12px;
            border-radius: 8px;
            display: inline-block;
            border: 1px solid rgba(40, 167, 69, 0.2);
            white-space: nowrap;
        }

        .contract-date {
            white-space: nowrap;
        }

        .contract-date .date-info {
            white-space: nowrap;
        }

        .no-data {
            padding: 40px 20px;
        }

        .no-data-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #6c757d;
        }

        .no-data-content i {
            font-size: 3rem;
            opacity: 0.5;
            margin-bottom: 10px;
        }

        .no-data-content p {
            font-size: 1rem;
            font-weight: 500;
        }

        /* 图表容器样式 */
        .chart-container {
            position: relative;
            height: 300px;
            width: 100%;
        }

        .chart-small {
            height: 280px;
        }

        /* 部门图表特殊样式 */
        .department-chart-container {
            height: 300px;
            padding: 10px;
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            .date-range-container .form-group {
                flex-direction: column;
                align-items: stretch;
            }
            
            .date-range-container label {
                margin-bottom: 5px;
            }

            .department-progress {
                padding: 10px 12px;
            }

            .department-label {
                font-size: 0.9rem;
            }

            .department-percentage {
                font-size: 0.9rem;
            }

            .contract-header {
                padding: 10px 8px;
                font-size: 0.8rem;
            }

            .contract-row td {
                padding: 10px 8px;
            }

            .contract-name {
                max-width: 200px;
            }

            .contract-link {
                font-size: 0.9rem;
                padding: 4px 6px;
            }

            .contract-amount .amount-value {
                font-size: 1rem;
                padding: 4px 8px;
                white-space: nowrap;
            }

            .contract-date .date-info {
                font-size: 0.8rem;
                padding: 4px 8px;
                white-space: nowrap;
            }
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bx bx-bar-chart-alt-2 me-2"></i>
                经营动态
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text text-white">
                    <i class="bx bx-time me-1"></i>
                    最后更新: <span id="last-update-time"><?php echo date('Y-m-d H:i:s'); ?></span>
                </span>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- 日期区间选择器 -->
        <div class="date-range-container">
            <form method="post" action="">
                <div class="form-group">
                    <label for="start-date">开始日期:</label>
                    <input type="date" id="start-date" name="start-date" 
                           value="<?php echo htmlspecialchars($startDate); ?>">
                    <label for="end-date">结束日期:</label>
                    <input type="date" id="end-date" name="end-date" 
                           value="<?php echo htmlspecialchars($endDate); ?>">
                    <button type="submit" id="query-btn">
                        <i class="bx bx-search me-1"></i>查询
                    </button>
                </div>
            </form>
        </div>

        <?php
        // 检查是否有合同数据（用于图表显示）- 使用与表格相同的查询条件
        $checkSql = "SELECT COUNT(*) as total_contracts FROM `tuqoa_htgl` WHERE `qdsj`>='$startDate' and `qdsj`<='$endDate'";
        $checkResult = mysqli_query($link, $checkSql);
        $hasData = false;
        $totalContractsForChart = 0;

        if ($checkResult) {
            $checkRow = mysqli_fetch_assoc($checkResult);
            $totalContractsForChart = $checkRow['total_contracts'];
            $hasData = $totalContractsForChart > 0;
        }
        ?>

        <!-- 统计卡片区域 -->
        <div class="row mb-4">
            <?php
            // 查询合同统计数据
            $contractCount = 0;
            $totalAmount = 0;
            $avgAmount = 0;

            $sql = "SELECT COUNT(*) as count, SUM(fwf) as total, AVG(fwf) as avg FROM `tuqoa_htgl` WHERE `qdsj`>='$startDate' and `qdsj`<='$endDate'";
            $result = mysqli_query($link, $sql);
            if ($result) {
                $row = mysqli_fetch_assoc($result);
                $contractCount = $row["count"] ?? 0;
                $totalAmount = $row["total"] ?? 0;
                $avgAmount = $row["avg"] ?? 0;
            }

            // 查询员工统计数据
            $totalEmployees = 0;
            $newEmployees = 0;

            $sql = "SELECT COUNT(*) as total FROM `tuqoa_userinfo` WHERE state<>5";
            $result = mysqli_query($link, $sql);
            if ($result) {
                $row = mysqli_fetch_assoc($result);
                $totalEmployees = $row["total"] ?? 0;
            }

            $sql = "SELECT COUNT(*) as new_count FROM `tuqoa_userinfo` WHERE `workdate`>='$startDate' and `workdate`<='$endDate'";
            $result = mysqli_query($link, $sql);
            if ($result) {
                $row = mysqli_fetch_assoc($result);
                $newEmployees = $row["new_count"] ?? 0;
            }
            ?>
            <div class="col-md-3">
                <div class="card stat-card stat-card-primary">
                    <div class="card-body">
                        <i class="fas fa-file-contract stat-icon"></i>
                        <h5 class="card-title">合同数量</h5>
                        <h2 class="card-text"><?php echo $contractCount; ?></h2>
                        <p class="stat-info">期间签订合同</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card stat-card-success">
                    <div class="card-body">
                        <i class="fas fa-money-bill-wave stat-icon"></i>
                        <h5 class="card-title">合同总额（万元）</h5>
                        <h2 class="card-text">¥<?php echo round($totalAmount / 10000, 2); ?>万</h2>
                        <p class="stat-info">期间合同金额（万元）</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card stat-card-info">
                    <div class="card-body">
                        <i class="fas fa-users stat-icon"></i>
                        <h5 class="card-title">员工总数</h5>
                        <h2 class="card-text"><?php echo $totalEmployees; ?></h2>
                        <p class="stat-info">在职员工总数</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card stat-card-warning">
                    <div class="card-body">
                        <i class="fas fa-user-plus stat-icon"></i>
                        <h5 class="card-title">新增员工</h5>
                        <h2 class="card-text"><?php echo $newEmployees; ?></h2>
                        <p class="stat-info">期间新入职</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 图表和数据区域 -->
        <div class="row">
            <div class="col-md-8">
                <!-- 合同金额趋势图 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-chart-line me-2"></i>合同金额趋势（万元）
                            <?php if ($hasData): ?>
                                <span class="badge bg-success ms-2">真实数据</span>
                            <?php else: ?>
                                <span class="badge bg-warning ms-2">演示数据</span>
                            <?php endif; ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if ($hasData): ?>
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <small class="text-muted">数据源</small>
                                    <div class="fw-bold">合同管理系统</div>
                                </div>
                                <div class="col-md-4">
                                    <small class="text-muted">合同总数</small>
                                    <div class="fw-bold"><?php echo number_format($totalContractsForChart); ?> 个</div>
                                </div>
                                <div class="col-md-4">
                                    <small class="text-muted">统计周期</small>
                                    <div class="fw-bold"><?php echo $startDate; ?> 至 <?php echo $endDate; ?></div>
                                </div>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-info mb-3">
                                <i class="fas fa-info-circle me-2"></i>
                                当前时间范围内无合同数据，显示演示数据。请调整日期范围或添加合同数据。
                            </div>
                        <?php endif; ?>
                        <div class="chart-container">
                            <canvas id="contractTrendChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- 近期合同信息表格 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-file-contract me-2"></i>近期合同信息
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table contract-table">
                                <thead>
                                    <tr>
                                        <th class="contract-header">
                                            <i class="bx bx-hash me-1"></i>合同编号
                                        </th>
                                        <th class="contract-header">
                                            <i class="bx bx-building me-1"></i>客户名称
                                        </th>
                                        <th class="contract-header">
                                            <i class="bx bx-money me-1"></i>合同金额（万元）
                                        </th>
                                        <th class="contract-header">
                                            <i class="bx bx-calendar me-1"></i>签订日期
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                <?php
                                $sql="SELECT * FROM `tuqoa_htgl` WHERE `qdsj`>='$startDate' and `qdsj`<='$endDate' order by qdsj desc LIMIT 10";
                                $result = mysqli_query($link, $sql);
                                if ($result && mysqli_num_rows($result) > 0) {
                                    while ($row = mysqli_fetch_assoc($result)) {
                                ?>
                                    <tr class="contract-row">
                                        <td class="contract-id">
                                            <span class="badge bg-primary"><?php echo htmlspecialchars($row["htbh"]);?></span>
                                        </td>
                                        <td class="contract-name">
                                            <a href="/task.php?a=p&num=htgl&mid=<?php echo $row["id"]?>" target="_blank" class="contract-link">
                                                <i class="bx bx-link-external me-1"></i>
                                                <?php echo htmlspecialchars($row["htmc"]);?>
                                            </a>
                                        </td>
                                        <td class="contract-amount">
                                            <span class="amount-value">¥<?php echo $row["fwf"];?></span>
                                        </td>
                                        <td class="contract-date">
                                            <div class="date-info">
                                                <i class="bx bx-time me-1"></i>
                                                <?php echo date('m-d', strtotime($row["qdsj"]));?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php
                                    }
                                } else {
                                    echo '<tr><td colspan="4" class="text-center no-data">
                                            <div class="no-data-content">
                                                <i class="bx bx-file-blank mb-2"></i>
                                                <p class="mb-0">没有找到合同数据</p>
                                            </div>
                                          </td></tr>';
                                }
                                ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <!-- 部门分布饼图 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-chart-pie me-2"></i>部门人员分布
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container department-chart-container">
                            <canvas id="departmentChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- 快速统计 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-tachometer-alt me-2"></i>快速统计
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php
                        // 查询平均合同金额
                        $avgContractAmount = $contractCount > 0 ? $totalAmount / $contractCount : 0;

                        // 查询最大合同金额
                        $maxContractAmount = 0;
                        $sql = "SELECT MAX(fwf) as max_amount FROM `tuqoa_htgl` WHERE `qdsj`>='$startDate' and `qdsj`<='$endDate'";
                        $result = mysqli_query($link, $sql);
                        if ($result) {
                            $row = mysqli_fetch_assoc($result);
                            $maxContractAmount = $row["max_amount"] ?? 0;
                        }
                        ?>
                        <div class="row text-center">
                            <div class="col-12 mb-3">
                                <div class="border rounded p-3">
                                    <h6 class="text-muted mb-1">平均合同金额（万元）</h6>
                                    <h4 class="text-primary mb-0">¥<?php echo round($avgContractAmount, 2); ?></h4>
                                </div>
                            </div>
                            <div class="col-12 mb-3">
                                <div class="border rounded p-3">
                                    <h6 class="text-muted mb-1">最大合同金额（万元）</h6>
                                    <h4 class="text-success mb-0">¥<?php echo $maxContractAmount; ?></h4>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="border rounded p-3">
                                    <h6 class="text-muted mb-1">合同完成率</h6>
                                    <h4 class="text-info mb-0"><?php echo $contractCount > 0 ? '100%' : '0%'; ?></h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 更新最后更新时间
        function updateLastUpdateTime() {
            const now = new Date();
            const formattedDate = now.getFullYear() + '-' +
                                 String(now.getMonth() + 1).padStart(2, '0') + '-' +
                                 String(now.getDate()).padStart(2, '0') + ' ' +
                                 String(now.getHours()).padStart(2, '0') + ':' +
                                 String(now.getMinutes()).padStart(2, '0') + ':' +
                                 String(now.getSeconds()).padStart(2, '0');
            document.getElementById('last-update-time').textContent = formattedDate;
        }

        // 创建合同金额趋势图
        function createContractTrendChart() {
            const canvas = document.getElementById('contractTrendChart');
            if (!canvas) {
                console.error('找不到合同趋势图表画布元素');
                return;
            }
            const ctx = canvas.getContext('2d');

            <?php
            // 获取合同金额趋势数据 - 使用与表格相同的数据源
            $trendData = [];
            $trendLabels = [];

            if ($hasData) {
                // 使用真实数据 - 基于选定的日期范围，将时间段分为7个区间
                $startTimestamp = strtotime($startDate);
                $endTimestamp = strtotime($endDate);
                $totalDays = ($endTimestamp - $startTimestamp) / (24 * 60 * 60);
                $intervalDays = max(1, floor($totalDays / 7));

                for ($i = 0; $i < 7; $i++) {
                    $periodStart = date('Y-m-d', $startTimestamp + ($i * $intervalDays * 24 * 60 * 60));
                    $periodEnd = date('Y-m-d', $startTimestamp + (($i + 1) * $intervalDays * 24 * 60 * 60));

                    // 确保最后一个区间包含结束日期
                    if ($i == 6) {
                        $periodEnd = $endDate;
                    }

                    $trendLabels[] = date('m/d', strtotime($periodEnd));

                    // 使用与表格相同的查询逻辑
                    $sql = "SELECT COALESCE(SUM(fwf), 0) as period_amount FROM `tuqoa_htgl`
                            WHERE `qdsj` >= '$periodStart' AND `qdsj` <= '$periodEnd'";
                    $result = mysqli_query($link, $sql);
                    $periodAmount = 0;

                    if ($result) {
                        $row = mysqli_fetch_assoc($result);
                        $periodAmount = $row["period_amount"] ?? 0;
                    }

                    $trendData[] = (float)$periodAmount;
                }
            } else {
                // 如果没有真实数据，生成基于选定日期范围的演示数据
                $startTimestamp = strtotime($startDate);
                $endTimestamp = strtotime($endDate);
                $totalDays = ($endTimestamp - $startTimestamp) / (24 * 60 * 60);
                $intervalDays = max(1, floor($totalDays / 7));

                for ($i = 0; $i < 7; $i++) {
                    $periodEnd = date('Y-m-d', $startTimestamp + (($i + 1) * $intervalDays * 24 * 60 * 60));
                    if ($i == 6) {
                        $periodEnd = $endDate;
                    }
                    $trendLabels[] = date('m/d', strtotime($periodEnd));

                    // 生成演示数据
                    $baseAmount = 50000;
                    $variation = sin($i * 0.8) * 30000 + (rand(-10000, 20000));
                    $trendData[] = max(0, $baseAmount + $variation);
                }
            }

            // 确保数据格式正确
            $trendData = array_map('floatval', $trendData);
            ?>

            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: <?php echo json_encode($trendLabels); ?>,
                    datasets: [{
                        label: '合同金额（万元）',
                        data: <?php echo json_encode($trendData); ?>,
                        borderColor: '#007bff',
                        backgroundColor: 'rgba(0, 123, 255, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4,
                        pointBackgroundColor: '#007bff',
                        pointBorderColor: '#ffffff',
                        pointBorderWidth: 2,
                        pointRadius: 6
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    },
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleColor: '#ffffff',
                            bodyColor: '#ffffff',
                            borderColor: '#007bff',
                            borderWidth: 1,
                            cornerRadius: 6,
                            displayColors: false,
                            callbacks: {
                                title: function(context) {
                                    return '日期: ' + context[0].label;
                                },
                                label: function(context) {
                                    const value = context.parsed.y;
                                    return '合同金额: ¥' + value + '万';
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.1)'
                            },
                            ticks: {
                                font: {
                                    size: 11
                                }
                            }
                        },
                        y: {
                            beginAtZero: true,
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.1)'
                            },
                            ticks: {
                                callback: function(value) {
                                    if (value >= 10000) {
                                        return '¥' + (value / 10000).toFixed(1) + '万';
                                    }
                                    return '¥' + value;
                                },
                                font: {
                                    size: 11
                                }
                            }
                        }
                    },
                    elements: {
                        point: {
                            hoverRadius: 8,
                            hoverBorderWidth: 3
                        },
                        line: {
                            tension: 0.4
                        }
                    }
                }
            });
        }

        // 创建部门分布饼图
        function createDepartmentChart() {
            const ctx = document.getElementById('departmentChart').getContext('2d');

            <?php
            // 查询部门分布数据
            $departmentData = [];
            $departmentLabels = [];
            $departmentColors = ['#007bff', '#28a745', '#17a2b8', '#ffc107', '#dc3545', '#6f42c1', '#fd7e14', '#20c997'];

            // 查询部门分布数据
            $sql = "SELECT
                        CASE
                            WHEN deptname IS NULL OR deptname = '' THEN '未分配部门'
                            ELSE deptname
                        END as department_name,
                        COUNT(*) as count
                    FROM `tuqoa_userinfo`
                    WHERE state=1
                    GROUP BY department_name
                    ORDER BY count DESC";

            $result = mysqli_query($link, $sql);
            $totalEmployees = 0;
            $tempData = [];

            if ($result && mysqli_num_rows($result) > 0) {
                while ($row = mysqli_fetch_assoc($result)) {
                    $tempData[] = [
                        'name' => $row['department_name'],
                        'count' => $row['count']
                    ];
                    $totalEmployees += $row['count'];
                }
            }

            // 如果没有部门数据，使用默认数据
            if (empty($tempData)) {
                $tempData = [
                    ['name' => '技术部', 'count' => 15],
                    ['name' => '市场部', 'count' => 12],
                    ['name' => '运营部', 'count' => 10],
                    ['name' => '其他部门', 'count' => 8]
                ];
                $totalEmployees = 45;
            }

            // 处理数据，最多显示8个部门，其余合并为"其他"
            $maxDepartments = 7;
            $otherCount = 0;

            for ($i = 0; $i < count($tempData); $i++) {
                if ($i < $maxDepartments) {
                    $departmentLabels[] = $tempData[$i]['name'];
                    $departmentData[] = $tempData[$i]['count'];
                } else {
                    $otherCount += $tempData[$i]['count'];
                }
            }

            // 如果有其他部门，添加到数据中
            if ($otherCount > 0) {
                $departmentLabels[] = '其他部门';
                $departmentData[] = $otherCount;
            }
            ?>

            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: <?php echo json_encode($departmentLabels); ?>,
                    datasets: [{
                        data: <?php echo json_encode($departmentData); ?>,
                        backgroundColor: <?php echo json_encode(array_slice($departmentColors, 0, count($departmentData))); ?>,
                        borderWidth: 0,
                        hoverBorderWidth: 3,
                        hoverBorderColor: '#ffffff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    layout: {
                        padding: {
                            bottom: 30
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'right',
                            align: 'center',
                            labels: {
                                padding: 12,
                                usePointStyle: true,
                                pointStyle: 'circle',
                                font: {
                                    size: 10,
                                    family: "'Microsoft YaHei', Arial, sans-serif"
                                },
                                boxWidth: 10,
                                boxHeight: 10,
                                generateLabels: function(chart) {
                                    const data = chart.data;
                                    if (data.labels.length && data.datasets.length) {
                                        return data.labels.map((label, i) => {
                                            const count = data.datasets[0].data[i];
                                            const total = data.datasets[0].data.reduce((a, b) => a + b, 0);
                                            const percentage = ((count / total) * 100).toFixed(1);

                                            // 优化标签显示：如果部门名称过长，进行截断
                                            let displayLabel = label;
                                            if (label.length > 6) {
                                                displayLabel = label.substring(0, 5) + '..';
                                            }

                                            return {
                                                text: `${displayLabel} ${count}人 ${percentage}%`,
                                                fillStyle: data.datasets[0].backgroundColor[i],
                                                strokeStyle: data.datasets[0].backgroundColor[i],
                                                lineWidth: 0,
                                                pointStyle: 'circle'
                                            };
                                        });
                                    }
                                    return [];
                                }
                            }
                        },
                        tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleColor: '#ffffff',
                            bodyColor: '#ffffff',
                            borderColor: '#ffffff',
                            borderWidth: 1,
                            cornerRadius: 6,
                            displayColors: true,
                            callbacks: {
                                title: function(context) {
                                    return '部门统计';
                                },
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.parsed;
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = ((value / total) * 100).toFixed(1);
                                    return `${label}: ${value}人 (${percentage}%)`;
                                }
                            }
                        }
                    },
                    cutout: '55%',
                    elements: {
                        arc: {
                            borderWidth: 2,
                            borderColor: '#ffffff'
                        }
                    }
                }
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 更新最后更新时间
            updateLastUpdateTime();

            // 创建图表
            createContractTrendChart();
            createDepartmentChart();

            // 每30秒更新一次时间
            setInterval(updateLastUpdateTime, 30000);
        });
    </script>
<?php
mysqli_close($link);
?>
</body>
</html>