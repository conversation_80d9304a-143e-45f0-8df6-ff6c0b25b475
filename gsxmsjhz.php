<?php
include '../config.php';
$firstDayOfMonth = date('Y-m-01');
$lastDayOfMonth = date('Y-m-t');
$startDate = isset($_POST['start-date']) ? $_POST['start-date'] : $firstDayOfMonth;
$endDate = isset($_POST['end-date']) ? $_POST['end-date'] : $lastDayOfMonth;

// 验证日期格式
if (isset($_POST['start-date']) && $_POST['start-date']) {
    if (DateTime::createFromFormat('Y-m-d', $_POST['start-date']) !== false) {
        $startDate = $_POST['start-date'];
    }
}
if (isset($_POST['end-date']) && $_POST['end-date']) {
    if (DateTime::createFromFormat('Y-m-d', $_POST['end-date']) !== false) {
        $endDate = $_POST['end-date'];
    }
}

// 初始化日期变量
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // 验证日期
    if (strtotime($startDate) > strtotime($endDate)) {
        echo '<div class="result" style="background-color: #fde8e8;">错误：开始日期不能晚于结束日期</div>';
    } else {
        // 格式化日期用于显示
        $displayStart = date('Y年m月d日', strtotime($startDate));
        $displayEnd = date('Y年m月d日', strtotime($endDate));
        $daysDiff = (strtotime($endDate) - strtotime($startDate)) / (60 * 60 * 24) + 1;

        // 添加本月信息
        $currentMonth = date('Y年m月');
        $monthDays = date('t', strtotime($firstDayOfMonth));
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>公司项目数据汇总 - 公司数据总览系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/boxicons@2.0.7/css/boxicons.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles/main.css">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bx bx-bar-chart-alt-2 me-2"></i>
                公司项目数据汇总
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text text-white">
                    <i class="bx bx-time me-1"></i>
                    查询时间: <span id="query-time"><?php echo date('Y-m-d H:i:s'); ?></span>
                </span>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
            
            <!-- 日期选择器 -->
            <div class="date-range-container">
                <form method="post" action="">
                    <div class="form-group">
                        <label for="start-date">开始日期:</label>
                        <input type="date" id="start-date" name="start-date"
                               value="<?php echo htmlspecialchars($startDate); ?>">
                        <label for="end-date">结束日期:</label>
                        <input type="date" id="end-date" name="end-date"
                               value="<?php echo htmlspecialchars($endDate); ?>">
                        <button type="submit" class="btn btn-primary" id="query-btn">查询</button>
                    </div>
                </form>
            </div>
            
            <div class="row">
                <?php
                //计算服务费合计
                $fwfhj = 0;
                $sql="SELECT sum(fwf) fwfhj FROM `tuqoa_htgl` WHERE `qdsj`>='$startDate' and `qdsj`<='$endDate'";
                $result = mysqli_query($link, $sql);
                if ($result) {
                    while ($row = mysqli_fetch_assoc($result)) {
                        $fwfhj=$row["fwfhj"];
                    }
                }
                ?>
                <div class="col-md-3">
                    <div class="card stat-card stat-card-primary">
                        <div class="card-body">
                            <i class="fas fa-file-contract stat-icon"></i>
                            <h5 class="card-title">合同总额</h5>
                            <h2 class="card-text">¥<?php echo number_format($fwfhj, 1); ?>万</h2>
                            <p class="stat-info">期间签约总额</p>
                        </div>
                    </div>
                </div>
                <?php
                $cz = 0;
                $sql="SELECT IFNULL(SUM(wccz), 0) hj FROM `tuqoa_xmcztjb`  WHERE `sbrq`>='$startDate' and `sbrq`<='$endDate'";
                $result = mysqli_query($link, $sql);
                if ($result) {
                    while ($row = mysqli_fetch_assoc($result)) {
                        $cz=$row["hj"];
                    }
                }

                // 查询实际到账总额
                $dzje = 0;
                $sql="SELECT IFNULL(SUM(ysje), 0) as dzje FROM `tuqoa_htsf` WHERE `sksj`>='$startDate' and `sksj`<='$endDate'";
                $result = mysqli_query($link, $sql);
                if ($result) {
                    while ($row = mysqli_fetch_assoc($result)) {
                        $dzje=$row["dzje"];
                    }
                }

                // 计算差额
                $chae = $fwfhj - $dzje;

                // 查询图表数据
                $chart_data = [];

                // 1. 合同额与到账额趋势数据（按月统计）
                $trend_months = [];
                $trend_contracts = [];
                $trend_payments = [];

                for ($i = 1; $i <= 12; $i++) {
                    $month = sprintf('%02d', $i);
                    $year = date('Y', strtotime($startDate));
                    $trend_months[] = $month . '月';

                    // 查询当月合同额
                    $sql = "SELECT IFNULL(SUM(fwf), 0) as amount FROM `tuqoa_htgl`
                           WHERE DATE_FORMAT(qdsj, '%Y-%m') = '$year-$month'";
                    $result = mysqli_query($link, $sql);
                    $contract_amount = $result ? mysqli_fetch_assoc($result)['amount'] : 0;
                    $trend_contracts[] = (float)$contract_amount;

                    // 查询当月到账额
                    $sql = "SELECT IFNULL(SUM(ysje), 0) as amount FROM `tuqoa_htsf`
                           WHERE DATE_FORMAT(sksj, '%Y-%m') = '$year-$month'";
                    $result = mysqli_query($link, $sql);
                    $payment_amount = $result ? mysqli_fetch_assoc($result)['amount'] : 0;
                    $trend_payments[] = (float)$payment_amount;
                }

                // 2. 项目状态分布数据
                $status_labels = [];
                $status_data = [];
                $sql = "SELECT
                    CASE
                        WHEN xmzt LIKE '%完工%' OR xmzt LIKE '%结算%' THEN '已完成'
                        WHEN xmzt LIKE '%在建%' OR xmzt LIKE '%执行%' THEN '执行中'
                        WHEN xmzt LIKE '%新开%' OR xmzt LIKE '%开工%' THEN '新开工'
                        WHEN xmzt LIKE '%暂停%' OR xmzt LIKE '%终止%' THEN '暂停/终止'
                        ELSE '其他'
                    END as status_type,
                    COUNT(*) as count
                FROM `tuqoa_gcproject`
                WHERE qdsj >= '$startDate' AND qdsj <= '$endDate'
                GROUP BY status_type";

                $result = mysqli_query($link, $sql);
                if ($result) {
                    while ($row = mysqli_fetch_assoc($result)) {
                        $status_labels[] = $row['status_type'];
                        $status_data[] = (int)$row['count'];
                    }
                }

                if (empty($status_labels)) {
                    $status_labels = ['暂无数据'];
                    $status_data = [1];
                }

                // 3. 项目类型分布数据
                $type_labels = [];
                $type_data = [];
                $sql = "SELECT
                    CASE
                        WHEN xmxz LIKE '%市政%' OR xmxz LIKE '%道路%' THEN '市政工程'
                        WHEN xmxz LIKE '%建筑%' OR xmxz LIKE '%房建%' THEN '建筑工程'
                        WHEN xmxz LIKE '%装修%' OR xmxz LIKE '%装饰%' THEN '装修工程'
                        WHEN xmxz LIKE '%水利%' THEN '水利工程'
                        ELSE '其他工程'
                    END as project_type,
                    COUNT(*) as count
                FROM `tuqoa_gcproject`
                WHERE qdsj >= '$startDate' AND qdsj <= '$endDate'
                GROUP BY project_type";

                $result = mysqli_query($link, $sql);
                if ($result) {
                    while ($row = mysqli_fetch_assoc($result)) {
                        $type_labels[] = $row['project_type'];
                        $type_data[] = (int)$row['count'];
                    }
                }

                if (empty($type_labels)) {
                    $type_labels = ['暂无数据'];
                    $type_data = [1];
                }

                // 4. 到账率分布数据
                $rate_labels = [];
                $rate_data = [];
                $rate_ranges = [
                    ['0-25%', 0, 25],
                    ['26-50%', 26, 50],
                    ['51-75%', 51, 75],
                    ['76-100%', 76, 100]
                ];

                // 初始化计数器
                foreach ($rate_ranges as $range) {
                    $rate_labels[] = $range[0];
                    $rate_data[] = 0;
                }

                $sql = "SELECT
                    h.fwf as contract_amount,
                    IFNULL(SUM(s.ysje), 0) as received_amount
                FROM `tuqoa_htgl` h
                LEFT JOIN `tuqoa_htsf` s ON h.projectid = s.projectid
                WHERE h.qdsj >= '$startDate' AND h.qdsj <= '$endDate'
                GROUP BY h.id, h.fwf
                HAVING contract_amount > 0";

                $result = mysqli_query($link, $sql);
                if ($result) {
                    while ($row = mysqli_fetch_assoc($result)) {
                        $rate = ($row['received_amount'] / $row['contract_amount']) * 100;

                        // 动态分配到对应的区间
                        for ($i = 0; $i < count($rate_ranges); $i++) {
                            $min = $rate_ranges[$i][1];
                            $max = $rate_ranges[$i][2];
                            if ($rate >= $min && $rate <= $max) {
                                $rate_data[$i]++;
                                break;
                            }
                        }
                    }
                }

                // 如果没有数据，提供默认分布
                if (array_sum($rate_data) == 0) {
                    $rate_labels = ['暂无数据'];
                    $rate_data = [1];
                }

                // 5. 月度产值趋势数据
                $value_months = [];
                $value_data = [];

                for ($i = 1; $i <= 12; $i++) {
                    $month = sprintf('%02d', $i);
                    $year = date('Y', strtotime($startDate));
                    $value_months[] = $month . '月';

                    $sql = "SELECT IFNULL(SUM(wccz), 0) as amount FROM `tuqoa_xmcztjb`
                           WHERE DATE_FORMAT(sbrq, '%Y-%m') = '$year-$month'";
                    $result = mysqli_query($link, $sql);
                    $amount = $result ? mysqli_fetch_assoc($result)['amount'] : 0;
                    $value_data[] = (float)$amount;
                }

                // 6. 项目规模分布数据
                $scale_labels = [];
                $scale_data = [];
                $sql = "SELECT
                    CASE
                        WHEN zaojia >= 1000 THEN '大型项目(≥1000万)'
                        WHEN zaojia >= 500 THEN '中型项目(500-1000万)'
                        WHEN zaojia >= 100 THEN '小型项目(100-500万)'
                        WHEN zaojia > 0 THEN '微型项目(<100万)'
                        ELSE '未定义'
                    END as scale_type,
                    COUNT(*) as count,
                    IFNULL(SUM(zaojia), 0) as total_amount
                FROM `tuqoa_gcproject`
                WHERE qdsj >= '$startDate' AND qdsj <= '$endDate'
                GROUP BY scale_type
                ORDER BY
                    CASE
                        WHEN zaojia >= 1000 THEN 1
                        WHEN zaojia >= 500 THEN 2
                        WHEN zaojia >= 100 THEN 3
                        WHEN zaojia > 0 THEN 4
                        ELSE 5
                    END";

                $result = mysqli_query($link, $sql);
                if ($result) {
                    while ($row = mysqli_fetch_assoc($result)) {
                        $scale_labels[] = $row['scale_type'];
                        $scale_data[] = (float)$row['total_amount'];
                    }
                }

                if (empty($scale_labels)) {
                    $scale_labels = ['暂无数据'];
                    $scale_data = [1];
                }

                // 7. 项目负责人分布数据
                $manager_labels = [];
                $manager_data = [];
                $sql = "SELECT
                    CASE
                        WHEN fzr IS NULL OR fzr = '' THEN '未分配'
                        ELSE fzr
                    END as manager_name,
                    COUNT(*) as project_count,
                    IFNULL(SUM(zaojia), 0) as total_amount
                FROM `tuqoa_gcproject`
                WHERE qdsj >= '$startDate' AND qdsj <= '$endDate'
                GROUP BY manager_name
                ORDER BY project_count DESC
                LIMIT 10";

                $result = mysqli_query($link, $sql);
                if ($result) {
                    while ($row = mysqli_fetch_assoc($result)) {
                        $manager_labels[] = $row['manager_name'];
                        $manager_data[] = (int)$row['project_count'];
                    }
                }

                if (empty($manager_labels)) {
                    $manager_labels = ['暂无数据'];
                    $manager_data = [1];
                }

                // 8. 项目投资额分布数据
                $investment_labels = [];
                $investment_data = [];
                $sql = "SELECT
                    CASE
                        WHEN zaojia = 0 THEN '未定义投资额'
                        WHEN zaojia <= 500 THEN '500万以下'
                        WHEN zaojia <= 1000 THEN '500-1000万'
                        WHEN zaojia <= 2000 THEN '1000-2000万'
                        WHEN zaojia <= 5000 THEN '2000-5000万'
                        ELSE '5000万以上'
                    END as investment_range,
                    COUNT(*) as project_count
                FROM `tuqoa_gcproject`
                WHERE qdsj >= '$startDate' AND qdsj <= '$endDate'
                GROUP BY investment_range
                ORDER BY
                    CASE
                        WHEN zaojia = 0 THEN 6
                        WHEN zaojia <= 500 THEN 1
                        WHEN zaojia <= 1000 THEN 2
                        WHEN zaojia <= 2000 THEN 3
                        WHEN zaojia <= 5000 THEN 4
                        ELSE 5
                    END";

                $result = mysqli_query($link, $sql);
                if ($result) {
                    while ($row = mysqli_fetch_assoc($result)) {
                        $investment_labels[] = $row['investment_range'];
                        $investment_data[] = (int)$row['project_count'];
                    }
                }

                if (empty($investment_labels)) {
                    $investment_labels = ['暂无数据'];
                    $investment_data = [1];
                }

                // 9. 年度项目签约趋势数据
                $yearly_labels = [];
                $yearly_data = [];
                $current_year = date('Y');

                for ($year = $current_year - 4; $year <= $current_year; $year++) {
                    $yearly_labels[] = $year . '年';

                    $sql = "SELECT COUNT(*) as count FROM `tuqoa_gcproject`
                           WHERE YEAR(qdsj) = $year";
                    $result = mysqli_query($link, $sql);
                    $count = $result ? mysqli_fetch_assoc($result)['count'] : 0;
                    $yearly_data[] = (int)$count;
                }

                // 10. 项目完成率分析数据
                $completion_rate_labels = [];
                $completion_rate_data = [];
                $sql = "SELECT
                    CASE
                        WHEN c.wcl IS NULL OR c.wcl = 0 THEN '未开始(0%)'
                        WHEN c.wcl <= 25 THEN '初期(1-25%)'
                        WHEN c.wcl <= 50 THEN '进行中(26-50%)'
                        WHEN c.wcl <= 75 THEN '后期(51-75%)'
                        WHEN c.wcl <= 99 THEN '接近完成(76-99%)'
                        ELSE '已完成(100%)'
                    END as completion_range,
                    COUNT(DISTINCT p.id) as project_count
                FROM `tuqoa_gcproject` p
                LEFT JOIN `tuqoa_xmcztjb` c ON p.id = c.projectid
                WHERE p.qdsj >= '$startDate' AND p.qdsj <= '$endDate'
                GROUP BY completion_range
                ORDER BY
                    CASE
                        WHEN c.wcl IS NULL OR c.wcl = 0 THEN 1
                        WHEN c.wcl <= 25 THEN 2
                        WHEN c.wcl <= 50 THEN 3
                        WHEN c.wcl <= 75 THEN 4
                        WHEN c.wcl <= 99 THEN 5
                        ELSE 6
                    END";

                $result = mysqli_query($link, $sql);
                if ($result) {
                    while ($row = mysqli_fetch_assoc($result)) {
                        $completion_rate_labels[] = $row['completion_range'];
                        $completion_rate_data[] = (int)$row['project_count'];
                    }
                }

                if (empty($completion_rate_labels)) {
                    $completion_rate_labels = ['暂无数据'];
                    $completion_rate_data = [1];
                }

                // 11. 收款周期分析数据
                $cycle_labels = [];
                $cycle_data = [];
                $sql = "SELECT
                    CASE
                        WHEN s.sksj IS NULL OR s.sksj = '0000-00-00' THEN '未收款'
                        WHEN DATEDIFF(s.sksj, h.qdsj) <= 30 THEN '30天内'
                        WHEN DATEDIFF(s.sksj, h.qdsj) <= 90 THEN '31-90天'
                        WHEN DATEDIFF(s.sksj, h.qdsj) <= 180 THEN '91-180天'
                        WHEN DATEDIFF(s.sksj, h.qdsj) <= 365 THEN '181-365天'
                        ELSE '超过1年'
                    END as cycle_range,
                    COUNT(*) as payment_count
                FROM `tuqoa_htgl` h
                LEFT JOIN `tuqoa_htsf` s ON h.projectid = s.projectid
                WHERE h.qdsj >= '$startDate' AND h.qdsj <= '$endDate'
                GROUP BY cycle_range
                ORDER BY
                    CASE
                        WHEN s.sksj IS NULL OR s.sksj = '0000-00-00' THEN 6
                        WHEN DATEDIFF(s.sksj, h.qdsj) <= 30 THEN 1
                        WHEN DATEDIFF(s.sksj, h.qdsj) <= 90 THEN 2
                        WHEN DATEDIFF(s.sksj, h.qdsj) <= 180 THEN 3
                        WHEN DATEDIFF(s.sksj, h.qdsj) <= 365 THEN 4
                        ELSE 5
                    END";

                $result = mysqli_query($link, $sql);
                if ($result) {
                    while ($row = mysqli_fetch_assoc($result)) {
                        $cycle_labels[] = $row['cycle_range'];
                        $cycle_data[] = (int)$row['payment_count'];
                    }
                }

                if (empty($cycle_labels)) {
                    $cycle_labels = ['暂无数据'];
                    $cycle_data = [1];
                }

                $chart_data = [
                    'trend' => [
                        'months' => $trend_months,
                        'contracts' => $trend_contracts,
                        'payments' => $trend_payments
                    ],
                    'status' => [
                        'labels' => $status_labels,
                        'data' => $status_data
                    ],
                    'type' => [
                        'labels' => $type_labels,
                        'data' => $type_data
                    ],
                    'rate' => [
                        'labels' => $rate_labels,
                        'data' => $rate_data
                    ],
                    'value' => [
                        'months' => $value_months,
                        'data' => $value_data
                    ],
                    'scale' => [
                        'labels' => $scale_labels,
                        'data' => $scale_data
                    ],
                    'manager' => [
                        'labels' => $manager_labels,
                        'data' => $manager_data
                    ],
                    'investment' => [
                        'labels' => $investment_labels,
                        'data' => $investment_data
                    ],
                    'yearly' => [
                        'labels' => $yearly_labels,
                        'data' => $yearly_data
                    ],
                    'completion_rate' => [
                        'labels' => $completion_rate_labels,
                        'data' => $completion_rate_data
                    ],
                    'cycle' => [
                        'labels' => $cycle_labels,
                        'data' => $cycle_data
                    ]
                ];
                ?>
                <div class="col-md-3">
                    <div class="card stat-card stat-card-info">
                        <div class="card-body">
                            <i class="fas fa-chart-line stat-icon"></i>
                            <h5 class="card-title">完成产值总额</h5>
                            <h2 class="card-text">¥<?php echo number_format($cz, 1); ?>万</h2>
                            <p class="stat-info">期间完成产值</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card stat-card stat-card-success">
                        <div class="card-body">
                            <i class="fas fa-money-bill-wave stat-icon"></i>
                            <h5 class="card-title">实际到账总额</h5>
                            <h2 class="card-text">¥<?php echo number_format($dzje, 1); ?>万</h2>
                            <p class="stat-info">期间到账金额</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card stat-card <?php echo $chae >= 0 ? 'stat-card-success' : 'stat-card-danger'; ?>">
                        <div class="card-body">
                            <i class="fas <?php echo $chae >= 0 ? 'fa-check-circle' : 'fa-exclamation-triangle'; ?> stat-icon"></i>
                            <h5 class="card-title">差额</h5>
                            <h2 class="card-text">¥<?php echo number_format($chae, 1); ?>万</h2>
                            <p class="stat-info">
                                <?php echo $chae >= 0 ? '合同额充足' : '收款不足'; ?>
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">合同额与到账额趋势对比</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="contractPaymentTrendChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">项目状态分布</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="projectStatusChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">项目类型分布</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="projectTypeChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">项目到账率分布</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="paymentRateChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">月度产值趋势</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="monthlyValueChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">项目规模分布</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="projectScaleChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">项目负责人分布</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="projectManagerChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 新增图表行 -->
            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">项目投资额分布</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="investmentDistributionChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">年度项目签约趋势</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="yearlyContractTrendChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">项目完成率分析</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="projectCompletionChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">收款周期分析</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="paymentCycleChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 项目汇总表格 -->
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">项目汇总统计</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>项目名称</th>
                                            <th>项目类型</th>
                                            <th>合同总额(万)</th>
                                            <th>已收款(万)</th>
                                            <th>完成产值(万)</th>
                                            <th>到账率</th>
                                            <th>项目状态</th>
                                            <th>负责人</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        // 查询项目汇总数据
                                        $sql = "SELECT
                                            p.gcname as project_name,
                                            CASE
                                                WHEN p.xmxz LIKE '%市政%' OR p.xmxz LIKE '%道路%' THEN '市政工程'
                                                WHEN p.xmxz LIKE '%建筑%' OR p.xmxz LIKE '%房建%' THEN '建筑工程'
                                                WHEN p.xmxz LIKE '%装修%' OR p.xmxz LIKE '%装饰%' THEN '装修工程'
                                                WHEN p.xmxz LIKE '%水利%' THEN '水利工程'
                                                ELSE '其他工程'
                                            END as project_type,
                                            IFNULL(SUM(h.fwf), 0) as contract_total,
                                            IFNULL(SUM(s.ysje), 0) as received_total,
                                            IFNULL(SUM(c.wccz), 0) as completed_value,
                                            CASE
                                                WHEN SUM(h.fwf) > 0 THEN ROUND((SUM(s.ysje) / SUM(h.fwf)) * 100, 1)
                                                ELSE 0
                                            END as payment_rate,
                                            CASE
                                                WHEN p.xmzt LIKE '%完工%' OR p.xmzt LIKE '%结算%' THEN '已完成'
                                                WHEN p.xmzt LIKE '%在建%' OR p.xmzt LIKE '%执行%' THEN '执行中'
                                                WHEN p.xmzt LIKE '%新开%' OR p.xmzt LIKE '%开工%' THEN '新开工'
                                                WHEN p.xmzt LIKE '%暂停%' OR p.xmzt LIKE '%终止%' THEN '暂停/终止'
                                                ELSE '其他'
                                            END as project_status,
                                            IFNULL(p.fzr, '未分配') as manager_name
                                        FROM `tuqoa_gcproject` p
                                        LEFT JOIN `tuqoa_htgl` h ON p.id = h.projectid
                                        LEFT JOIN `tuqoa_htsf` s ON p.id = s.projectid
                                        LEFT JOIN `tuqoa_xmcztjb` c ON p.id = c.projectid
                                        WHERE p.qdsj >= '$startDate' AND p.qdsj <= '$endDate'
                                        GROUP BY p.id, p.gcname, p.xmxz, p.xmzt, p.fzr
                                        ORDER BY contract_total DESC
                                        LIMIT 20";

                                        $result = mysqli_query($link, $sql);
                                        $total_projects = 0;
                                        $total_contracts = 0;
                                        $total_received = 0;
                                        $total_value = 0;

                                        if ($result && mysqli_num_rows($result) > 0) {
                                            while ($row = mysqli_fetch_assoc($result)) {
                                                $total_projects++;
                                                $total_contracts += $row['contract_total'];
                                                $total_received += $row['received_total'];
                                                $total_value += $row['completed_value'];

                                                // 到账率颜色
                                                $rate_class = 'text-danger';
                                                if ($row['payment_rate'] >= 80) $rate_class = 'text-success';
                                                elseif ($row['payment_rate'] >= 60) $rate_class = 'text-warning';

                                                // 项目状态颜色
                                                $status_class = 'bg-secondary';
                                                switch ($row['project_status']) {
                                                    case '已完成': $status_class = 'bg-success'; break;
                                                    case '执行中': $status_class = 'bg-primary'; break;
                                                    case '新开工': $status_class = 'bg-info'; break;
                                                    case '暂停/终止': $status_class = 'bg-danger'; break;
                                                }
                                        ?>
                                        <tr>
                                            <td><strong><?php echo htmlspecialchars($row['project_name']); ?></strong></td>
                                            <td><?php echo $row['project_type']; ?></td>
                                            <td>¥<?php echo number_format($row['contract_total'], 1); ?></td>
                                            <td>¥<?php echo number_format($row['received_total'], 1); ?></td>
                                            <td>¥<?php echo number_format($row['completed_value'], 1); ?></td>
                                            <td><span class="<?php echo $rate_class; ?>"><?php echo $row['payment_rate']; ?>%</span></td>
                                            <td><span class="badge <?php echo $status_class; ?>"><?php echo $row['project_status']; ?></span></td>
                                            <td><?php echo htmlspecialchars($row['manager_name']); ?></td>
                                        </tr>
                                        <?php
                                            }
                                        } else {
                                            echo "<tr><td colspan='8' class='text-center text-muted'>暂无数据</td></tr>";
                                        }

                                        // 计算总体到账率
                                        $overall_rate = $total_contracts > 0 ? round(($total_received / $total_contracts) * 100, 1) : 0;
                                        $overall_class = 'text-danger';
                                        if ($overall_rate >= 80) $overall_class = 'text-success';
                                        elseif ($overall_rate >= 60) $overall_class = 'text-warning';
                                        ?>
                                        <tr class="table-info">
                                            <td><strong>合计 (<?php echo $total_projects; ?>个项目)</strong></td>
                                            <td><strong>-</strong></td>
                                            <td><strong>¥<?php echo number_format($total_contracts, 1); ?></strong></td>
                                            <td><strong>¥<?php echo number_format($total_received, 1); ?></strong></td>
                                            <td><strong>¥<?php echo number_format($total_value, 1); ?></strong></td>
                                            <td><strong><span class="<?php echo $overall_class; ?>"><?php echo $overall_rate; ?>%</span></strong></td>
                                            <td>
                                                <?php if ($overall_rate >= 90): ?>
                                                    <span class="badge bg-success">整体优秀</span>
                                                <?php elseif ($overall_rate >= 70): ?>
                                                    <span class="badge bg-warning">整体良好</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">需要改进</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><strong>-</strong></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // 设置默认日期范围为本月
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date();
            const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
            const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);

            //document.getElementById('start-date').value = formatDate(firstDay);
            //document.getElementById('end-date').value = formatDate(lastDay);

            // 查询按钮点击事件（如果存在）
            const queryBtn = document.getElementById('query-btn');
            if (queryBtn) {
                queryBtn.addEventListener('click', function() {
                const startDate = document.getElementById('start-date').value;
                const endDate = document.getElementById('end-date').value;
                
                if (!startDate || !endDate) {
                    alert('请选择开始日期和结束日期');
                    return;
                }
                
                if (new Date(startDate) > new Date(endDate)) {
                    alert('开始日期不能晚于结束日期');
                    return;
                }
                
                    // 这里可以添加查询逻辑，例如AJAX请求获取数据
                    console.log('查询日期范围:', startDate, '至', endDate);
                    // 模拟数据刷新
                    //alert('已更新数据，日期范围: ' + startDate + ' 至 ' + endDate);
                });
            }

            // 等待页面完全加载后再初始化图表
            setTimeout(function() {
                initCharts();
            }, 100);
        });
        
        // 格式化日期为YYYY-MM-DD
        function formatDate(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        }
        
        // 初始化图表
        function initCharts() {
            const chartData = <?php echo json_encode($chart_data); ?>;
            console.log('图表数据:', chartData);
            console.log('开始初始化图表...');

            // 合同额与到账额趋势图表
            const contractPaymentTrendCtx = document.getElementById('contractPaymentTrendChart');
            if (contractPaymentTrendCtx) {
                try {
                    new Chart(contractPaymentTrendCtx.getContext('2d'), {
                    type: 'line',
                    data: {
                        labels: chartData.trend.months,
                        datasets: [
                            {
                                label: '合同额',
                                data: chartData.trend.contracts,
                                borderColor: '#1e88e5',
                                backgroundColor: 'rgba(30, 136, 229, 0.1)',
                                tension: 0.3,
                                fill: false,
                                pointBackgroundColor: '#1e88e5',
                                pointBorderColor: '#ffffff',
                                pointBorderWidth: 2,
                                pointRadius: 4
                            },
                            {
                                label: '到账额',
                                data: chartData.trend.payments,
                                borderColor: '#43a047',
                                backgroundColor: 'rgba(67, 160, 71, 0.1)',
                                tension: 0.3,
                                fill: false,
                                pointBackgroundColor: '#43a047',
                                pointBorderColor: '#ffffff',
                                pointBorderWidth: 2,
                                pointRadius: 4
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: '合同额与到账额趋势对比',
                                font: {
                                    size: 16,
                                    weight: 'bold'
                                }
                            },
                            legend: {
                                position: 'top'
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        return context.dataset.label + ': ¥' + context.parsed.y + '万';
                                    }
                                }
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: '金额（万元）'
                                }
                            },
                            x: {
                                title: {
                                    display: true,
                                    text: '月份'
                                }
                            }
                        }
                    }
                });
                console.log('合同额与到账额趋势图表初始化完成');
                } catch (error) {
                    console.error('合同额与到账额趋势图表初始化失败:', error);
                }
            }

            // 项目状态分布图表
            const projectStatusCtx = document.getElementById('projectStatusChart');
            if (projectStatusCtx) {
                try {
                const statusColors = {
                    '执行中': '#1e88e5',
                    '已完成': '#4caf50',
                    '新开工': '#ff9800',
                    '暂停/终止': '#f44336',
                    '其他': '#9e9e9e'
                };
                const colors = chartData.status.labels.map(label => statusColors[label] || '#9e9e9e');

                new Chart(projectStatusCtx.getContext('2d'), {
                    type: 'pie',
                    data: {
                        labels: chartData.status.labels,
                        datasets: [{
                            data: chartData.status.data,
                            backgroundColor: colors,
                            borderColor: '#ffffff',
                            borderWidth: 2
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: '项目状态分布',
                                font: {
                                    size: 16,
                                    weight: 'bold'
                                }
                            },
                            legend: {
                                position: 'bottom'
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                        const percentage = ((context.parsed / total) * 100).toFixed(1);
                                        return context.label + ': ' + context.parsed + '个 (' + percentage + '%)';
                                    }
                                }
                            }
                        }
                    }
                });
                console.log('项目状态分布图表初始化完成');
                } catch (error) {
                    console.error('项目状态分布图表初始化失败:', error);
                }
            }

            // 项目类型分布图表
            const projectTypeCtx = document.getElementById('projectTypeChart');
            if (projectTypeCtx) {
                try {
                const typeColors = {
                    '市政工程': '#1e88e5',
                    '建筑工程': '#e53935',
                    '装修工程': '#43a047',
                    '水利工程': '#2196f3',
                    '其他工程': '#9e9e9e'
                };
                const colors = chartData.type.labels.map(label => typeColors[label] || '#9e9e9e');

                new Chart(projectTypeCtx.getContext('2d'), {
                    type: 'doughnut',
                    data: {
                        labels: chartData.type.labels,
                        datasets: [{
                            data: chartData.type.data,
                            backgroundColor: colors,
                            borderColor: '#ffffff',
                            borderWidth: 2
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        cutout: '50%',
                        plugins: {
                            title: {
                                display: true,
                                text: '项目类型分布',
                                font: {
                                    size: 16,
                                    weight: 'bold'
                                }
                            },
                            legend: {
                                position: 'bottom'
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                        const percentage = ((context.parsed / total) * 100).toFixed(1);
                                        return context.label + ': ' + context.parsed + '个 (' + percentage + '%)';
                                    }
                                }
                            }
                        }
                    }
                });
                console.log('项目类型分布图表初始化完成');
                } catch (error) {
                    console.error('项目类型分布图表初始化失败:', error);
                }
            }

            // 项目到账率分布图表
            const paymentRateCtx = document.getElementById('paymentRateChart');
            if (paymentRateCtx) {
                try {
                const rateColors = ['#f44336', '#ff9800', '#ffeb3b', '#4caf50'];
                new Chart(paymentRateCtx.getContext('2d'), {
                    type: 'bar',
                    data: {
                        labels: chartData.rate.labels,
                        datasets: [{
                            label: '项目数量',
                            data: chartData.rate.data,
                            backgroundColor: rateColors,
                            borderColor: rateColors.map(color => color.replace('f4', 'c6')),
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: '项目到账率分布',
                                font: {
                                    size: 16,
                                    weight: 'bold'
                                }
                            },
                            legend: {
                                display: false
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        return '项目数量: ' + context.parsed.y + '个';
                                    }
                                }
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: '项目数量'
                                }
                            },
                            x: {
                                title: {
                                    display: true,
                                    text: '到账率区间'
                                }
                            }
                        }
                    }
                });
                console.log('项目到账率分布图表初始化完成');
                } catch (error) {
                    console.error('项目到账率分布图表初始化失败:', error);
                }
            }

            // 月度产值趋势图表
            const monthlyValueCtx = document.getElementById('monthlyValueChart');
            if (monthlyValueCtx) {
                try {
                new Chart(monthlyValueCtx.getContext('2d'), {
                    type: 'bar',
                    data: {
                        labels: chartData.value.months,
                        datasets: [{
                            label: '完成产值',
                            data: chartData.value.data,
                            backgroundColor: '#ff9800',
                            borderColor: '#f57c00',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: '月度产值趋势',
                                font: {
                                    size: 16,
                                    weight: 'bold'
                                }
                            },
                            legend: {
                                display: false
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        return '完成产值: ¥' + context.parsed.y + '万';
                                    }
                                }
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: '产值（万元）'
                                }
                            },
                            x: {
                                title: {
                                    display: true,
                                    text: '月份'
                                }
                            }
                        }
                    }
                });
                console.log('月度产值趋势图表初始化完成');
                } catch (error) {
                    console.error('月度产值趋势图表初始化失败:', error);
                }
            }

            // 项目规模分布图表
            const projectScaleCtx = document.getElementById('projectScaleChart');
            if (projectScaleCtx) {
                try {
                const scaleColors = ['#1e88e5', '#43a047', '#ff9800', '#f44336'];
                new Chart(projectScaleCtx.getContext('2d'), {
                    type: 'bar',
                    data: {
                        labels: chartData.scale.labels,
                        datasets: [{
                            label: '投资总额',
                            data: chartData.scale.data,
                            backgroundColor: scaleColors.slice(0, chartData.scale.labels.length),
                            borderColor: scaleColors.slice(0, chartData.scale.labels.length).map(color => color.replace('e5', 'c0')),
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: '项目规模分布',
                                font: {
                                    size: 16,
                                    weight: 'bold'
                                }
                            },
                            legend: {
                                display: false
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        return '投资总额: ¥' + context.parsed.y + '万';
                                    }
                                }
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: '投资总额（万元）'
                                }
                            },
                            x: {
                                title: {
                                    display: true,
                                    text: '项目规模'
                                }
                            }
                        }
                    }
                });
                console.log('项目规模分布图表初始化完成');
                } catch (error) {
                    console.error('项目规模分布图表初始化失败:', error);
                }
            }

            // 项目负责人分布图表
            const projectManagerCtx = document.getElementById('projectManagerChart');
            if (projectManagerCtx) {
                try {
                    const managerColors = generateColors(chartData.manager.labels.length);
                    new Chart(projectManagerCtx.getContext('2d'), {
                    type: 'doughnut',
                    data: {
                        labels: chartData.manager.labels,
                        datasets: [{
                            data: chartData.manager.data,
                            backgroundColor: managerColors,
                            borderColor: '#ffffff',
                            borderWidth: 2
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        cutout: '60%',
                        plugins: {
                            title: {
                                display: true,
                                text: '项目负责人分布',
                                font: {
                                    size: 16,
                                    weight: 'bold'
                                }
                            },
                            legend: {
                                position: 'bottom',
                                labels: {
                                    maxWidth: 100,
                                    usePointStyle: true
                                }
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                        const percentage = ((context.parsed / total) * 100).toFixed(1);
                                        return context.label + ': ' + context.parsed + '个项目 (' + percentage + '%)';
                                    }
                                }
                            }
                        }
                    }
                });
                console.log('项目负责人分布图表初始化完成');
                } catch (error) {
                    console.error('项目负责人分布图表初始化失败:', error);
                }
            }

            // 项目投资额分布图表
            const investmentDistributionCtx = document.getElementById('investmentDistributionChart');
            if (investmentDistributionCtx) {
                try {
                    const investmentColors = ['#e3f2fd', '#bbdefb', '#90caf9', '#64b5f6', '#42a5f5', '#2196f3'];
                    new Chart(investmentDistributionCtx.getContext('2d'), {
                    type: 'pie',
                    data: {
                        labels: chartData.investment.labels,
                        datasets: [{
                            data: chartData.investment.data,
                            backgroundColor: investmentColors.slice(0, chartData.investment.labels.length),
                            borderColor: '#ffffff',
                            borderWidth: 2
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: '项目投资额分布',
                                font: {
                                    size: 16,
                                    weight: 'bold'
                                }
                            },
                            legend: {
                                position: 'bottom'
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                        const percentage = ((context.parsed / total) * 100).toFixed(1);
                                        return context.label + ': ' + context.parsed + '个项目 (' + percentage + '%)';
                                    }
                                }
                            }
                        }
                    }
                });
                console.log('项目投资额分布图表初始化完成');
                } catch (error) {
                    console.error('项目投资额分布图表初始化失败:', error);
                }
            }

            // 年度项目签约趋势图表
            const yearlyContractTrendCtx = document.getElementById('yearlyContractTrendChart');
            if (yearlyContractTrendCtx) {
                try {
                    new Chart(yearlyContractTrendCtx.getContext('2d'), {
                    type: 'line',
                    data: {
                        labels: chartData.yearly.labels,
                        datasets: [{
                            label: '签约项目数',
                            data: chartData.yearly.data,
                            borderColor: '#4caf50',
                            backgroundColor: 'rgba(76, 175, 80, 0.1)',
                            tension: 0.3,
                            fill: true,
                            pointBackgroundColor: '#4caf50',
                            pointBorderColor: '#ffffff',
                            pointBorderWidth: 2,
                            pointRadius: 5
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: '年度项目签约趋势',
                                font: {
                                    size: 16,
                                    weight: 'bold'
                                }
                            },
                            legend: {
                                display: false
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        return '签约项目: ' + context.parsed.y + '个';
                                    }
                                }
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: '项目数量'
                                }
                            },
                            x: {
                                title: {
                                    display: true,
                                    text: '年份'
                                }
                            }
                        }
                    }
                });
                console.log('年度项目签约趋势图表初始化完成');
                } catch (error) {
                    console.error('年度项目签约趋势图表初始化失败:', error);
                }
            }

            // 项目完成率分析图表
            const projectCompletionCtx = document.getElementById('projectCompletionChart');
            if (projectCompletionCtx) {
                try {
                    const completionColors = ['#f44336', '#ff9800', '#ffeb3b', '#8bc34a', '#4caf50', '#2196f3'];
                    new Chart(projectCompletionCtx.getContext('2d'), {
                    type: 'doughnut',
                    data: {
                        labels: chartData.completion_rate.labels,
                        datasets: [{
                            data: chartData.completion_rate.data,
                            backgroundColor: completionColors.slice(0, chartData.completion_rate.labels.length),
                            borderColor: '#ffffff',
                            borderWidth: 2
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        cutout: '50%',
                        plugins: {
                            title: {
                                display: true,
                                text: '项目完成率分析',
                                font: {
                                    size: 16,
                                    weight: 'bold'
                                }
                            },
                            legend: {
                                position: 'bottom'
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                        const percentage = ((context.parsed / total) * 100).toFixed(1);
                                        return context.label + ': ' + context.parsed + '个项目 (' + percentage + '%)';
                                    }
                                }
                            }
                        }
                    }
                });
                console.log('项目完成率分析图表初始化完成');
                } catch (error) {
                    console.error('项目完成率分析图表初始化失败:', error);
                }
            }

            // 收款周期分析图表
            const paymentCycleCtx = document.getElementById('paymentCycleChart');
            if (paymentCycleCtx) {
                try {
                    const cycleColors = ['#4caf50', '#8bc34a', '#ffeb3b', '#ff9800', '#f44336', '#9e9e9e'];
                    new Chart(paymentCycleCtx.getContext('2d'), {
                    type: 'bar',
                    data: {
                        labels: chartData.cycle.labels,
                        datasets: [{
                            label: '收款笔数',
                            data: chartData.cycle.data,
                            backgroundColor: cycleColors.slice(0, chartData.cycle.labels.length),
                            borderColor: cycleColors.slice(0, chartData.cycle.labels.length).map(color => color.replace('f4', 'c6')),
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: '收款周期分析',
                                font: {
                                    size: 16,
                                    weight: 'bold'
                                }
                            },
                            legend: {
                                display: false
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        return '收款笔数: ' + context.parsed.y + '笔';
                                    }
                                }
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: '收款笔数'
                                }
                            },
                            x: {
                                title: {
                                    display: true,
                                    text: '收款周期'
                                }
                            }
                        }
                    }
                });
                console.log('收款周期分析图表初始化完成');
                } catch (error) {
                    console.error('收款周期分析图表初始化失败:', error);
                }
            }

            console.log('所有图表初始化完成');
        }

        // 生成颜色数组的辅助函数
        function generateColors(count) {
            const baseColors = [
                '#1e88e5', '#e53935', '#43a047', '#ffb300',
                '#8e24aa', '#00acc1', '#6d4c41', '#757575',
                '#f44336', '#9c27b0', '#3f51b5', '#2196f3',
                '#009688', '#4caf50', '#8bc34a', '#cddc39',
                '#ffeb3b', '#ffc107', '#ff9800', '#ff5722'
            ];

            const colors = [];
            for (let i = 0; i < count; i++) {
                colors.push(baseColors[i % baseColors.length]);
            }
            return colors;
        }
    </script>
</body>
</html> 