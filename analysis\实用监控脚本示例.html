<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实用监控脚本示例</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 10px;
            font-size: 2.5rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .subtitle {
            text-align: center;
            color: #7f8c8d;
            margin-bottom: 30px;
            font-size: 1.1rem;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }
        h3 {
            color: #2980b9;
            margin-top: 20px;
        }
        .script-card {
            background: #f8f9fa;
            padding: 20px;
            margin: 15px 0;
            border-radius: 10px;
            border-left: 4px solid #3498db;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .php-script { border-left-color: #6f42c1; }
        .js-script { border-left-color: #ffc107; }
        .bash-script { border-left-color: #28a745; }
        .sql-script { border-left-color: #dc3545; }
        .code-snippet {
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 10px 0;
            position: relative;
        }
        .code-header {
            background: #34495e;
            color: white;
            padding: 8px 15px;
            border-radius: 5px 5px 0 0;
            font-size: 12px;
            font-weight: bold;
            margin: 10px 0 0 0;
        }
        .code-snippet.with-header {
            margin-top: 0;
            border-radius: 0 0 5px 5px;
        }
        .copy-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #3498db;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 12px;
            cursor: pointer;
        }
        .copy-btn:hover {
            background: #2980b9;
        }
        .usage-note {
            background: #e7f3ff;
            border: 1px solid #b8daff;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .warning-note {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .file-structure {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 实用监控脚本示例</h1>
        <p class="subtitle">基于diagnose.php的系统监控脚本集合</p>
        
        <h2>🔧 增强版诊断脚本</h2>
        
        <div class="script-card php-script">
            <h3>enhanced_diagnose.php - 增强版系统诊断</h3>
            
            <div class="code-header">enhanced_diagnose.php</div>
            <div class="code-snippet with-header">
&lt;?php
/**
 * 增强版系统诊断脚本
 * 基于原有diagnose.php扩展功能
 */

require_once 'config.php'; // 数据库配置

class SystemDiagnostic {
    private $db;
    private $diagnostics = [];
    
    public function __construct($database_connection) {
        $this->db = $database_connection;
    }
    
    /**
     * 执行完整的系统诊断
     */
    public function runFullDiagnostic() {
        $this->checkDatabaseHealth();
        $this->checkSystemResources();
        $this->checkApplicationHealth();
        $this->checkPerformanceMetrics();
        
        return $this->generateReport();
    }
    
    /**
     * 数据库健康检查
     */
    private function checkDatabaseHealth() {
        try {
            // 基础连接检查
            if (mysqli_ping($this->db)) {
                $this->diagnostics['db_connection'] = [
                    'status' => 'success',
                    'message' => '数据库连接正常'
                ];
                
                // 连接数检查
                $result = mysqli_query($this->db, "SHOW STATUS LIKE 'Threads_connected'");
                $connected = mysqli_fetch_assoc($result)['Value'];
                
                $result = mysqli_query($this->db, "SHOW VARIABLES LIKE 'max_connections'");
                $max_connections = mysqli_fetch_assoc($result)['Value'];
                
                $usage_percent = round(($connected / $max_connections) * 100, 2);
                
                $this->diagnostics['db_connections'] = [
                    'status' => $usage_percent > 80 ? 'warning' : 'success',
                    'message' => "连接使用率: {$usage_percent}%",
                    'detail' => "当前连接: {$connected}/{$max_connections}",
                    'value' => $usage_percent
                ];
                
                // 慢查询检查
                $result = mysqli_query($this->db, "SHOW STATUS LIKE 'Slow_queries'");
                $slow_queries = mysqli_fetch_assoc($result)['Value'];
                
                $this->diagnostics['slow_queries'] = [
                    'status' => $slow_queries > 10 ? 'warning' : 'success',
                    'message' => "慢查询数量: {$slow_queries}",
                    'value' => $slow_queries
                ];
                
                // 查询缓存命中率
                $this->checkQueryCacheHitRate();
                
            } else {
                $this->diagnostics['db_connection'] = [
                    'status' => 'error',
                    'message' => '数据库连接失败'
                ];
            }
        } catch (Exception $e) {
            $this->diagnostics['db_connection'] = [
                'status' => 'error',
                'message' => '数据库检查异常: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * 查询缓存命中率检查
     */
    private function checkQueryCacheHitRate() {
        try {
            $result = mysqli_query($this->db, "SHOW STATUS LIKE 'Qcache_hits'");
            $hits = $result ? mysqli_fetch_assoc($result)['Value'] : 0;
            
            $result = mysqli_query($this->db, "SHOW STATUS LIKE 'Qcache_inserts'");
            $inserts = $result ? mysqli_fetch_assoc($result)['Value'] : 0;
            
            if (($hits + $inserts) > 0) {
                $hit_rate = round(($hits / ($hits + $inserts)) * 100, 2);
                
                $this->diagnostics['query_cache'] = [
                    'status' => $hit_rate < 50 ? 'warning' : 'success',
                    'message' => "查询缓存命中率: {$hit_rate}%",
                    'detail' => "命中: {$hits}, 插入: {$inserts}",
                    'value' => $hit_rate
                ];
            } else {
                $this->diagnostics['query_cache'] = [
                    'status' => 'info',
                    'message' => '查询缓存未启用或无数据'
                ];
            }
        } catch (Exception $e) {
            $this->diagnostics['query_cache'] = [
                'status' => 'error',
                'message' => '查询缓存检查失败: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * 系统资源检查
     */
    private function checkSystemResources() {
        // CPU负载检查
        if (function_exists('sys_getloadavg')) {
            $load = sys_getloadavg();
            $load_1min = $load[0];
            
            $this->diagnostics['cpu_load'] = [
                'status' => $load_1min > 2.0 ? 'warning' : 'success',
                'message' => "CPU负载: {$load_1min}",
                'detail' => "1分钟: {$load[0]}, 5分钟: {$load[1]}, 15分钟: {$load[2]}",
                'value' => $load_1min
            ];
        }
        
        // 内存使用检查
        $memory_usage = memory_get_usage(true);
        $memory_peak = memory_get_peak_usage(true);
        $memory_limit = $this->parseMemoryLimit(ini_get('memory_limit'));
        
        $memory_percent = round(($memory_usage / $memory_limit) * 100, 2);
        
        $this->diagnostics['memory_usage'] = [
            'status' => $memory_percent > 85 ? 'warning' : 'success',
            'message' => "内存使用率: {$memory_percent}%",
            'detail' => "当前: " . $this->formatBytes($memory_usage) . 
                       ", 峰值: " . $this->formatBytes($memory_peak) . 
                       ", 限制: " . $this->formatBytes($memory_limit),
            'value' => $memory_percent
        ];
        
        // 磁盘空间检查
        $disk_free = disk_free_space('/');
        $disk_total = disk_total_space('/');
        $disk_usage_percent = round((1 - ($disk_free / $disk_total)) * 100, 2);
        
        $this->diagnostics['disk_usage'] = [
            'status' => $disk_usage_percent > 85 ? 'warning' : 'success',
            'message' => "磁盘使用率: {$disk_usage_percent}%",
            'detail' => "可用: " . $this->formatBytes($disk_free) . 
                       ", 总计: " . $this->formatBytes($disk_total),
            'value' => $disk_usage_percent
        ];
    }
    
    /**
     * 应用程序健康检查
     */
    private function checkApplicationHealth() {
        // 核心表检查
        $core_tables = [
            'tuqoa_gcproject' => '工程项目表',
            'tuqoa_htgl' => '合同管理表',
            'tuqoa_xmcztjb' => '项目成本表',
            'tuqoa_htsf' => '合同收费表',
            'tuqoa_userinfo' => '用户信息表'
        ];
        
        foreach ($core_tables as $table => $description) {
            try {
                $result = mysqli_query($this->db, "SELECT COUNT(*) as count FROM `{$table}`");
                if ($result) {
                    $row = mysqli_fetch_assoc($result);
                    $count = $row['count'];
                    
                    $this->diagnostics["table_{$table}"] = [
                        'status' => $count > 0 ? 'success' : 'warning',
                        'message' => "{$description}: {$count} 条记录",
                        'value' => $count
                    ];
                } else {
                    $this->diagnostics["table_{$table}"] = [
                        'status' => 'error',
                        'message' => "{$description}: 查询失败"
                    ];
                }
            } catch (Exception $e) {
                $this->diagnostics["table_{$table}"] = [
                    'status' => 'error',
                    'message' => "{$description}: " . $e->getMessage()
                ];
            }
        }
        
        // 关键目录权限检查
        $directories = [
            '/var/www/tuqoa/storage' => '存储目录',
            '/var/www/tuqoa/logs' => '日志目录',
            '/var/www/tuqoa/uploads' => '上传目录'
        ];
        
        foreach ($directories as $dir => $description) {
            if (is_dir($dir)) {
                $writable = is_writable($dir);
                $readable = is_readable($dir);
                
                $this->diagnostics["dir_" . basename($dir)] = [
                    'status' => ($writable && $readable) ? 'success' : 'warning',
                    'message' => "{$description}: " . 
                               ($writable ? '可写' : '不可写') . ', ' . 
                               ($readable ? '可读' : '不可读')
                ];
            } else {
                $this->diagnostics["dir_" . basename($dir)] = [
                    'status' => 'error',
                    'message' => "{$description}: 目录不存在"
                ];
            }
        }
    }
    
    /**
     * 性能指标检查
     */
    private function checkPerformanceMetrics() {
        // 数据库查询性能测试
        $start_time = microtime(true);
        
        $test_queries = [
            'simple_count' => "SELECT COUNT(*) FROM tuqoa_gcproject",
            'complex_join' => "SELECT COUNT(*) FROM tuqoa_gcproject p 
                              LEFT JOIN tuqoa_htgl h ON p.id = h.projectid",
            'date_range' => "SELECT COUNT(*) FROM tuqoa_xmcztjb 
                            WHERE sbrq >= DATE_SUB(NOW(), INTERVAL 30 DAY)"
        ];
        
        foreach ($test_queries as $test_name => $query) {
            $query_start = microtime(true);
            $result = mysqli_query($this->db, $query);
            $query_time = (microtime(true) - $query_start) * 1000; // 转换为毫秒
            
            $this->diagnostics["perf_{$test_name}"] = [
                'status' => $query_time > 1000 ? 'warning' : 'success',
                'message' => "查询耗时: " . round($query_time, 2) . "ms",
                'value' => $query_time
            ];
        }
        
        $total_time = (microtime(true) - $start_time) * 1000;
        
        $this->diagnostics['perf_total'] = [
            'status' => $total_time > 3000 ? 'warning' : 'success',
            'message' => "总测试耗时: " . round($total_time, 2) . "ms",
            'value' => $total_time
        ];
    }
    
    /**
     * 生成诊断报告
     */
    private function generateReport() {
        $total = count($this->diagnostics);
        $passed = 0;
        $warnings = 0;
        $errors = 0;
        
        foreach ($this->diagnostics as $diagnostic) {
            switch ($diagnostic['status']) {
                case 'success':
                    $passed++;
                    break;
                case 'warning':
                    $warnings++;
                    break;
                case 'error':
                    $errors++;
                    break;
            }
        }
        
        return [
            'summary' => [
                'total' => $total,
                'passed' => $passed,
                'warnings' => $warnings,
                'errors' => $errors,
                'success_rate' => round(($passed / $total) * 100, 2)
            ],
            'diagnostics' => $this->diagnostics,
            'timestamp' => date('Y-m-d H:i:s')
        ];
    }
    
    /**
     * 辅助方法：解析内存限制
     */
    private function parseMemoryLimit($limit) {
        $unit = strtolower(substr($limit, -1));
        $value = (int)$limit;
        
        switch ($unit) {
            case 'g': return $value * 1024 * 1024 * 1024;
            case 'm': return $value * 1024 * 1024;
            case 'k': return $value * 1024;
            default: return $value;
        }
    }
    
    /**
     * 辅助方法：格式化字节数
     */
    private function formatBytes($bytes, $precision = 2) {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
}

// 使用示例
if (isset($_GET['api']) && $_GET['api'] === 'json') {
    // API模式：返回JSON数据
    header('Content-Type: application/json');
    
    $diagnostic = new SystemDiagnostic($link);
    $report = $diagnostic->runFullDiagnostic();
    
    echo json_encode($report, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    exit;
}

// 正常页面模式
$diagnostic = new SystemDiagnostic($link);
$report = $diagnostic->runFullDiagnostic();
?&gt;
            </div>
            
            <div class="usage-note">
                <strong>💡 使用说明：</strong>
                <ul>
                    <li>将此脚本保存为 <code>enhanced_diagnose.php</code></li>
                    <li>访问 <code>enhanced_diagnose.php</code> 查看完整诊断页面</li>
                    <li>访问 <code>enhanced_diagnose.php?api=json</code> 获取JSON格式数据</li>
                    <li>可以集成到现有的diagnose.php中</li>
                </ul>
            </div>
        </div>

        <h2>📊 实时监控API</h2>
        
        <div class="script-card php-script">
            <h3>monitor_api.php - 实时监控API接口</h3>
            
            <div class="code-header">monitor_api.php</div>
            <div class="code-snippet with-header">
&lt;?php
/**
 * 实时监控API接口
 * 为前端提供实时监控数据
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

require_once 'config.php';

class MonitorAPI {
    private $db;
    
    public function __construct($database_connection) {
        $this->db = $database_connection;
    }
    
    /**
     * 获取实时监控数据
     */
    public function getRealtimeMetrics() {
        return [
            'database' => $this->getDatabaseMetrics(),
            'system' => $this->getSystemMetrics(),
            'application' => $this->getApplicationMetrics(),
            'timestamp' => time()
        ];
    }
    
    /**
     * 获取数据库指标
     */
    private function getDatabaseMetrics() {
        $metrics = [];
        
        try {
            // 连接数
            $result = mysqli_query($this->db, "SHOW STATUS LIKE 'Threads_connected'");
            $metrics['connected'] = $result ? mysqli_fetch_assoc($result)['Value'] : 0;
            
            $result = mysqli_query($this->db, "SHOW VARIABLES LIKE 'max_connections'");
            $metrics['max_connections'] = $result ? mysqli_fetch_assoc($result)['Value'] : 0;
            
            // 慢查询
            $result = mysqli_query($this->db, "SHOW STATUS LIKE 'Slow_queries'");
            $metrics['slow_queries'] = $result ? mysqli_fetch_assoc($result)['Value'] : 0;
            
            // 查询数
            $result = mysqli_query($this->db, "SHOW STATUS LIKE 'Questions'");
            $metrics['total_queries'] = $result ? mysqli_fetch_assoc($result)['Value'] : 0;
            
            // 运行时间
            $result = mysqli_query($this->db, "SHOW STATUS LIKE 'Uptime'");
            $metrics['uptime'] = $result ? mysqli_fetch_assoc($result)['Value'] : 0;
            
        } catch (Exception $e) {
            $metrics['error'] = $e->getMessage();
        }
        
        return $metrics;
    }
    
    /**
     * 获取系统指标
     */
    private function getSystemMetrics() {
        $metrics = [];
        
        // CPU负载
        if (function_exists('sys_getloadavg')) {
            $load = sys_getloadavg();
            $metrics['cpu_load'] = [
                '1min' => $load[0],
                '5min' => $load[1],
                '15min' => $load[2]
            ];
        }
        
        // 内存使用
        $metrics['memory'] = [
            'current_usage' => memory_get_usage(true),
            'peak_usage' => memory_get_peak_usage(true),
            'limit' => $this->parseMemoryLimit(ini_get('memory_limit'))
        ];
        
        // 磁盘空间
        $metrics['disk'] = [
            'free_space' => disk_free_space('/'),
            'total_space' => disk_total_space('/'),
            'usage_percent' => round((1 - (disk_free_space('/') / disk_total_space('/'))) * 100, 2)
        ];
        
        return $metrics;
    }
    
    /**
     * 获取应用指标
     */
    private function getApplicationMetrics() {
        $metrics = [];
        
        try {
            // 活跃项目数
            $result = mysqli_query($this->db, 
                "SELECT COUNT(*) as count FROM tuqoa_gcproject 
                 WHERE xmzt IN ('新开工项目','在建项目','完工未结算')");
            $metrics['active_projects'] = $result ? mysqli_fetch_assoc($result)['count'] : 0;
            
            // 在职员工数
            $result = mysqli_query($this->db, 
                "SELECT COUNT(*) as count FROM tuqoa_userinfo WHERE state != 5");
            $metrics['active_employees'] = $result ? mysqli_fetch_assoc($result)['count'] : 0;
            
            // 今日新增记录
            $today = date('Y-m-d');
            $result = mysqli_query($this->db, 
                "SELECT COUNT(*) as count FROM tuqoa_xmcztjb WHERE DATE(sbrq) = '$today'");
            $metrics['today_records'] = $result ? mysqli_fetch_assoc($result)['count'] : 0;
            
        } catch (Exception $e) {
            $metrics['error'] = $e->getMessage();
        }
        
        return $metrics;
    }
    
    /**
     * 记录监控数据到历史表
     */
    public function recordMetrics($metrics) {
        try {
            $sql = "INSERT INTO diagnostic_history (metric_type, metric_value, additional_data, status) 
                    VALUES (?, ?, ?, ?)";
            
            $stmt = mysqli_prepare($this->db, $sql);
            
            // 记录数据库连接使用率
            if (isset($metrics['database']['connected']) && isset($metrics['database']['max_connections'])) {
                $usage = ($metrics['database']['connected'] / $metrics['database']['max_connections']) * 100;
                $status = $usage > 80 ? 'warning' : 'normal';
                
                mysqli_stmt_bind_param($stmt, 'sdss', 
                    $metric_type = 'db_connection_usage',
                    $usage,
                    $additional_data = json_encode($metrics['database']),
                    $status
                );
                mysqli_stmt_execute($stmt);
            }
            
            // 记录系统负载
            if (isset($metrics['system']['cpu_load']['1min'])) {
                $load = $metrics['system']['cpu_load']['1min'];
                $status = $load > 2.0 ? 'warning' : 'normal';
                
                mysqli_stmt_bind_param($stmt, 'sdss',
                    $metric_type = 'cpu_load_1min',
                    $load,
                    $additional_data = json_encode($metrics['system']['cpu_load']),
                    $status
                );
                mysqli_stmt_execute($stmt);
            }
            
            // 记录内存使用率
            if (isset($metrics['system']['memory'])) {
                $memory = $metrics['system']['memory'];
                $usage_percent = ($memory['current_usage'] / $memory['limit']) * 100;
                $status = $usage_percent > 85 ? 'warning' : 'normal';
                
                mysqli_stmt_bind_param($stmt, 'sdss',
                    $metric_type = 'memory_usage_percent',
                    $usage_percent,
                    $additional_data = json_encode($memory),
                    $status
                );
                mysqli_stmt_execute($stmt);
            }
            
        } catch (Exception $e) {
            error_log("Failed to record metrics: " . $e->getMessage());
        }
    }
    
    private function parseMemoryLimit($limit) {
        $unit = strtolower(substr($limit, -1));
        $value = (int)$limit;
        
        switch ($unit) {
            case 'g': return $value * 1024 * 1024 * 1024;
            case 'm': return $value * 1024 * 1024;
            case 'k': return $value * 1024;
            default: return $value;
        }
    }
}

// API路由处理
$action = $_GET['action'] ?? 'metrics';

$api = new MonitorAPI($link);

switch ($action) {
    case 'metrics':
        $metrics = $api->getRealtimeMetrics();
        
        // 记录到历史表（可选）
        if (isset($_GET['record']) && $_GET['record'] === 'true') {
            $api->recordMetrics($metrics);
        }
        
        echo json_encode($metrics, JSON_UNESCAPED_UNICODE);
        break;
        
    case 'health':
        // 简单的健康检查
        echo json_encode([
            'status' => 'ok',
            'timestamp' => time(),
            'version' => '1.0.0'
        ]);
        break;
        
    default:
        http_response_code(404);
        echo json_encode(['error' => 'Action not found']);
        break;
}
?&gt;
            </div>
            
            <div class="usage-note">
                <strong>💡 API使用示例：</strong>
                <ul>
                    <li><code>GET /monitor_api.php?action=metrics</code> - 获取实时监控数据</li>
                    <li><code>GET /monitor_api.php?action=metrics&record=true</code> - 获取数据并记录历史</li>
                    <li><code>GET /monitor_api.php?action=health</code> - 健康检查</li>
                </ul>
            </div>
        </div>

        <h2>🖥️ 前端监控界面</h2>
        
        <div class="script-card js-script">
            <h3>monitor_dashboard.js - 监控仪表板</h3>
            
            <div class="code-header">monitor_dashboard.js</div>
            <div class="code-snippet with-header">
/**
 * 实时监控仪表板
 * 配合monitor_api.php使用
 */

class MonitorDashboard {
    constructor(options = {}) {
        this.apiUrl = options.apiUrl || '/monitor_api.php';
        this.updateInterval = options.updateInterval || 30000; // 30秒
        this.maxDataPoints = options.maxDataPoints || 20;
        
        this.isRunning = false;
        this.intervalId = null;
        this.charts = {};
        this.historicalData = {
            cpu_load: [],
            memory_usage: [],
            db_connections: [],
            timestamps: []
        };
        
        this.init();
    }
    
    init() {
        this.createDashboardHTML();
        this.initializeCharts();
        this.bindEvents();
        this.start();
    }
    
    createDashboardHTML() {
        const dashboardHTML = `
            <div id="monitor-dashboard" class="monitor-dashboard">
                <div class="dashboard-header">
                    <h3>系统实时监控</h3>
                    <div class="dashboard-controls">
                        <span id="monitor-status" class="status-indicator">监控中...</span>
                        <button id="toggle-monitor" class="btn btn-sm btn-primary">停止监控</button>
                        <span id="last-update" class="last-update">--</span>
                    </div>
                </div>
                
                <div class="metrics-grid">
                    <div class="metric-card" id="db-metric">
                        <h4>数据库连接</h4>
                        <div class="metric-value" id="db-connections">--</div>
                        <div class="metric-label">活跃连接</div>
                    </div>
                    
                    <div class="metric-card" id="cpu-metric">
                        <h4>CPU负载</h4>
                        <div class="metric-value" id="cpu-load">--</div>
                        <div class="metric-label">1分钟平均</div>
                    </div>
                    
                    <div class="metric-card" id="memory-metric">
                        <h4>内存使用</h4>
                        <div class="metric-value" id="memory-usage">--</div>
                        <div class="metric-label">使用率</div>
                    </div>
                    
                    <div class="metric-card" id="disk-metric">
                        <h4>磁盘空间</h4>
                        <div class="metric-value" id="disk-usage">--</div>
                        <div class="metric-label">使用率</div>
                    </div>
                </div>
                
                <div class="charts-container">
                    <div class="chart-card">
                        <h4>CPU负载趋势</h4>
                        <canvas id="cpu-chart" width="400" height="200"></canvas>
                    </div>
                    
                    <div class="chart-card">
                        <h4>内存使用趋势</h4>
                        <canvas id="memory-chart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>
        `;
        
        // 插入到页面中
        const container = document.querySelector('.container-fluid') || document.body;
        container.insertAdjacentHTML('afterbegin', dashboardHTML);
    }
    
    initializeCharts() {
        // 如果有Chart.js库，初始化图表
        if (typeof Chart !== 'undefined') {
            this.initCPUChart();
            this.initMemoryChart();
        }
    }
    
    initCPUChart() {
        const ctx = document.getElementById('cpu-chart').getContext('2d');
        this.charts.cpu = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'CPU负载',
                    data: [],
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 4
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    }
    
    initMemoryChart() {
        const ctx = document.getElementById('memory-chart').getContext('2d');
        this.charts.memory = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: '内存使用率',
                    data: [],
                    borderColor: 'rgb(255, 99, 132)',
                    backgroundColor: 'rgba(255, 99, 132, 0.2)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    }
    
    bindEvents() {
        const toggleBtn = document.getElementById('toggle-monitor');
        if (toggleBtn) {
            toggleBtn.addEventListener('click', () => {
                if (this.isRunning) {
                    this.stop();
                } else {
                    this.start();
                }
            });
        }
    }
    
    start() {
        if (this.isRunning) return;
        
        this.isRunning = true;
        this.update();
        this.intervalId = setInterval(() => this.update(), this.updateInterval);
        
        // 更新UI
        const statusEl = document.getElementById('monitor-status');
        const toggleBtn = document.getElementById('toggle-monitor');
        
        if (statusEl) statusEl.textContent = '监控中...';
        if (toggleBtn) toggleBtn.textContent = '停止监控';
    }
    
    stop() {
        if (!this.isRunning) return;
        
        this.isRunning = false;
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }
        
        // 更新UI
        const statusEl = document.getElementById('monitor-status');
        const toggleBtn = document.getElementById('toggle-monitor');
        
        if (statusEl) statusEl.textContent = '已停止';
        if (toggleBtn) toggleBtn.textContent = '开始监控';
    }
    
    async update() {
        try {
            const response = await fetch(`${this.apiUrl}?action=metrics`);
            const data = await response.json();
            
            this.updateMetrics(data);
            this.updateCharts(data);
            this.updateTimestamp();
            
        } catch (error) {
            console.error('监控更新失败:', error);
            this.handleError(error);
        }
    }
    
    updateMetrics(data) {
        // 更新数据库连接
        const dbEl = document.getElementById('db-connections');
        if (dbEl && data.database) {
            const usage = (data.database.connected / data.database.max_connections * 100).toFixed(1);
            dbEl.textContent = `${data.database.connected}/${data.database.max_connections}`;
            
            const metricCard = document.getElementById('db-metric');
            metricCard.className = `metric-card ${usage > 80 ? 'warning' : usage > 60 ? 'info' : 'success'}`;
        }
        
        // 更新CPU负载
        const cpuEl = document.getElementById('cpu-load');
        if (cpuEl && data.system && data.system.cpu_load) {
            const load = data.system.cpu_load['1min'];
            cpuEl.textContent = load.toFixed(2);
            
            const metricCard = document.getElementById('cpu-metric');
            metricCard.className = `metric-card ${load > 2 ? 'warning' : 'success'}`;
        }
        
        // 更新内存使用
        const memoryEl = document.getElementById('memory-usage');
        if (memoryEl && data.system && data.system.memory) {
            const usage = (data.system.memory.current_usage / data.system.memory.limit * 100);
            memoryEl.textContent = `${usage.toFixed(1)}%`;
            
            const metricCard = document.getElementById('memory-metric');
            metricCard.className = `metric-card ${usage > 85 ? 'warning' : usage > 70 ? 'info' : 'success'}`;
        }
        
        // 更新磁盘使用
        const diskEl = document.getElementById('disk-usage');
        if (diskEl && data.system && data.system.disk) {
            const usage = data.system.disk.usage_percent;
            diskEl.textContent = `${usage}%`;
            
            const metricCard = document.getElementById('disk-metric');
            metricCard.className = `metric-card ${usage > 85 ? 'warning' : usage > 70 ? 'info' : 'success'}`;
        }
    }
    
    updateCharts(data) {
        if (!this.charts.cpu || !this.charts.memory) return;
        
        const now = new Date().toLocaleTimeString();
        
        // 添加新数据点
        if (data.system && data.system.cpu_load) {
            this.historicalData.cpu_load.push(data.system.cpu_load['1min']);
        }
        
        if (data.system && data.system.memory) {
            const memoryUsage = (data.system.memory.current_usage / data.system.memory.limit * 100);
            this.historicalData.memory_usage.push(memoryUsage);
        }
        
        this.historicalData.timestamps.push(now);
        
        // 限制数据点数量
        if (this.historicalData.timestamps.length > this.maxDataPoints) {
            this.historicalData.cpu_load.shift();
            this.historicalData.memory_usage.shift();
            this.historicalData.timestamps.shift();
        }
        
        // 更新图表
        this.charts.cpu.data.labels = this.historicalData.timestamps;
        this.charts.cpu.data.datasets[0].data = this.historicalData.cpu_load;
        this.charts.cpu.update('none');
        
        this.charts.memory.data.labels = this.historicalData.timestamps;
        this.charts.memory.data.datasets[0].data = this.historicalData.memory_usage;
        this.charts.memory.update('none');
    }
    
    updateTimestamp() {
        const timestampEl = document.getElementById('last-update');
        if (timestampEl) {
            timestampEl.textContent = `最后更新: ${new Date().toLocaleTimeString()}`;
        }
    }
    
    handleError(error) {
        console.error('监控错误:', error);
        
        // 显示错误提示
        const errorDiv = document.createElement('div');
        errorDiv.className = 'alert alert-warning alert-dismissible fade show';
        errorDiv.innerHTML = `
            <strong>监控更新失败:</strong> ${error.message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        const dashboard = document.getElementById('monitor-dashboard');
        if (dashboard) {
            dashboard.insertBefore(errorDiv, dashboard.firstChild);
            
            // 3秒后自动移除
            setTimeout(() => {
                if (errorDiv.parentNode) {
                    errorDiv.remove();
                }
            }, 3000);
        }
    }
}

// 自动初始化
document.addEventListener('DOMContentLoaded', function() {
    // 添加必要的CSS样式
    const style = document.createElement('style');
    style.textContent = `
        .monitor-dashboard {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .dashboard-controls {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .status-indicator {
            padding: 4px 8px;
            border-radius: 4px;
            background: #28a745;
            color: white;
            font-size: 12px;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .metric-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            border-left: 4px solid #6c757d;
        }
        
        .metric-card.success { border-left-color: #28a745; }
        .metric-card.info { border-left-color: #17a2b8; }
        .metric-card.warning { border-left-color: #ffc107; }
        .metric-card.danger { border-left-color: #dc3545; }
        
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            margin: 10px 0;
        }
        
        .metric-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .charts-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
        }
        
        .chart-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
        }
        
        .last-update {
            font-size: 0.9rem;
            color: #6c757d;
        }
    `;
    document.head.appendChild(style);
    
    // 初始化监控仪表板
    window.monitorDashboard = new MonitorDashboard({
        updateInterval: 30000, // 30秒更新一次
        maxDataPoints: 20      // 最多显示20个数据点
    });
});
            </div>
            
            <div class="usage-note">
                <strong>💡 使用说明：</strong>
                <ul>
                    <li>需要引入Chart.js库来显示图表</li>
                    <li>自动在页面顶部创建监控仪表板</li>
                    <li>支持实时更新和历史趋势显示</li>
                    <li>可以通过按钮控制监控的开始和停止</li>
                </ul>
            </div>
        </div>

        <h2>📁 文件结构建议</h2>
        
        <div class="file-structure">
project/
├── diagnose/
│   ├── enhanced_diagnose.php      # 增强版诊断脚本
│   ├── monitor_api.php            # 监控API接口
│   ├── monitor_dashboard.js       # 前端监控界面
│   ├── diagnostic_history.sql     # 历史数据表结构
│   └── config.php                 # 配置文件
├── assets/
│   ├── css/
│   │   └── monitor.css           # 监控样式
│   └── js/
│       ├── chart.min.js          # Chart.js库
│       └── monitor_dashboard.js   # 监控脚本
└── logs/
    └── diagnostic.log            # 诊断日志
        </div>

        <div class="warning-note">
            <strong>⚠️ 部署注意事项：</strong>
            <ul>
                <li>确保数据库连接配置正确</li>
                <li>检查文件权限，确保PHP可以读写必要目录</li>
                <li>在生产环境中限制诊断页面的访问权限</li>
                <li>定期清理历史监控数据，避免数据表过大</li>
                <li>监控API接口建议添加认证机制</li>
            </ul>
        </div>

        <div style="text-align: center; margin-top: 40px; color: #7f8c8d;">
            <p>📅 脚本版本：v1.0</p>
            <p>🔄 最后更新：2025年8月7日</p>
            <p>📧 技术支持：<EMAIL></p>
        </div>
    </div>

    <script>
        // 复制代码功能
        document.addEventListener('DOMContentLoaded', function() {
            const codeSnippets = document.querySelectorAll('.code-snippet');
            
            codeSnippets.forEach(snippet => {
                const copyBtn = document.createElement('button');
                copyBtn.className = 'copy-btn';
                copyBtn.textContent = '复制';
                copyBtn.onclick = function() {
                    const text = snippet.textContent;
                    navigator.clipboard.writeText(text).then(() => {
                        copyBtn.textContent = '已复制';
                        setTimeout(() => {
                            copyBtn.textContent = '复制';
                        }, 2000);
                    });
                };
                
                snippet.style.position = 'relative';
                snippet.appendChild(copyBtn);
            });
        });
    </script>
</body>
</html>
