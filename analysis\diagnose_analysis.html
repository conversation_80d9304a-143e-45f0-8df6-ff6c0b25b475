<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>diagnose.php 数据来源分析</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }
        h3 {
            color: #2980b9;
            margin-top: 20px;
        }
        .data-source {
            background: #ecf0f1;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #3498db;
        }
        .static-data {
            background: #fff3cd;
            border-left-color: #ffc107;
        }
        .dynamic-data {
            background: #d1ecf1;
            border-left-color: #17a2b8;
        }
        .table-info {
            background: #d4edda;
            border-left-color: #28a745;
        }
        .code-snippet {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        .tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            margin-right: 5px;
        }
        .tag-static { background: #ffc107; color: #212529; }
        .tag-dynamic { background: #17a2b8; color: white; }
        .tag-table { background: #28a745; color: white; }
        .tag-function { background: #6f42c1; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <h1>diagnose.php 页面数据来源分析</h1>
        
        <h2>页面概述</h2>
        <p>diagnose.php 是一个系统诊断页面，用于检查系统的各个组件状态、数据库连接、数据表完整性以及基本的数据查询功能。</p>
        
        <h2>数据来源分析</h2>
        
        <h3>1. 静态数据</h3>
        
        <div class="data-source static-data">
            <h4><span class="tag tag-static">静态</span>配置数据</h4>
            <p><strong>数据内容：</strong>系统配置信息</p>
            <p><strong>来源：</strong>硬编码在PHP代码中</p>
            <div class="code-snippet">
$configs = array('apptheme' => '#1389D3');
            </div>
            <p><strong>说明：</strong>主题颜色等配置信息直接写在代码中，属于静态数据。</p>
        </div>
        
        <div class="data-source static-data">
            <h4><span class="tag tag-static">静态</span>检查项目列表</h4>
            <p><strong>数据内容：</strong>需要检查的数据表名称</p>
            <p><strong>来源：</strong>硬编码数组</p>
            <div class="code-snippet">
$tables = ['tuqoa_gcproject', 'tuqoa_htgl', 'tuqoa_xmcztjb'];
            </div>
            <p><strong>说明：</strong>要检查的数据表列表是预定义的静态数据。</p>
        </div>
        
        <div class="data-source static-data">
            <h4><span class="tag tag-static">静态</span>时间范围</h4>
            <p><strong>数据内容：</strong>当前月份的开始和结束日期</p>
            <p><strong>来源：</strong>PHP date函数生成</p>
            <div class="code-snippet">
$startDate = date('Y-m-01');  // 当月第一天
$endDate = date('Y-m-t');     // 当月最后一天
            </div>
            <p><strong>说明：</strong>虽然是动态计算的，但逻辑是固定的，用于限定查询范围。</p>
        </div>
        
        <h3>2. 动态数据（数据库查询）</h3>
        
        <div class="data-source dynamic-data">
            <h4><span class="tag tag-dynamic">动态</span><span class="tag tag-table">tuqoa_gcproject</span>活跃项目统计</h4>
            <p><strong>数据内容：</strong>非完工状态的项目数量</p>
            <p><strong>来源：</strong>tuqoa_gcproject 数据表</p>
            <div class="code-snippet">
SELECT COUNT(*) as count FROM `tuqoa_gcproject` 
WHERE `xmzt` not in ('完工项目','完工已结算','合同终止')
            </div>
            <p><strong>说明：</strong>统计当前活跃的项目数量，排除已完工和终止的项目。</p>
        </div>
        
        <div class="data-source dynamic-data">
            <h4><span class="tag tag-dynamic">动态</span><span class="tag tag-table">tuqoa_gcproject</span>合同金额统计</h4>
            <p><strong>数据内容：</strong>当月签订合同的总金额</p>
            <p><strong>来源：</strong>tuqoa_gcproject 数据表</p>
            <div class="code-snippet">
SELECT COALESCE(SUM(zaojia), 0) as htzje FROM `tuqoa_gcproject` 
WHERE `qdsj`>='$startDate' and `qdsj`<='$endDate'
            </div>
            <p><strong>说明：</strong>统计当月签订的合同总金额，使用COALESCE确保返回0而不是NULL。</p>
        </div>
        
        <div class="data-source dynamic-data">
            <h4><span class="tag tag-dynamic">动态</span><span class="tag tag-table">tuqoa_xmcztjb</span>成本数据统计</h4>
            <p><strong>数据内容：</strong>当月实际成本总额</p>
            <p><strong>来源：</strong>tuqoa_xmcztjb 数据表</p>
            <div class="code-snippet">
SELECT COALESCE(SUM(wccz), 0) as sjcbzje FROM `tuqoa_xmcztjb` 
WHERE `sbrq`>='$startDate' and `sbrq`<='$endDate'
            </div>
            <p><strong>说明：</strong>统计当月的实际成本支出总额。</p>
        </div>
        
        <div class="data-source dynamic-data">
            <h4><span class="tag tag-dynamic">动态</span>数据表记录数检查</h4>
            <p><strong>数据内容：</strong>各数据表的记录数量</p>
            <p><strong>来源：</strong>对每个表执行COUNT查询</p>
            <div class="code-snippet">
SELECT COUNT(*) as count FROM `$table`
            </div>
            <p><strong>说明：</strong>检查每个数据表是否存在以及包含的记录数量。</p>
        </div>
        
        <h3>3. 系统状态检查</h3>
        
        <div class="data-source table-info">
            <h4><span class="tag tag-function">系统</span>配置文件检查</h4>
            <p><strong>检查内容：</strong>config.php文件是否存在</p>
            <p><strong>检查方法：</strong>file_exists('../config.php')</p>
            <p><strong>说明：</strong>验证系统配置文件的可用性。</p>
        </div>
        
        <div class="data-source table-info">
            <h4><span class="tag tag-function">系统</span>数据库连接检查</h4>
            <p><strong>检查内容：</strong>数据库连接状态</p>
            <p><strong>检查方法：</strong>验证$link变量和连接状态</p>
            <p><strong>说明：</strong>确保数据库连接正常。</p>
        </div>
        
        <div class="data-source table-info">
            <h4><span class="tag tag-function">系统</span>文件权限检查</h4>
            <p><strong>检查内容：</strong>关键文件的读取权限</p>
            <p><strong>检查方法：</strong>is_readable()函数</p>
            <p><strong>说明：</strong>检查xmcbhs.php和config.php的读取权限。</p>
        </div>
        
        <h2>数据表结构</h2>
        
        <div class="table-info">
            <h4>主要数据表</h4>
            <ul>
                <li><strong>tuqoa_gcproject：</strong>工程项目表，包含项目状态(xmzt)、签订时间(qdsj)、造价(zaojia)等字段</li>
                <li><strong>tuqoa_htgl：</strong>合同管理表</li>
                <li><strong>tuqoa_xmcztjb：</strong>项目成本统计表，包含完成成本(wccz)、申报日期(sbrq)等字段</li>
            </ul>
        </div>
        
        <h2>页面特点</h2>
        <ul>
            <li>主要用于系统健康检查，不包含复杂的图表展示</li>
            <li>数据以统计卡片和诊断列表形式展示</li>
            <li>包含实时的系统状态检查</li>
            <li>提供基本的数据查询验证功能</li>
            <li>具有自动刷新和重新诊断功能</li>
        </ul>
        
        <h2>总结</h2>
        <p>diagnose.php页面主要用于系统诊断，数据来源包括：</p>
        <ul>
            <li><strong>静态数据：</strong>配置信息、检查项目列表等硬编码数据</li>
            <li><strong>动态数据：</strong>从tuqoa_gcproject和tuqoa_xmcztjb表查询的统计数据</li>
            <li><strong>系统检查：</strong>文件存在性、数据库连接状态、权限检查等</li>
        </ul>
        <p>该页面不包含复杂的图表，主要以诊断卡片和状态列表的形式展示系统健康状况。</p>
    </div>
</body>
</html>
