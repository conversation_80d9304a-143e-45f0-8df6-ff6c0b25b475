<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据字典文档</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 10px;
            font-size: 2.5rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .subtitle {
            text-align: center;
            color: #7f8c8d;
            margin-bottom: 30px;
            font-size: 1.1rem;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }
        h3 {
            color: #2980b9;
            margin-top: 20px;
        }
        .table-card {
            background: #f8f9fa;
            padding: 20px;
            margin: 15px 0;
            border-radius: 10px;
            border-left: 4px solid #3498db;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .core-table { border-left-color: #e74c3c; }
        .business-table { border-left-color: #f39c12; }
        .support-table { border-left-color: #27ae60; }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .table th, .table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
            font-size: 14px;
        }
        .table th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            font-weight: 600;
        }
        .table tr:hover {
            background-color: #f8f9fa;
        }
        .field-type {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: bold;
        }
        .type-int { background: #d4edda; color: #155724; }
        .type-varchar { background: #cce5ff; color: #004085; }
        .type-decimal { background: #fff3cd; color: #856404; }
        .type-date { background: #f8d7da; color: #721c24; }
        .type-datetime { background: #e2e3e5; color: #383d41; }
        .type-text { background: #d1ecf1; color: #0c5460; }
        .relationship-diagram {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
        }
        .table-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin: 10px 0;
        }
        .index-info {
            background: #e7f3ff;
            border: 1px solid #b8daff;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
            font-size: 13px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📚 数据字典文档</h1>
        <p class="subtitle">系统数据库表结构和字段详细说明</p>
        
        <div class="table-stats">
            <div class="stat-card">
                <h3>核心表</h3>
                <div class="stat-number">6</div>
                <p>主要业务表</p>
            </div>
            <div class="stat-card">
                <h3>业务表</h3>
                <div class="stat-number">11</div>
                <p>监理业务表</p>
            </div>
            <div class="stat-card">
                <h3>支撑表</h3>
                <div class="stat-number">8</div>
                <p>辅助功能表</p>
            </div>
            <div class="stat-card">
                <h3>总计</h3>
                <div class="stat-number">25+</div>
                <p>数据表总数</p>
            </div>
        </div>

        <h2>🔥 核心业务表</h2>

        <div class="table-card core-table">
            <h3>tuqoa_gcproject - 工程项目表</h3>
            <p><strong>用途：</strong>存储工程项目的基本信息，是系统的核心表</p>
            <p><strong>使用频率：</strong>极高 (8+个页面使用)</p>
            
            <table class="table">
                <thead>
                    <tr>
                        <th>字段名</th>
                        <th>数据类型</th>
                        <th>是否必填</th>
                        <th>说明</th>
                        <th>示例值</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>id</td>
                        <td><span class="field-type type-int">INT</span></td>
                        <td>是</td>
                        <td>项目唯一标识，主键</td>
                        <td>1001</td>
                    </tr>
                    <tr>
                        <td>gcname</td>
                        <td><span class="field-type type-varchar">VARCHAR(255)</span></td>
                        <td>是</td>
                        <td>工程项目名称</td>
                        <td>某市政道路工程</td>
                    </tr>
                    <tr>
                        <td>xmzt</td>
                        <td><span class="field-type type-varchar">VARCHAR(50)</span></td>
                        <td>是</td>
                        <td>项目状态</td>
                        <td>在建项目</td>
                    </tr>
                    <tr>
                        <td>zaojia</td>
                        <td><span class="field-type type-decimal">DECIMAL(15,2)</span></td>
                        <td>否</td>
                        <td>项目造价（元）</td>
                        <td>5000000.00</td>
                    </tr>
                    <tr>
                        <td>qdsj</td>
                        <td><span class="field-type type-date">DATE</span></td>
                        <td>否</td>
                        <td>签订时间</td>
                        <td>2024-01-15</td>
                    </tr>
                    <tr>
                        <td>bumen</td>
                        <td><span class="field-type type-varchar">VARCHAR(100)</span></td>
                        <td>否</td>
                        <td>所属部门</td>
                        <td>市政工程部</td>
                    </tr>
                    <tr>
                        <td>leixing</td>
                        <td><span class="field-type type-varchar">VARCHAR(50)</span></td>
                        <td>否</td>
                        <td>项目类型</td>
                        <td>市政工程</td>
                    </tr>
                    <tr>
                        <td>xmxz</td>
                        <td><span class="field-type type-varchar">VARCHAR(50)</span></td>
                        <td>否</td>
                        <td>项目性质</td>
                        <td>新建</td>
                    </tr>
                    <tr>
                        <td>jhstartdt</td>
                        <td><span class="field-type type-date">DATE</span></td>
                        <td>否</td>
                        <td>计划开始日期</td>
                        <td>2024-02-01</td>
                    </tr>
                    <tr>
                        <td>jhenddt</td>
                        <td><span class="field-type type-date">DATE</span></td>
                        <td>否</td>
                        <td>计划结束日期</td>
                        <td>2024-12-31</td>
                    </tr>
                </tbody>
            </table>
            
            <div class="index-info">
                <strong>建议索引：</strong>
                <ul>
                    <li>PRIMARY KEY (id)</li>
                    <li>INDEX idx_xmzt (xmzt)</li>
                    <li>INDEX idx_qdsj (qdsj)</li>
                    <li>INDEX idx_bumen (bumen)</li>
                </ul>
            </div>
        </div>

        <div class="table-card core-table">
            <h3>tuqoa_htgl - 合同管理表</h3>
            <p><strong>用途：</strong>存储合同信息和服务费数据</p>
            <p><strong>使用频率：</strong>高 (6+个页面使用)</p>
            
            <table class="table">
                <thead>
                    <tr>
                        <th>字段名</th>
                        <th>数据类型</th>
                        <th>是否必填</th>
                        <th>说明</th>
                        <th>示例值</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>id</td>
                        <td><span class="field-type type-int">INT</span></td>
                        <td>是</td>
                        <td>合同唯一标识</td>
                        <td>2001</td>
                    </tr>
                    <tr>
                        <td>projectid</td>
                        <td><span class="field-type type-int">INT</span></td>
                        <td>是</td>
                        <td>关联项目ID</td>
                        <td>1001</td>
                    </tr>
                    <tr>
                        <td>htbh</td>
                        <td><span class="field-type type-varchar">VARCHAR(100)</span></td>
                        <td>否</td>
                        <td>合同编号</td>
                        <td>HT2024001</td>
                    </tr>
                    <tr>
                        <td>htmc</td>
                        <td><span class="field-type type-varchar">VARCHAR(255)</span></td>
                        <td>是</td>
                        <td>合同名称</td>
                        <td>监理服务合同</td>
                    </tr>
                    <tr>
                        <td>khmc</td>
                        <td><span class="field-type type-varchar">VARCHAR(255)</span></td>
                        <td>否</td>
                        <td>客户名称</td>
                        <td>某建设集团</td>
                    </tr>
                    <tr>
                        <td>fwf</td>
                        <td><span class="field-type type-decimal">DECIMAL(15,2)</span></td>
                        <td>是</td>
                        <td>服务费（元）</td>
                        <td>480000.00</td>
                    </tr>
                    <tr>
                        <td>qdsj</td>
                        <td><span class="field-type type-date">DATE</span></td>
                        <td>是</td>
                        <td>签订时间</td>
                        <td>2024-01-15</td>
                    </tr>
                </tbody>
            </table>
            
            <div class="index-info">
                <strong>建议索引：</strong>
                <ul>
                    <li>PRIMARY KEY (id)</li>
                    <li>INDEX idx_projectid (projectid)</li>
                    <li>INDEX idx_qdsj (qdsj)</li>
                    <li>FOREIGN KEY (projectid) REFERENCES tuqoa_gcproject(id)</li>
                </ul>
            </div>
        </div>

        <div class="table-card core-table">
            <h3>tuqoa_xmcztjb - 项目成本统计表</h3>
            <p><strong>用途：</strong>记录项目的成本支出明细</p>
            <p><strong>使用频率：</strong>高 (6+个页面使用)</p>
            
            <table class="table">
                <thead>
                    <tr>
                        <th>字段名</th>
                        <th>数据类型</th>
                        <th>是否必填</th>
                        <th>说明</th>
                        <th>示例值</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>id</td>
                        <td><span class="field-type type-int">INT</span></td>
                        <td>是</td>
                        <td>记录唯一标识</td>
                        <td>3001</td>
                    </tr>
                    <tr>
                        <td>projectid</td>
                        <td><span class="field-type type-int">INT</span></td>
                        <td>是</td>
                        <td>关联项目ID</td>
                        <td>1001</td>
                    </tr>
                    <tr>
                        <td>wccz</td>
                        <td><span class="field-type type-decimal">DECIMAL(15,2)</span></td>
                        <td>是</td>
                        <td>完成成本（元）</td>
                        <td>150000.00</td>
                    </tr>
                    <tr>
                        <td>sbrq</td>
                        <td><span class="field-type type-date">DATE</span></td>
                        <td>是</td>
                        <td>申报日期</td>
                        <td>2024-01-31</td>
                    </tr>
                    <tr>
                        <td>cbfl</td>
                        <td><span class="field-type type-varchar">VARCHAR(50)</span></td>
                        <td>否</td>
                        <td>成本分类</td>
                        <td>人工费</td>
                    </tr>
                    <tr>
                        <td>bz</td>
                        <td><span class="field-type type-text">TEXT</span></td>
                        <td>否</td>
                        <td>备注说明</td>
                        <td>1月份人工成本</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="table-card core-table">
            <h3>tuqoa_htsf - 合同收费表</h3>
            <p><strong>用途：</strong>记录合同的收费计划和实际收款</p>
            <p><strong>使用频率：</strong>中等 (5+个页面使用)</p>
            
            <table class="table">
                <thead>
                    <tr>
                        <th>字段名</th>
                        <th>数据类型</th>
                        <th>是否必填</th>
                        <th>说明</th>
                        <th>示例值</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>id</td>
                        <td><span class="field-type type-int">INT</span></td>
                        <td>是</td>
                        <td>收费记录ID</td>
                        <td>4001</td>
                    </tr>
                    <tr>
                        <td>projectid</td>
                        <td><span class="field-type type-int">INT</span></td>
                        <td>是</td>
                        <td>关联项目ID</td>
                        <td>1001</td>
                    </tr>
                    <tr>
                        <td>htmc</td>
                        <td><span class="field-type type-varchar">VARCHAR(255)</span></td>
                        <td>否</td>
                        <td>合同名称</td>
                        <td>监理服务合同</td>
                    </tr>
                    <tr>
                        <td>yjje</td>
                        <td><span class="field-type type-decimal">DECIMAL(15,2)</span></td>
                        <td>是</td>
                        <td>预计金额（元）</td>
                        <td>120000.00</td>
                    </tr>
                    <tr>
                        <td>ysje</td>
                        <td><span class="field-type type-decimal">DECIMAL(15,2)</span></td>
                        <td>否</td>
                        <td>已收金额（元）</td>
                        <td>120000.00</td>
                    </tr>
                    <tr>
                        <td>yjsj</td>
                        <td><span class="field-type type-date">DATE</span></td>
                        <td>是</td>
                        <td>预计时间</td>
                        <td>2024-02-15</td>
                    </tr>
                    <tr>
                        <td>sksj</td>
                        <td><span class="field-type type-date">DATE</span></td>
                        <td>否</td>
                        <td>收款时间</td>
                        <td>2024-02-15</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <h2>👥 人员管理表</h2>

        <div class="table-card business-table">
            <h3>tuqoa_userinfo - 用户信息表</h3>
            <p><strong>用途：</strong>存储员工基本信息</p>
            
            <table class="table">
                <thead>
                    <tr>
                        <th>字段名</th>
                        <th>数据类型</th>
                        <th>说明</th>
                        <th>示例值</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>id</td>
                        <td><span class="field-type type-int">INT</span></td>
                        <td>用户ID</td>
                        <td>5001</td>
                    </tr>
                    <tr>
                        <td>username</td>
                        <td><span class="field-type type-varchar">VARCHAR(50)</span></td>
                        <td>用户名</td>
                        <td>zhangsan</td>
                    </tr>
                    <tr>
                        <td>realname</td>
                        <td><span class="field-type type-varchar">VARCHAR(50)</span></td>
                        <td>真实姓名</td>
                        <td>张三</td>
                    </tr>
                    <tr>
                        <td>department</td>
                        <td><span class="field-type type-varchar">VARCHAR(100)</span></td>
                        <td>所属部门</td>
                        <td>市政工程部</td>
                    </tr>
                    <tr>
                        <td>workdate</td>
                        <td><span class="field-type type-date">DATE</span></td>
                        <td>入职日期</td>
                        <td>2023-01-15</td>
                    </tr>
                    <tr>
                        <td>state</td>
                        <td><span class="field-type type-int">INT</span></td>
                        <td>员工状态 (5=离职)</td>
                        <td>1</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="table-card business-table">
            <h3>tuqoa_rydp - 人员配置表</h3>
            <p><strong>用途：</strong>记录员工的项目分配情况</p>
            
            <table class="table">
                <thead>
                    <tr>
                        <th>字段名</th>
                        <th>数据类型</th>
                        <th>说明</th>
                        <th>示例值</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>id</td>
                        <td><span class="field-type type-int">INT</span></td>
                        <td>配置记录ID</td>
                        <td>6001</td>
                    </tr>
                    <tr>
                        <td>drxmid</td>
                        <td><span class="field-type type-int">INT</span></td>
                        <td>项目ID</td>
                        <td>1001</td>
                    </tr>
                    <tr>
                        <td>dpryxm</td>
                        <td><span class="field-type type-varchar">VARCHAR(50)</span></td>
                        <td>配置人员姓名</td>
                        <td>张三</td>
                    </tr>
                    <tr>
                        <td>sfqz</td>
                        <td><span class="field-type type-varchar">VARCHAR(10)</span></td>
                        <td>是否全职</td>
                        <td>全职</td>
                    </tr>
                    <tr>
                        <td>state</td>
                        <td><span class="field-type type-varchar">VARCHAR(10)</span></td>
                        <td>状态</td>
                        <td>在职</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <h2>📋 监理业务表</h2>

        <div class="table-card support-table">
            <h3>监理工作记录表（11个表）</h3>
            <p><strong>用途：</strong>记录各类监理工作的执行情况</p>
            <p><strong>使用页面：</strong>主要在 gztz.php 中使用</p>
            
            <table class="table">
                <thead>
                    <tr>
                        <th>表名</th>
                        <th>用途</th>
                        <th>主要时间字段</th>
                        <th>记录内容</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>tuqoa_jlrz</td>
                        <td>监理日志</td>
                        <td>kssj</td>
                        <td>日常监理工作记录</td>
                    </tr>
                    <tr>
                        <td>tuqoa_aqrz</td>
                        <td>安全日志</td>
                        <td>kssj</td>
                        <td>安全检查记录</td>
                    </tr>
                    <tr>
                        <td>tuqoa_xmxjcc</td>
                        <td>现场巡查</td>
                        <td>xjrq</td>
                        <td>现场巡视检查记录</td>
                    </tr>
                    <tr>
                        <td>tuqoa_pzjl</td>
                        <td>旁站监理</td>
                        <td>kssj, jssj</td>
                        <td>旁站监理记录</td>
                    </tr>
                    <tr>
                        <td>tuqoa_zxjc</td>
                        <td>专项检查</td>
                        <td>jcsj</td>
                        <td>专项检查记录</td>
                    </tr>
                    <tr>
                        <td>tuqoa_aqjc</td>
                        <td>安全检查</td>
                        <td>jcsj</td>
                        <td>安全检查记录</td>
                    </tr>
                    <tr>
                        <td>tuqoa_gcys</td>
                        <td>工程验收</td>
                        <td>yssj</td>
                        <td>工程验收记录</td>
                    </tr>
                    <tr>
                        <td>tuqoa_jcys</td>
                        <td>见证检验</td>
                        <td>jcsj</td>
                        <td>见证检验记录</td>
                    </tr>
                    <tr>
                        <td>tuqoa_xmfw</td>
                        <td>项目服务</td>
                        <td>fwrq</td>
                        <td>项目服务记录</td>
                    </tr>
                    <tr>
                        <td>tuqoa_hyjy</td>
                        <td>会议纪要</td>
                        <td>kssj</td>
                        <td>会议记录</td>
                    </tr>
                    <tr>
                        <td>tuqoa_pxjc</td>
                        <td>平行检验</td>
                        <td>qysj</td>
                        <td>平行检验记录</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <h2>🔗 表关系图</h2>

        <div class="relationship-diagram">
            <h3>核心表关系</h3>
            <div style="font-family: monospace; font-size: 14px; line-height: 2;">
                tuqoa_gcproject (项目表)<br>
                &nbsp;&nbsp;&nbsp;&nbsp;├── tuqoa_htgl (合同表) [projectid]<br>
                &nbsp;&nbsp;&nbsp;&nbsp;├── tuqoa_xmcztjb (成本表) [projectid]<br>
                &nbsp;&nbsp;&nbsp;&nbsp;├── tuqoa_htsf (收费表) [projectid]<br>
                &nbsp;&nbsp;&nbsp;&nbsp;├── tuqoa_rydp (人员配置) [drxmid]<br>
                &nbsp;&nbsp;&nbsp;&nbsp;└── 监理业务表 [projectid]<br>
                <br>
                tuqoa_userinfo (用户表)<br>
                &nbsp;&nbsp;&nbsp;&nbsp;├── tuqoa_rydp (人员配置) [dpryxm]<br>
                &nbsp;&nbsp;&nbsp;&nbsp;├── tuqoa_hrsalary (工资表) [uname]<br>
                &nbsp;&nbsp;&nbsp;&nbsp;└── tuqoa_kqdkjl (考勤表) [username]
            </div>
        </div>

        <div style="text-align: center; margin-top: 40px; color: #7f8c8d;">
            <p>📅 文档版本：v1.0</p>
            <p>🔄 最后更新：2025年8月7日</p>
            <p>📝 基于系统页面分析生成</p>
        </div>
    </div>
</body>
</html>
