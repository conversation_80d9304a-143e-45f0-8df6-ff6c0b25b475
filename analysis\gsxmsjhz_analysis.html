<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>gsxmsjhz.php 数据来源分析</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }
        h3 {
            color: #2980b9;
            margin-top: 20px;
        }
        .data-source {
            background: #ecf0f1;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #3498db;
        }
        .static-data {
            background: #fff3cd;
            border-left-color: #ffc107;
        }
        .dynamic-data {
            background: #d1ecf1;
            border-left-color: #17a2b8;
        }
        .table-info {
            background: #d4edda;
            border-left-color: #28a745;
        }
        .chart-info {
            background: #f8d7da;
            border-left-color: #dc3545;
        }
        .code-snippet {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        .tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            margin-right: 5px;
        }
        .tag-static { background: #ffc107; color: #212529; }
        .tag-dynamic { background: #17a2b8; color: white; }
        .tag-table { background: #28a745; color: white; }
        .tag-chart { background: #dc3545; color: white; }
        .tag-form { background: #fd7e14; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <h1>gsxmsjhz.php 页面数据来源分析</h1>
        
        <h2>页面概述</h2>
        <p>gsxmsjhz.php 是一个公司项目数据汇总页面，提供全面的项目统计分析，包括合同总额、成本支出、到账情况等关键指标，以及多维度的图表分析。</p>
        
        <h2>数据来源分析</h2>
        
        <h3>1. 用户输入数据</h3>
        
        <div class="data-source static-data">
            <h4><span class="tag tag-form">表单</span>日期范围选择</h4>
            <p><strong>数据内容：</strong>查询的开始日期和结束日期</p>
            <p><strong>来源：</strong>用户通过表单输入，带日期格式验证</p>
            <div class="code-snippet">
$startDate = isset($_POST['start-date']) ? $_POST['start-date'] : $firstDayOfMonth;
$endDate = isset($_POST['end-date']) ? $_POST['end-date'] : $lastDayOfMonth;

// 日期格式验证
if (DateTime::createFromFormat('Y-m-d', $_POST['start-date']) !== false) {
    $startDate = $_POST['start-date'];
}
            </div>
            <p><strong>默认值：</strong>当月第一天到最后一天</p>
        </div>
        
        <h3>2. 动态数据（数据库查询）</h3>
        
        <div class="data-source dynamic-data">
            <h4><span class="tag tag-dynamic">动态</span><span class="tag tag-table">tuqoa_htgl</span>合同总额统计</h4>
            <p><strong>数据内容：</strong>指定时间范围内的服务费合计</p>
            <p><strong>来源：</strong>tuqoa_htgl（合同管理表）</p>
            <div class="code-snippet">
SELECT sum(fwf) fwfhj FROM `tuqoa_htgl` 
WHERE `qdsj`>='$startDate' and `qdsj`<='$endDate'
            </div>
            <p><strong>说明：</strong>统计期间签约的服务费总额</p>
        </div>
        
        <div class="data-source dynamic-data">
            <h4><span class="tag tag-dynamic">动态</span><span class="tag tag-table">tuqoa_xmcztjb</span>成本支出统计</h4>
            <p><strong>数据内容：</strong>项目实际成本支出总额</p>
            <p><strong>来源：</strong>tuqoa_xmcztjb（项目成本统计表）</p>
            <div class="code-snippet">
SELECT IFNULL(SUM(wccz), 0) hj FROM `tuqoa_xmcztjb`  
WHERE `sbrq`>='$startDate' and `sbrq`<='$endDate'
            </div>
            <p><strong>说明：</strong>统计期间的完成成本总额</p>
        </div>
        
        <div class="data-source dynamic-data">
            <h4><span class="tag tag-dynamic">动态</span><span class="tag tag-table">tuqoa_htsf</span>实际到账统计</h4>
            <p><strong>数据内容：</strong>实际收款金额总额</p>
            <p><strong>来源：</strong>tuqoa_htsf（合同收费表）</p>
            <div class="code-snippet">
SELECT IFNULL(SUM(ysje), 0) as dzje FROM `tuqoa_htsf` 
WHERE `sksj`>='$startDate' and `sksj`<='$endDate'
            </div>
            <p><strong>说明：</strong>统计期间的实际收款总额</p>
        </div>
        
        <h3>3. 图表数据（复杂查询）</h3>
        
        <div class="data-source chart-info">
            <h4><span class="tag tag-chart">图表</span>合同额与到账额趋势（按月）</h4>
            <p><strong>数据内容：</strong>12个月的合同额和到账额趋势</p>
            <p><strong>来源：</strong>tuqoa_htgl和tuqoa_htsf表</p>
            <div class="code-snippet">
// 合同额查询
SELECT IFNULL(SUM(fwf), 0) as amount FROM `tuqoa_htgl`
WHERE DATE_FORMAT(qdsj, '%Y-%m') = '$year-$month'

// 到账额查询  
SELECT IFNULL(SUM(ysje), 0) as amount FROM `tuqoa_htsf`
WHERE DATE_FORMAT(sksj, '%Y-%m') = '$year-$month'
            </div>
            <p><strong>说明：</strong>循环查询12个月数据，生成趋势图</p>
        </div>
        
        <div class="data-source chart-info">
            <h4><span class="tag tag-chart">图表</span>项目状态分布</h4>
            <p><strong>数据内容：</strong>项目按状态分类的数量分布</p>
            <p><strong>来源：</strong>tuqoa_gcproject（工程项目表）</p>
            <div class="code-snippet">
SELECT
    CASE
        WHEN xmzt LIKE '%完工%' OR xmzt LIKE '%结算%' THEN '已完成'
        WHEN xmzt LIKE '%在建%' OR xmzt LIKE '%执行%' THEN '执行中'
        WHEN xmzt LIKE '%新开%' OR xmzt LIKE '%开工%' THEN '新开工'
        WHEN xmzt LIKE '%暂停%' OR xmzt LIKE '%终止%' THEN '暂停/终止'
        ELSE '其他'
    END as status_type,
    COUNT(*) as count
FROM `tuqoa_gcproject`
WHERE qdsj >= '$startDate' AND qdsj <= '$endDate'
GROUP BY status_type
            </div>
            <p><strong>说明：</strong>使用CASE语句对项目状态进行分类统计</p>
        </div>
        
        <div class="data-source chart-info">
            <h4><span class="tag tag-chart">图表</span>项目类型分布</h4>
            <p><strong>数据内容：</strong>项目按类型分类的数量分布</p>
            <p><strong>来源：</strong>tuqoa_gcproject表</p>
            <div class="code-snippet">
SELECT
    CASE
        WHEN xmxz LIKE '%市政%' OR xmxz LIKE '%道路%' THEN '市政工程'
        WHEN xmxz LIKE '%建筑%' OR xmxz LIKE '%房建%' THEN '建筑工程'
        WHEN xmxz LIKE '%装修%' OR xmxz LIKE '%装饰%' THEN '装修工程'
        WHEN xmxz LIKE '%水利%' THEN '水利工程'
        ELSE '其他工程'
    END as project_type,
    COUNT(*) as count
FROM `tuqoa_gcproject`
WHERE qdsj >= '$startDate' AND qdsj <= '$endDate'
GROUP BY project_type
            </div>
            <p><strong>说明：</strong>根据项目性质字段进行分类统计</p>
        </div>
        
        <div class="data-source chart-info">
            <h4><span class="tag tag-chart">图表</span>到账率分布</h4>
            <p><strong>数据内容：</strong>项目按到账率区间的分布情况</p>
            <p><strong>来源：</strong>tuqoa_htgl和tuqoa_htsf表关联查询</p>
            <div class="code-snippet">
SELECT
    h.fwf as contract_amount,
    IFNULL(SUM(s.ysje), 0) as received_amount
FROM `tuqoa_htgl` h
LEFT JOIN `tuqoa_htsf` s ON h.projectid = s.projectid
WHERE h.qdsj >= '$startDate' AND h.qdsj <= '$endDate'
GROUP BY h.id, h.fwf
HAVING contract_amount > 0

// 计算到账率并分配到区间
$rate = ($row['received_amount'] / $row['contract_amount']) * 100;
            </div>
            <p><strong>说明：</strong>计算每个项目的到账率，按0-25%、26-50%、51-75%、76-100%分组</p>
        </div>
        
        <div class="data-source chart-info">
            <h4><span class="tag tag-chart">图表</span>月度产值趋势</h4>
            <p><strong>数据内容：</strong>12个月的成本支出趋势</p>
            <p><strong>来源：</strong>tuqoa_xmcztjb表</p>
            <div class="code-snippet">
SELECT IFNULL(SUM(wccz), 0) as amount FROM `tuqoa_xmcztjb`
WHERE DATE_FORMAT(sbrq, '%Y-%m') = '$year-$month'
            </div>
            <p><strong>说明：</strong>按月统计完成成本，生成产值趋势图</p>
        </div>
        
        <div class="data-source chart-info">
            <h4><span class="tag tag-chart">图表</span>项目规模分布</h4>
            <p><strong>数据内容：</strong>项目按造价规模的分布情况</p>
            <p><strong>来源：</strong>tuqoa_gcproject表</p>
            <div class="code-snippet">
SELECT
    CASE
        WHEN zaojia >= 1000 THEN '大型项目(≥1000万)'
        WHEN zaojia >= 500 THEN '中型项目(500-1000万)'
        WHEN zaojia >= 100 THEN '小型项目(100-500万)'
        WHEN zaojia > 0 THEN '微型项目(<100万)'
        ELSE '未定义'
    END as scale_type,
    COUNT(*) as count,
    IFNULL(SUM(zaojia), 0) as total_amount
FROM `tuqoa_gcproject`
WHERE qdsj >= '$startDate' AND qdsj <= '$endDate'
GROUP BY scale_type
            </div>
            <p><strong>说明：</strong>按造价金额分为大、中、小、微型项目</p>
        </div>
        
        <h2>数据表结构</h2>
        
        <div class="table-info">
            <h4>主要数据表</h4>
            <ul>
                <li><strong>tuqoa_htgl：</strong>合同管理表
                    <ul>
                        <li>fwf：服务费</li>
                        <li>qdsj：签订时间</li>
                        <li>projectid：项目ID</li>
                    </ul>
                </li>
                <li><strong>tuqoa_xmcztjb：</strong>项目成本统计表
                    <ul>
                        <li>wccz：完成成本</li>
                        <li>sbrq：申报日期</li>
                    </ul>
                </li>
                <li><strong>tuqoa_htsf：</strong>合同收费表
                    <ul>
                        <li>ysje：已收金额</li>
                        <li>sksj：收款时间</li>
                        <li>projectid：项目ID</li>
                    </ul>
                </li>
                <li><strong>tuqoa_gcproject：</strong>工程项目表
                    <ul>
                        <li>xmzt：项目状态</li>
                        <li>xmxz：项目性质</li>
                        <li>zaojia：造价</li>
                        <li>qdsj：签订时间</li>
                    </ul>
                </li>
            </ul>
        </div>
        
        <h2>页面特点</h2>
        
        <h3>1. 统计卡片</h3>
        <ul>
            <li>合同总额：期间签约总额</li>
            <li>成本支出：实际成本总额</li>
            <li>实际到账：收款金额总额</li>
            <li>差额计算：合同总额 - 实际到账</li>
        </ul>
        
        <h3>2. 多维度图表分析</h3>
        <ul>
            <li>时间维度：月度趋势分析</li>
            <li>状态维度：项目状态分布</li>
            <li>类型维度：项目类型分布</li>
            <li>规模维度：项目规模分布</li>
            <li>效率维度：到账率分布</li>
        </ul>
        
        <h3>3. 数据处理特点</h3>
        <ul>
            <li>使用CASE语句进行数据分类</li>
            <li>使用DATE_FORMAT进行时间维度统计</li>
            <li>使用LEFT JOIN关联多表查询</li>
            <li>使用IFNULL处理空值</li>
            <li>动态计算到账率并分组</li>
        </ul>
        
        <h2>总结</h2>
        <p>gsxmsjhz.php页面是一个功能完整的项目数据汇总分析系统，数据来源包括：</p>
        <ul>
            <li><strong>用户输入：</strong>日期范围选择（带格式验证）</li>
            <li><strong>动态数据：</strong>从4个主要数据表查询的实时数据</li>
            <li><strong>复杂计算：</strong>多维度分类统计、趋势分析、比率计算</li>
            <li><strong>图表展示：</strong>6种不同类型的数据可视化</li>
        </ul>
        <p>该页面通过复杂的SQL查询和数据处理，提供了全面的项目数据分析功能，是一个典型的商业智能报表页面。</p>
    </div>
</body>
</html>
