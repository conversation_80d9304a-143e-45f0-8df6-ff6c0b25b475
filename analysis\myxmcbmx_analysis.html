<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>某月项目成本明细分析</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 10px;
            font-size: 2.5rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .subtitle {
            text-align: center;
            color: #7f8c8d;
            margin-bottom: 30px;
            font-size: 1.1rem;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }
        h3 {
            color: #2980b9;
            margin-top: 20px;
        }
        .analysis-card {
            background: #f8f9fa;
            padding: 20px;
            margin: 15px 0;
            border-radius: 10px;
            border-left: 4px solid #3498db;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .data-source { border-left-color: #28a745; }
        .feature { border-left-color: #ffc107; }
        .chart { border-left-color: #dc3545; }
        .business { border-left-color: #6f42c1; }
        .code-snippet {
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 10px 0;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .table th, .table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
            font-size: 14px;
        }
        .table th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            font-weight: 600;
        }
        .highlight-box {
            background: #e7f3ff;
            border: 1px solid #b8daff;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .tag {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            margin-right: 5px;
        }
        .tag-query { background: #28a745; color: white; }
        .tag-calc { background: #ffc107; color: #212529; }
        .tag-detail { background: #dc3545; color: white; }
        .tag-summary { background: #6f42c1; color: white; }
        .cost-breakdown {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .cost-item {
            background: white;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }
        .metric-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📋 某月项目成本明细分析</h1>
        <p class="subtitle">myxmcbmx.php - 单项目月度成本详细分解和分析</p>
        
        <h2>📋 页面功能概述</h2>
        
        <div class="analysis-card business">
            <h3>核心功能</h3>
            <p>某月项目成本明细页面是项目精细化管理的重要工具，提供单个项目的深度成本分析：</p>
            <ul>
                <li><strong>项目成本明细：</strong>详细展示单个项目的月度成本构成</li>
                <li><strong>多维度对比：</strong>月度、年度、累计成本的对比分析</li>
                <li><strong>成本结构分析：</strong>人工、管理、业务等各项成本的详细分解</li>
                <li><strong>收支平衡分析：</strong>成本与收入的对比，评估项目盈利能力</li>
            </ul>
            
            <div class="highlight-box">
                <strong>💡 管理价值：</strong>
                <ul>
                    <li>为项目经理提供精确的成本控制数据</li>
                    <li>帮助识别成本异常和优化空间</li>
                    <li>支持项目盈利能力评估</li>
                    <li>为成本预算制定提供历史数据</li>
                </ul>
            </div>
        </div>

        <h2>🗄️ 详细数据来源分析</h2>

        <div class="analysis-card data-source">
            <h3>核心数据表结构</h3>
            
            <h4><span class="tag tag-query">项目</span>tuqoa_gcproject - 工程项目表</h4>
            <div class="code-snippet">
-- 获取特定项目信息
SELECT * FROM `tuqoa_gcproject` WHERE id=$projectid
            </div>
            <p><strong>用途：</strong>获取项目基本信息，作为成本分析的基础</p>
            
            <h4><span class="tag tag-query">成本</span>tuqoa_xmcztjb - 项目成本统计表</h4>
            <div class="code-snippet">
-- 月度成本明细
SELECT * FROM `tuqoa_xmcztjb` 
where projectid=$projectId and `sbrq` like '$selectedMonth%' 
order by id desc

-- 年度成本汇总
select IFNULL(SUM(wccz), 0) as wccznd 
from tuqoa_xmcztjb 
where projectid=$projectId and sbrq like '$year%'

-- 累计成本统计
select IFNULL(sum(wccz),0) as wcczlj 
from tuqoa_xmcztjb 
where projectid=$projectId
            </div>
            <p><strong>用途：</strong>核心成本数据，提供完成产值和进度信息</p>
            
            <h4><span class="tag tag-query">人员</span>tuqoa_rydp - 人员配置表</h4>
            <div class="code-snippet">
-- 获取项目全职人员配置
SELECT * FROM `tuqoa_rydp` 
WHERE `drxmid`=$projectId and `sfqz`='全职' and `state`='在职'
            </div>
            <p><strong>用途：</strong>获取项目人员配置，为人工成本计算提供基础</p>
            
            <h4><span class="tag tag-query">工资</span>tuqoa_hrsalary - 人力资源工资表</h4>
            <div class="code-snippet">
-- 查询员工月度工资详情
SELECT * FROM `tuqoa_hrsalary` 
WHERE `uname`='$employeeName' and `month`='$selectedMonth'
            </div>
            <p><strong>用途：</strong>获取员工应发工资，计算人工成本</p>
            
            <h4><span class="tag tag-query">社保</span>tuqoa_xmsjbxmx - 项目社保明细表</h4>
            <div class="code-snippet">
-- 项目社保费用统计
SELECT ifnull(sum(sjje),0) as hj 
FROM `tuqoa_xmsjbxmx` 
WHERE `projectid`=$projectId and `ys` like '$selectedMonth%'
            </div>
            <p><strong>用途：</strong>统计员工社保等福利费用</p>
            
            <h4><span class="tag tag-query">核算</span>tuqoa_xmhstjzl - 项目核算统计资料表</h4>
            <div class="code-snippet">
-- 管理费和业务费查询
SELECT * FROM `tuqoa_xmhstjzl` 
WHERE `projectid`=$projectId and `sbrq` like '$selectedMonth%'
            </div>
            <p><strong>用途：</strong>获取预算成本、管理费、业务费等间接成本</p>
            
            <h4><span class="tag tag-query">收费</span>tuqoa_htsf - 合同收费表</h4>
            <div class="code-snippet">
-- 应回收款统计
SELECT ifnull(sum(yjje),0) as yjjehj 
FROM `tuqoa_htsf` 
WHERE projectid=$projectId and `yjsj` like '$selectedMonth%'

-- 实际收款统计
SELECT ifnull(sum(ysje),0) as ysjehj 
FROM `tuqoa_htsf` 
WHERE projectid=$projectId and `sksj` like '$selectedMonth%'
            </div>
            <p><strong>用途：</strong>统计项目收款情况，计算收支平衡</p>
        </div>

        <h2>💰 成本明细计算逻辑</h2>

        <div class="analysis-card feature">
            <h3>详细成本分解体系</h3>
            
            <div class="cost-breakdown">
                <h4>成本计算流程</h4>
                
                <div class="cost-item">
                    <h5><span class="tag tag-calc">基础</span>项目完成情况</h5>
                    <div class="code-snippet">
// 月度完成情况统计
$wcl = 0;  // 完成量
$wccz = 0; // 完成成本

$sql1 = "SELECT * FROM `tuqoa_xmcztjb` 
         where projectid=$projectId and `sbrq` like '$selectedMonth%' 
         order by id desc";

while ($row1 = mysqli_fetch_assoc($result1)) {
    $jhjd = $row1["jhjd"];    // 计划进度
    $sjjd = $row1["sjjd"];    // 实际进度
    $wcl += $row1["wcl"];     // 完成量累加
    $wccz += $row1["wccz"];   // 完成成本累加
}
                    </div>
                </div>
                
                <div class="cost-item">
                    <h5><span class="tag tag-calc">人工</span>人工成本计算</h5>
                    <div class="code-snippet">
// 应发工资合计计算
$应发工资合计 = 0;
$实际成本费用 = 0;

// 获取项目全职人员
$sql1 = "SELECT * FROM `tuqoa_rydp` 
         WHERE `drxmid`=$projectId and `sfqz`='全职' and `state`='在职'";

while ($row1 = mysqli_fetch_assoc($result1)) {
    // 查询每个员工的工资
    $sql2 = "SELECT * FROM `tuqoa_hrsalary` 
             WHERE `uname`='{$row1['dpryxm']}' and `month`='$selectedMonth'";
    
    while ($row2 = mysqli_fetch_assoc($result2)) {
        $应发工资合计 += $row2["yfgz"];  // 应发工资
        $实际成本费用 += $row2["sfgz"];  // 实发工资
    }
}
                    </div>
                </div>
                
                <div class="cost-item">
                    <h5><span class="tag tag-calc">福利</span>社保费用计算</h5>
                    <div class="code-snippet">
// 员工社保等上缴金额合计
$员工社保等上缴金额合计 = 0;

$sql1 = "SELECT ifnull(sum(sjje),0) as hj 
         FROM `tuqoa_xmsjbxmx` 
         WHERE `projectid`=$projectId and `ys` like '$selectedMonth%'";

while ($row1 = mysqli_fetch_assoc($result1)) {
    $员工社保等上缴金额合计 = $row1["hj"];
}

// 总人工成本 = 应发工资 + 社保费用
$总人工成本 = $应发工资合计 + $员工社保等上缴金额合计;
                    </div>
                </div>
                
                <div class="cost-item">
                    <h5><span class="tag tag-calc">管理</span>管理费用计算</h5>
                    <div class="code-snippet">
// 管理费和业务费计算
$预算成本费用 = 0;
$企业管理费 = 0;
$经营业务费 = 0;

$sql1 = "SELECT * FROM `tuqoa_xmhstjzl` 
         WHERE `projectid`=$projectId and `sbrq` like '$selectedMonth%'";

while ($row1 = mysqli_fetch_assoc($result1)) {
    $预算成本费用 = $row1["yszcbfy"];  // 预算成本费用
    $企业管理费 = $row1["glf"];        // 管理费
    $经营业务费 = $row1["ywf"];        // 业务费
}

// 间接费用合计
$间接费用合计 = $企业管理费 + $经营业务费;
                    </div>
                </div>
                
                <div class="cost-item">
                    <h5><span class="tag tag-calc">收支</span>收支平衡计算</h5>
                    <div class="code-snippet">
// 收款情况统计
$应回收款 = 0;
$实际收款 = 0;

// 应回收款
$sql1 = "SELECT ifnull(sum(yjje),0) as yjjehj 
         FROM `tuqoa_htsf` 
         WHERE projectid=$projectId and `yjsj` like '$selectedMonth%'";
$应回收款 = $result["yjjehj"];

// 实际收款
$sql1 = "SELECT ifnull(sum(ysje),0) as ysjehj 
         FROM `tuqoa_htsf` 
         WHERE projectid=$projectId and `sksj` like '$selectedMonth%'";
$实际收款 = $result["ysjehj"];

// 收款完成率
$收款完成率 = ($应回收款 > 0) ? ($实际收款 / $应回收款) * 100 : 0;
                    </div>
                </div>
            </div>
        </div>

        <h2>📊 成本分析指标</h2>

        <div class="analysis-card chart">
            <h3>关键分析指标</h3>
            
            <div class="metric-grid">
                <div class="metric-card">
                    <h4>月度成本</h4>
                    <div class="metric-value">¥X.XX万</div>
                    <p>当月完成成本</p>
                </div>
                <div class="metric-card">
                    <h4>年度成本</h4>
                    <div class="metric-value">¥X.XX万</div>
                    <p>年初至今累计</p>
                </div>
                <div class="metric-card">
                    <h4>累计成本</h4>
                    <div class="metric-value">¥X.XX万</div>
                    <p>项目总成本</p>
                </div>
                <div class="metric-card">
                    <h4>收款完成率</h4>
                    <div class="metric-value">XX%</div>
                    <p>收款执行情况</p>
                </div>
            </div>
            
            <table class="table">
                <thead>
                    <tr>
                        <th>成本类别</th>
                        <th>计算方式</th>
                        <th>数据来源</th>
                        <th>分析意义</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>直接人工成本</td>
                        <td>应发工资合计</td>
                        <td>tuqoa_hrsalary</td>
                        <td>核心人力投入</td>
                    </tr>
                    <tr>
                        <td>人工福利成本</td>
                        <td>社保费用合计</td>
                        <td>tuqoa_xmsjbxmx</td>
                        <td>人力附加成本</td>
                    </tr>
                    <tr>
                        <td>管理费用</td>
                        <td>企业管理费</td>
                        <td>tuqoa_xmhstjzl</td>
                        <td>管理成本分摊</td>
                    </tr>
                    <tr>
                        <td>业务费用</td>
                        <td>经营业务费</td>
                        <td>tuqoa_xmhstjzl</td>
                        <td>业务运营成本</td>
                    </tr>
                    <tr>
                        <td>完成产值</td>
                        <td>完成成本合计</td>
                        <td>tuqoa_xmcztjb</td>
                        <td>项目产出价值</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <h2>📈 成本分析特色</h2>

        <div class="analysis-card business">
            <h3>深度分析功能</h3>
            
            <h4><span class="tag tag-detail">对比</span>多维度成本对比</h4>
            <ul>
                <li><strong>时间维度：</strong>月度 vs 年度 vs 累计成本对比</li>
                <li><strong>预算对比：</strong>实际成本 vs 预算成本对比</li>
                <li><strong>结构分析：</strong>各类成本占比分析</li>
                <li><strong>效率分析：</strong>人均成本、单位产值成本分析</li>
            </ul>
            
            <h4><span class="tag tag-detail">监控</span>成本异常监控</h4>
            <div class="code-snippet">
// 成本异常检测逻辑
function detectCostAnomalies($costData) {
    $anomalies = [];
    
    // 人工成本占比检查
    $laborRatio = ($costData['labor_cost'] / $costData['total_cost']) * 100;
    if ($laborRatio > 70) {
        $anomalies[] = [
            'type' => '人工成本占比过高',
            'value' => $laborRatio . '%',
            'threshold' => '70%',
            'suggestion' => '检查人员配置是否合理'
        ];
    }
    
    // 管理费占比检查
    $mgmtRatio = ($costData['management_fee'] / $costData['total_cost']) * 100;
    if ($mgmtRatio > 15) {
        $anomalies[] = [
            'type' => '管理费占比过高',
            'value' => $mgmtRatio . '%',
            'threshold' => '15%',
            'suggestion' => '优化管理费用分摊'
        ];
    }
    
    // 收支平衡检查
    if ($costData['cost_income_ratio'] > 0.9) {
        $anomalies[] = [
            'type' => '成本收入比过高',
            'value' => ($costData['cost_income_ratio'] * 100) . '%',
            'threshold' => '90%',
            'suggestion' => '项目盈利能力较低，需要成本控制'
        ];
    }
    
    return $anomalies;
}
            </div>
            
            <h4><span class="tag tag-summary">报告</span>成本分析报告</h4>
            <ul>
                <li><strong>成本构成分析：</strong>各项成本的详细分解和占比</li>
                <li><strong>趋势分析：</strong>成本变化趋势和异常识别</li>
                <li><strong>效率分析：</strong>投入产出比和人员效率</li>
                <li><strong>建议措施：</strong>基于分析结果的优化建议</li>
            </ul>
        </div>

        <h2>🎯 技术实现特点</h2>

        <div class="analysis-card">
            <h3>代码特色分析</h3>
            
            <h4>查询优化特点</h4>
            <ul>
                <li><strong>单项目聚焦：</strong>针对特定项目的深度分析</li>
                <li><strong>多表关联：</strong>6个核心表的复杂关联查询</li>
                <li><strong>时间筛选：</strong>灵活的月度时间维度筛选</li>
                <li><strong>聚合计算：</strong>多层次的成本汇总计算</li>
            </ul>
            
            <h4>数据处理特色</h4>
            <ul>
                <li><strong>中文变量：</strong>使用中文变量名增强可读性</li>
                <li><strong>逐步累加：</strong>通过循环逐步累加各项成本</li>
                <li><strong>空值处理：</strong>使用IFNULL确保计算准确性</li>
                <li><strong>实时计算：</strong>页面加载时实时计算所有指标</li>
            </ul>
            
            <h4>用户体验特色</h4>
            <ul>
                <li><strong>详细展示：</strong>完整的成本明细展示</li>
                <li><strong>对比分析：</strong>多维度数据对比</li>
                <li><strong>清晰布局：</strong>结构化的信息展示</li>
                <li><strong>数据准确：</strong>精确到小数点的计算</li>
            </ul>
            
            <div class="warning-box">
                <strong>⚠️ 技术注意事项：</strong>
                <ul>
                    <li>嵌套查询较多，可能影响页面加载速度</li>
                    <li>中文变量名可能在某些环境下有兼容性问题</li>
                    <li>建议添加数据缓存机制提高性能</li>
                    <li>需要确保所有相关表的数据完整性</li>
                </ul>
            </div>
        </div>

        <h2>🚀 优化建议</h2>

        <div class="analysis-card">
            <h3>性能优化</h3>
            <ul>
                <li>优化嵌套查询，使用JOIN减少查询次数</li>
                <li>为时间字段（sbrq、yjsj、sksj）添加索引</li>
                <li>实现成本数据的预计算和缓存</li>
                <li>添加查询结果缓存机制</li>
            </ul>
            
            <h3>功能增强</h3>
            <ul>
                <li>添加成本趋势图表展示</li>
                <li>支持成本数据的导出功能</li>
                <li>增加成本预警和异常提醒</li>
                <li>添加成本对比分析功能</li>
            </ul>
            
            <h3>用户体验</h3>
            <ul>
                <li>添加数据加载进度指示</li>
                <li>支持成本数据的打印格式</li>
                <li>增加成本异常的高亮显示</li>
                <li>添加成本分析的帮助说明</li>
            </ul>
        </div>

        <div style="text-align: center; margin-top: 40px; color: #7f8c8d;">
            <p>📅 分析日期：2025年8月7日</p>
            <p>📊 页面重要性：⭐⭐⭐⭐ (项目成本核心)</p>
            <p>🔄 建议更新频率：月度</p>
        </div>
    </div>
</body>
</html>
