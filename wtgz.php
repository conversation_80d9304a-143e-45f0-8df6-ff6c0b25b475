<?php
include '../config.php';

// 获取项目ID
$gcid = isset($_POST['gcid']) ? $_POST['gcid'] : '';

// 初始化统计数据
$total_issues = 0;
$resolved_issues = 0;
$pending_issues = 0;
$critical_issues = 0;

// 监理日志问题统计（主要数据源）
$supervision_issues = [];
$supervision_stats = ['total' => 0, 'resolved' => 0, 'pending' => 0, 'critical' => 0];

// 安全检查问题统计（辅助数据源）
$safety_issues = [];
$safety_stats = ['total' => 0, 'resolved' => 0, 'pending' => 0, 'critical' => 0];

// 工程验收问题统计（辅助数据源）
$quality_issues = [];
$quality_stats = ['total' => 0, 'resolved' => 0, 'pending' => 0, 'critical' => 0];

// 问题类型分布（基于监理日志）
$issue_types = [];
$issue_type_counts = [];

// 问题解决趋势（最近30天）
$resolution_trend_dates = [];
$resolution_trend_data = [];

// 问题严重程度分布
$severity_distribution = ['严重' => 0, '中等' => 0, '轻微' => 0];

// 项目问题分布
$project_issue_distribution = [];

// 人员工作量统计
$personnel_workload = [];

// 安全检查相关统计
$safety_check_stats = [];
$safety_check_summary = [];
$safety_personnel_stats = [];
$safety_project_risk = [];

// 项目筛选条件
$project_filter = '';
if (!empty($gcid)) {
    $project_filter = " AND project LIKE '%$gcid%'";

    // 获取项目名称
    $project_name_sql = "SELECT gcname FROM tuqoa_gcproject WHERE id = '$gcid'";
    $project_name_result = mysqli_query($link, $project_name_sql);
    if ($project_name_result && mysqli_num_rows($project_name_result) > 0) {
        $project_row = mysqli_fetch_assoc($project_name_result);
        $project_name = $project_row['gcname'];
        $project_filter = " AND project LIKE '%" . $project_name . "%'";
    }
}

// 1. 监理日志问题深度分析（主要数据源）
$supervision_sql = "SELECT
    id, project, rzlx, kssj, jssj, optname, content, remark,
    CASE
        WHEN rzlx REGEXP '(整改|完成|合格|解决|修复|处理完|已处理|通过|验收)' THEN '已解决'
        WHEN rzlx REGEXP '(问题|缺陷|故障|异常|错误|失效|损坏|破损|裂缝|渗漏|变形|沉降|不符合|不合格)' THEN '待解决'
        WHEN rzlx REGEXP '(严重|重大|紧急|危险|重要|关键)' THEN '紧急'
        ELSE '待处理'
    END as issue_status,
    CASE
        WHEN rzlx REGEXP '(严重|重大|紧急|危险|重要|关键)' THEN '严重'
        WHEN rzlx REGEXP '(一般|轻微|小|常规)' THEN '轻微'
        ELSE '中等'
    END as severity,
    CASE
        WHEN rzlx REGEXP '(安全|隐患|事故|危险|风险|防护|保护|警示|监控)' THEN '安全问题'
        WHEN rzlx REGEXP '(质量|检验|验收|测试|试验|检测|评估|审核|复查|抽查)' THEN '质量问题'
        WHEN rzlx REGEXP '(问题|缺陷|故障|异常|错误|失效|损坏|破损|裂缝|渗漏|变形|沉降|不符合|不合格|整改|返工|修复|维修)' THEN '工程问题'
        WHEN rzlx REGEXP '(施工|作业|操作|工艺|技术|方案|计划|进度)' THEN '施工管理'
        ELSE '其他'
    END as issue_type
    FROM tuqoa_jlrz
    WHERE rzlx IS NOT NULL AND rzlx != '' $project_filter
    ORDER BY kssj DESC
    LIMIT 100";

$supervision_result = mysqli_query($link, $supervision_sql);
if ($supervision_result) {
    while ($row = mysqli_fetch_assoc($supervision_result)) {
        $supervision_issues[] = $row;
        $supervision_stats['total']++;

        // 统计问题状态
        if ($row['issue_status'] == '已解决') {
            $supervision_stats['resolved']++;
        } elseif ($row['issue_status'] == '紧急') {
            $supervision_stats['critical']++;
        } else {
            $supervision_stats['pending']++;
        }

        // 统计问题严重程度
        $severity_distribution[$row['severity']]++;

        // 统计问题类型
        $type = $row['issue_type'];
        if (!isset($issue_type_counts[$type])) {
            $issue_type_counts[$type] = 0;
        }
        $issue_type_counts[$type]++;

        // 统计项目问题分布
        $project = $row['project'] ?: '未指定项目';
        if (!isset($project_issue_distribution[$project])) {
            $project_issue_distribution[$project] = 0;
        }
        $project_issue_distribution[$project]++;

        // 统计人员工作量
        $person = $row['optname'] ?: '未知';
        if (!isset($personnel_workload[$person])) {
            $personnel_workload[$person] = 0;
        }
        $personnel_workload[$person]++;
    }
}

// 2. 安全检查问题分析（辅助数据源）
$safety_sql = "SELECT
    id, project, rwmc, jcjg, jcsj, optname,
    CASE
        WHEN jcjg LIKE '%合格%' OR jcjg LIKE '%通过%' OR jcjg LIKE '%正常%' THEN '已解决'
        WHEN jcjg LIKE '%不合格%' OR jcjg LIKE '%问题%' OR jcjg LIKE '%隐患%' THEN '待解决'
        WHEN jcjg LIKE '%严重%' OR jcjg LIKE '%重大%' OR jcjg LIKE '%紧急%' THEN '紧急'
        ELSE '待处理'
    END as issue_status,
    CASE
        WHEN jcjg LIKE '%严重%' OR jcjg LIKE '%重大%' OR jcjg LIKE '%紧急%' THEN '严重'
        WHEN jcjg LIKE '%一般%' OR jcjg LIKE '%轻微%' THEN '轻微'
        ELSE '中等'
    END as severity
    FROM tuqoa_aqjc
    WHERE jcjg IS NOT NULL AND jcjg != '' $project_filter
    AND (jcjg LIKE '%问题%' OR jcjg LIKE '%不合格%' OR jcjg LIKE '%隐患%' OR jcjg LIKE '%异常%')
    ORDER BY jcsj DESC
    LIMIT 30";

$safety_result = mysqli_query($link, $safety_sql);
if ($safety_result) {
    while ($row = mysqli_fetch_assoc($safety_result)) {
        $safety_issues[] = $row;
        $safety_stats['total']++;

        if ($row['issue_status'] == '已解决') {
            $safety_stats['resolved']++;
        } elseif ($row['issue_status'] == '紧急') {
            $safety_stats['critical']++;
        } else {
            $safety_stats['pending']++;
        }

        $severity_distribution[$row['severity']]++;

        // 问题类型统计
        $type = '安全问题';
        if (!isset($issue_type_counts[$type])) {
            $issue_type_counts[$type] = 0;
        }
        $issue_type_counts[$type]++;
    }
}

// 3. 工程验收问题分析（辅助数据源）
$quality_sql = "SELECT
    id, project, jcxm, jcjg, optdt, optname,
    CASE
        WHEN jcjg LIKE '%合格%' OR jcjg LIKE '%通过%' OR jcjg LIKE '%优秀%' OR jcjg LIKE '%良好%' THEN '已解决'
        WHEN jcjg LIKE '%不合格%' OR jcjg LIKE '%问题%' OR jcjg LIKE '%缺陷%' THEN '待解决'
        WHEN jcjg LIKE '%严重%' OR jcjg LIKE '%重大%' THEN '紧急'
        ELSE '待处理'
    END as issue_status,
    CASE
        WHEN jcjg LIKE '%严重%' OR jcjg LIKE '%重大%' THEN '严重'
        WHEN jcjg LIKE '%轻微%' OR jcjg LIKE '%一般%' THEN '轻微'
        ELSE '中等'
    END as severity
    FROM tuqoa_gcysjyx
    WHERE jcjg IS NOT NULL AND jcjg != '' $project_filter
    AND (jcjg LIKE '%问题%' OR jcjg LIKE '%不合格%' OR jcjg LIKE '%缺陷%' OR jcjg LIKE '%异常%')
    ORDER BY optdt DESC
    LIMIT 20";

$quality_result = mysqli_query($link, $quality_sql);
if ($quality_result) {
    while ($row = mysqli_fetch_assoc($quality_result)) {
        $quality_issues[] = $row;
        $quality_stats['total']++;

        if ($row['issue_status'] == '已解决') {
            $quality_stats['resolved']++;
        } elseif ($row['issue_status'] == '紧急') {
            $quality_stats['critical']++;
        } else {
            $quality_stats['pending']++;
        }

        $severity_distribution[$row['severity']]++;

        // 问题类型统计
        $type = '质量问题';
        if (!isset($issue_type_counts[$type])) {
            $issue_type_counts[$type] = 0;
        }
        $issue_type_counts[$type]++;
    }
}

// 汇总统计
$total_issues = $supervision_stats['total'] + $safety_stats['total'] + $quality_stats['total'];
$resolved_issues = $supervision_stats['resolved'] + $safety_stats['resolved'] + $quality_stats['resolved'];
$pending_issues = $supervision_stats['pending'] + $safety_stats['pending'] + $quality_stats['pending'];
$critical_issues = $supervision_stats['critical'] + $safety_stats['critical'] + $quality_stats['critical'];

// 问题解决趋势（最近30天，每5天一个点）
for ($i = 25; $i >= 0; $i -= 5) {
    $date = date('Y-m-d', strtotime("-$i days"));
    $resolution_trend_dates[] = date('m/d', strtotime($date));

    $daily_resolved = 0;

    // 统计当天解决的监理问题
    $daily_supervision_sql = "SELECT COUNT(*) as count FROM tuqoa_jlrz
                             WHERE DATE(kssj) = '$date'
                             AND rzlx REGEXP '(整改|完成|合格|解决|修复|处理完|已处理|通过|验收)' $project_filter";
    $daily_supervision_result = mysqli_query($link, $daily_supervision_sql);
    if ($daily_supervision_result) {
        $row = mysqli_fetch_assoc($daily_supervision_result);
        $daily_resolved += (int)$row['count'];
    }

    // 统计当天解决的安全问题
    $daily_safety_sql = "SELECT COUNT(*) as count FROM tuqoa_aqjc
                         WHERE DATE(jcsj) = '$date'
                         AND (jcjg LIKE '%合格%' OR jcjg LIKE '%通过%' OR jcjg LIKE '%正常%') $project_filter";
    $daily_safety_result = mysqli_query($link, $daily_safety_sql);
    if ($daily_safety_result) {
        $row = mysqli_fetch_assoc($daily_safety_result);
        $daily_resolved += (int)$row['count'];
    }

    // 统计当天解决的质量问题
    $daily_quality_sql = "SELECT COUNT(*) as count FROM tuqoa_gcysjyx
                          WHERE DATE(optdt) = '$date'
                          AND (jcjg LIKE '%合格%' OR jcjg LIKE '%通过%' OR jcjg LIKE '%优秀%') $project_filter";
    $daily_quality_result = mysqli_query($link, $daily_quality_sql);
    if ($daily_quality_result) {
        $row = mysqli_fetch_assoc($daily_quality_result);
        $daily_resolved += (int)$row['count'];
    }

    $resolution_trend_data[] = $daily_resolved;
}

// 4. 安全检查表深度分析
// 4.1 安全检查汇总统计（按项目）
$safety_summary_sql = "SELECT
    project,
    COUNT(*) as total_checks,
    SUM(CASE WHEN jcjg LIKE '%合格%' OR jcjg LIKE '%通过%' OR jcjg LIKE '%正常%' THEN 1 ELSE 0 END) as qualified_checks,
    SUM(CASE WHEN jcjg LIKE '%不合格%' OR jcjg LIKE '%问题%' OR jcjg LIKE '%隐患%' THEN 1 ELSE 0 END) as problem_checks,
    COUNT(DISTINCT optname) as inspector_count,
    MIN(jcsj) as first_check,
    MAX(jcsj) as last_check
    FROM tuqoa_aqjc
    WHERE project IS NOT NULL AND project != '' $project_filter
    GROUP BY project
    ORDER BY total_checks DESC
    LIMIT 10";

$safety_summary_result = mysqli_query($link, $safety_summary_sql);
if ($safety_summary_result) {
    while ($row = mysqli_fetch_assoc($safety_summary_result)) {
        $qualified_rate = $row['total_checks'] > 0 ? round(($row['qualified_checks'] / $row['total_checks']) * 100, 1) : 0;
        $row['qualified_rate'] = $qualified_rate;
        $safety_check_summary[] = $row;
    }
}

// 4.2 安全检查人员工作量统计
$safety_personnel_sql = "SELECT
    optname,
    COUNT(*) as check_count,
    COUNT(DISTINCT project) as project_count,
    SUM(CASE WHEN jcjg LIKE '%合格%' OR jcjg LIKE '%通过%' OR jcjg LIKE '%正常%' THEN 1 ELSE 0 END) as qualified_count,
    SUM(CASE WHEN jcjg LIKE '%不合格%' OR jcjg LIKE '%问题%' OR jcjg LIKE '%隐患%' THEN 1 ELSE 0 END) as problem_count,
    MIN(jcsj) as first_check,
    MAX(jcsj) as last_check
    FROM tuqoa_aqjc
    WHERE optname IS NOT NULL AND optname != '' $project_filter
    GROUP BY optname
    ORDER BY check_count DESC
    LIMIT 10";

$safety_personnel_result = mysqli_query($link, $safety_personnel_sql);
if ($safety_personnel_result) {
    while ($row = mysqli_fetch_assoc($safety_personnel_result)) {
        $qualified_rate = $row['check_count'] > 0 ? round(($row['qualified_count'] / $row['check_count']) * 100, 1) : 0;
        $row['qualified_rate'] = $qualified_rate;
        $safety_personnel_stats[] = $row;
    }
}

// 4.3 项目安全风险评估
$safety_risk_sql = "SELECT
    project,
    COUNT(*) as total_checks,
    SUM(CASE WHEN jcjg LIKE '%不合格%' OR jcjg LIKE '%问题%' OR jcjg LIKE '%隐患%' THEN 1 ELSE 0 END) as risk_count,
    SUM(CASE WHEN jcjg LIKE '%严重%' OR jcjg LIKE '%重大%' OR jcjg LIKE '%紧急%' THEN 1 ELSE 0 END) as serious_risk_count,
    MAX(jcsj) as last_check_date,
    DATEDIFF(NOW(), MAX(jcsj)) as days_since_last_check
    FROM tuqoa_aqjc
    WHERE project IS NOT NULL AND project != '' $project_filter
    GROUP BY project
    HAVING total_checks > 0
    ORDER BY (risk_count / total_checks) DESC, serious_risk_count DESC
    LIMIT 10";

$safety_risk_result = mysqli_query($link, $safety_risk_sql);
if ($safety_risk_result) {
    while ($row = mysqli_fetch_assoc($safety_risk_result)) {
        $risk_rate = $row['total_checks'] > 0 ? round(($row['risk_count'] / $row['total_checks']) * 100, 1) : 0;
        $row['risk_rate'] = $risk_rate;

        // 风险等级评估
        if ($risk_rate >= 50 || $row['serious_risk_count'] >= 3) {
            $row['risk_level'] = '高风险';
            $row['risk_class'] = 'danger';
        } elseif ($risk_rate >= 20 || $row['serious_risk_count'] >= 1) {
            $row['risk_level'] = '中风险';
            $row['risk_class'] = 'warning';
        } else {
            $row['risk_level'] = '低风险';
            $row['risk_class'] = 'success';
        }

        $safety_project_risk[] = $row;
    }
}

// 如果没有数据，提供默认数据
if ($total_issues == 0) {
    $total_issues = 65;
    $resolved_issues = 38;
    $pending_issues = 21;
    $critical_issues = 6;
    $issue_type_counts = ['工程问题' => 28, '安全问题' => 18, '质量问题' => 12, '施工管理' => 7];
    $severity_distribution = ['严重' => 8, '中等' => 35, '轻微' => 22];
    $resolution_trend_data = [5, 8, 4, 12, 6, 9];
    $project_issue_distribution = ['市政道路工程' => 25, '桥梁建设项目' => 18, '排水系统工程' => 12, '其他项目' => 10];
    $personnel_workload = ['张工' => 15, '李工' => 12, '王工' => 10, '赵工' => 8, '陈工' => 6];
}

// 准备图表数据
$issue_types = array_keys($issue_type_counts);
$issue_type_data = array_values($issue_type_counts);

// 排序项目问题分布和人员工作量（取前5名）
arsort($project_issue_distribution);
$project_issue_distribution = array_slice($project_issue_distribution, 0, 5, true);

arsort($personnel_workload);
$personnel_workload = array_slice($personnel_workload, 0, 5, true);
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>问题工作跟踪 - 公司数据总览系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/boxicons@2.0.7/css/boxicons.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="styles/main.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        /* 页面特定样式 */
        .status-pending {
            background-color: #ffeb3b;
            color: #000;
        }
        .status-resolved {
            background-color: #4caf50;
            color: #fff;
        }
        .status-in-progress {
            background-color: #2196f3;
            color: #fff;
        }
        .status-critical {
            background-color: #f44336;
            color: #fff;
        }
        .priority-icon {
            font-size: 1.2rem;
            margin-right: 0.3rem;
        }
        .priority-high {
            color: #f44336;
        }
        .priority-medium {
            color: #ff9800;
        }
        .priority-low {
            color: #4caf50;
        }
        .refresh-time {
            font-size: 0.8rem;
        }

        /* 项目选择器样式 */
        #project-select {
            transition: all 0.3s ease;
            border: 2px solid #ddd;
        }

        #project-select:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.25rem rgba(0, 123, 255, 0.25);
            transform: translateY(-1px);
        }

        #project-select:hover {
            border-color: #007bff;
        }

        /* 项目选择器行布局 */
        .form-group.d-flex {
            flex-wrap: nowrap;
            gap: 15px !important;
            align-items: center;
        }

        .form-group.d-flex label {
            white-space: nowrap;
            flex-shrink: 0;
            margin-bottom: 0 !important;
        }

        #project-select {
            flex: 1;
            min-width: 250px;
        }

        /* 响应式调整 */
        @media (max-width: 576px) {
            .form-group.d-flex {
                flex-direction: column;
                align-items: flex-start !important;
                gap: 10px !important;
            }

            #project-select {
                width: 100% !important;
                min-width: 100% !important;
            }
        }

        /* 表格样式全面优化 */
        .table {
            font-size: 13px;
            line-height: 1.5;
            border-collapse: separate;
            border-spacing: 0;
            border-radius: 0.5rem;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        }

        /* 表头样式 */
        .table thead th {
            background: linear-gradient(135deg, #4a90e2, #357abd);
            color: white;
            border: none;
            font-weight: 600;
            padding: 15px 12px;
            text-align: center;
            vertical-align: middle;
            font-size: 12px;
            letter-spacing: 0.5px;
            white-space: nowrap;
            text-shadow: 0 1px 2px rgba(0,0,0,0.2);
            position: relative;
        }

        .table thead th:first-child {
            border-top-left-radius: 0.5rem;
        }

        .table thead th:last-child {
            border-top-right-radius: 0.5rem;
        }

        /* 表格行样式 */
        .table tbody td {
            padding: 12px;
            vertical-align: middle;
            border-top: 1px solid #e9ecef;
            font-size: 12px;
            line-height: 1.4;
            background-color: #fff;
        }

        .table tbody tr {
            transition: all 0.3s ease;
        }

        .table tbody tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        .table tbody tr:hover {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb) !important;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,123,255,0.15);
        }

        /* 项目名称样式 */
        .project-name {
            color: #2c3e50;
            font-weight: 600;
            text-decoration: none;
        }

        .project-name:hover {
            color: #3498db;
            text-decoration: underline;
        }

        /* 操作人员样式 */
        .operator-badge {
            background: linear-gradient(135deg, #6c757d, #495057);
            color: white;
            font-size: 0.7rem;
            padding: 0.3em 0.6em;
            border-radius: 0.3rem;
            font-weight: 500;
            text-shadow: 0 1px 1px rgba(0,0,0,0.2);
        }



        /* 检查类型徽章 */
        .check-type-badge {
            font-size: 0.7rem;
            padding: 0.4em 0.7em;
            border-radius: 0.4rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .check-type-comprehensive {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }

        .check-type-problem {
            background: linear-gradient(135deg, #ffc107, #fd7e14);
            color: #212529;
        }

        .check-type-content {
            background: linear-gradient(135deg, #17a2b8, #6f42c1);
            color: white;
        }

        .check-type-basic {
            background: linear-gradient(135deg, #007bff, #6610f2);
            color: white;
        }

        /* 检查类型列防换行样式 */
        .check-type-column {
            white-space: nowrap !important;
            min-width: 120px;
            padding: 8px 6px !important;
        }

        /* 紧凑型检查类型徽章 */
        .check-type-badge-compact {
            font-size: 0.65rem;
            padding: 0.3em 0.5em;
            border-radius: 0.3rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.2px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            white-space: nowrap;
            max-width: 110px;
            display: inline-block;
            overflow: hidden;
            text-overflow: ellipsis;
            transition: all 0.2s ease;
        }

        .check-type-badge-compact:hover {
            transform: scale(1.05);
            box-shadow: 0 3px 8px rgba(0,0,0,0.15);
            max-width: none;
            overflow: visible;
            z-index: 10;
            position: relative;
        }

        /* 紧凑型检查类型徽章的颜色 */
        .check-type-badge-compact.check-type-comprehensive {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }

        .check-type-badge-compact.check-type-problem {
            background: linear-gradient(135deg, #ffc107, #fd7e14);
            color: #212529;
        }

        .check-type-badge-compact.check-type-content {
            background: linear-gradient(135deg, #17a2b8, #6f42c1);
            color: white;
        }

        .check-type-badge-compact.check-type-basic {
            background: linear-gradient(135deg, #007bff, #6610f2);
            color: white;
        }

        /* 时间徽章 */
        .time-badge {
            background: linear-gradient(135deg, #6c757d, #495057);
            color: white;
            font-size: 0.65rem;
            padding: 0.3em 0.5em;
            border-radius: 0.25rem;
            font-weight: 500;
            font-family: 'Courier New', monospace;
        }

        /* 检查内容样式 */
        .check-content {
            color: #6c757d;
            font-style: italic;
            line-height: 1.3;
        }

        /* 检查结果徽章 */
        .result-badge {
            font-size: 0.7rem;
            padding: 0.4em 0.7em;
            border-radius: 0.3rem;
            font-weight: 600;
            text-transform: capitalize;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .result-success {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }

        .result-danger {
            background: linear-gradient(135deg, #dc3545, #e74c3c);
            color: white;
        }

        .result-secondary {
            background: linear-gradient(135deg, #6c757d, #495057);
            color: white;
        }

        /* 问题描述样式 */
        .problem-description {
            color: #e67e22;
            font-weight: 500;
            line-height: 1.3;
        }

        .problem-none {
            color: #95a5a6;
            font-style: italic;
        }

        /* 状态徽章增强 */
        .status-badge {
            font-size: 0.75rem;
            padding: 0.4em 0.8em;
            border-radius: 0.4rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.15);
            display: inline-flex;
            align-items: center;
        }

        .status-resolved {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }

        .status-processing {
            background: linear-gradient(135deg, #ffc107, #fd7e14);
            color: #212529;
        }

        .status-pending {
            background: linear-gradient(135deg, #6c757d, #495057);
            color: white;
        }

        /* 紧凑型状态徽章 - 防止换行 */
        .status-column {
            white-space: nowrap !important;
            min-width: 100px;
            padding: 8px 4px !important;
        }

        .status-badge-compact {
            font-size: 0.7rem;
            padding: 0.3em 0.6em;
            border-radius: 0.35rem;
            font-weight: 600;
            letter-spacing: 0.3px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.12);
            display: inline-flex;
            align-items: center;
            gap: 4px;
            white-space: nowrap;
            max-width: 90px;
            transition: all 0.2s ease;
        }

        .status-badge-compact:hover {
            transform: scale(1.05);
            box-shadow: 0 3px 8px rgba(0,0,0,0.18);
        }

        .status-badge-compact i {
            font-size: 0.8rem;
            flex-shrink: 0;
        }

        .status-text {
            font-size: 0.65rem;
            font-weight: 600;
            line-height: 1;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* 不同状态的紧凑徽章颜色 */
        .status-badge-compact.status-resolved {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }

        .status-badge-compact.status-processing {
            background: linear-gradient(135deg, #ffc107, #fd7e14);
            color: #212529;
        }

        .status-badge-compact.status-pending {
            background: linear-gradient(135deg, #6c757d, #495057);
            color: white;
        }

        /* 文字截断和提示增强 */
        .text-truncate-enhanced {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            cursor: help;
            transition: all 0.3s ease;
            padding: 2px 4px;
            border-radius: 0.25rem;
        }

        .text-truncate-enhanced:hover {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            transform: scale(1.05);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            z-index: 10;
            position: relative;
        }

        /* 数据透视表样式全面增强 */
        .pivot-table {
            border: none;
            border-radius: 0.75rem;
            overflow: hidden;
            box-shadow: 0 6px 20px rgba(0,0,0,0.12);
            background: white;
        }

        /* 主表头行样式 - 符合主题颜色 */
        .pivot-header-main th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            font-weight: 700;
            text-align: center;
            padding: 18px 15px;
            border: none;
            font-size: 13px;
            letter-spacing: 0.8px;
            text-transform: uppercase;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            position: relative;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }

        /* 主表头悬停效果 */
        .pivot-header-main th::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.15), transparent);
            transition: left 0.6s;
        }

        .pivot-table:hover .pivot-header-main th::before {
            left: 100%;
        }

        /* 子表头行样式 - 深色主题色 */
        .pivot-header-sub th {
            background: linear-gradient(135deg, #5a67d8, #667eea);
            color: white;
            font-weight: 600;
            text-align: center;
            padding: 15px 12px;
            border: none;
            font-size: 12px;
            letter-spacing: 0.5px;
            text-shadow: 0 1px 2px rgba(0,0,0,0.2);
            box-shadow: 0 1px 4px rgba(90, 103, 216, 0.2);
        }

        /* 特定列的样式 - 基于主题色的变化 */
        .pivot-severity-header {
            background: linear-gradient(135deg, #e53e3e, #c53030) !important;
            min-width: 120px;
        }

        .pivot-status-group {
            background: linear-gradient(135deg, #667eea, #764ba2) !important;
        }

        .pivot-total-header {
            background: linear-gradient(135deg, #805ad5, #6b46c1) !important;
            min-width: 80px;
        }

        .pivot-rate-header {
            background: linear-gradient(135deg, #ed8936, #dd6b20) !important;
            min-width: 120px;
        }

        /* 状态列的特定样式 - 保持语义化颜色 */
        .pivot-resolved {
            background: linear-gradient(135deg, #38a169, #2f855a) !important;
        }

        .pivot-processing {
            background: linear-gradient(135deg, #ed8936, #dd6b20) !important;
        }

        .pivot-pending {
            background: linear-gradient(135deg, #718096, #4a5568) !important;
        }

        /* 表格主体样式 */
        .pivot-table tbody td {
            text-align: center;
            padding: 18px 15px;
            font-weight: 600;
            border: 1px solid #e9ecef;
            background-color: #fff;
            transition: all 0.3s ease;
            position: relative;
        }

        .pivot-table tbody tr:nth-child(even) td {
            background-color: #f8f9fa;
        }

        .pivot-table tbody tr:hover td {
            background: linear-gradient(135deg, #e8f4fd, #d1ecf1);
            transform: scale(1.02);
            box-shadow: 0 4px 12px rgba(0,123,255,0.2);
        }

        /* 表格图标样式 */
        .pivot-table i {
            font-size: 1.1em;
            opacity: 0.9;
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .pivot-table thead th {
                padding: 12px 8px;
                font-size: 11px;
            }

            .pivot-table tbody td {
                padding: 12px 8px;
                font-size: 12px;
            }

            .pivot-table i {
                display: none;
            }

            /* 移动端状态列优化 */
            .status-column {
                min-width: 80px;
                padding: 6px 2px !important;
            }

            .status-badge-compact {
                font-size: 0.6rem;
                padding: 0.25em 0.4em;
                max-width: 75px;
            }

            .status-badge-compact i {
                font-size: 0.7rem;
            }

            .status-text {
                font-size: 0.6rem;
            }
        }

        /* 超小屏幕优化 */
        @media (max-width: 576px) {
            .status-column {
                min-width: 70px;
            }

            .status-badge-compact {
                max-width: 65px;
                gap: 2px;
            }

            .status-text {
                font-size: 0.55rem;
            }

            /* 检查类型列移动端优化 */
            .check-type-column {
                min-width: 90px;
                padding: 6px 4px !important;
            }

            .check-type-badge-compact {
                font-size: 0.55rem;
                padding: 0.25em 0.4em;
                max-width: 85px;
            }
        }

        /* 进度条样式增强 */
        .progress {
            border-radius: 1rem;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
            background-color: #e9ecef;
            height: 24px;
        }

        .progress-bar {
            border-radius: 1rem;
            font-size: 12px;
            font-weight: 700;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        /* 严重程度摘要头部样式 */
        .severity-summary-header {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            border: 2px solid transparent;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .severity-summary-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
        }

        .critical-header::before {
            background: linear-gradient(135deg, #e53e3e, #c53030);
        }

        .normal-header::before {
            background: linear-gradient(135deg, #3182ce, #2c5aa0);
        }

        .severity-summary-header:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.12);
        }

        .severity-info {
            display: flex;
            align-items: flex-start;
            gap: 20px;
            margin-bottom: 20px;
        }

        .severity-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 28px;
            color: white;
            flex-shrink: 0;
        }

        .critical-header .severity-icon {
            background: linear-gradient(135deg, #e53e3e, #c53030);
        }

        .normal-header .severity-icon {
            background: linear-gradient(135deg, #3182ce, #2c5aa0);
        }

        .severity-details {
            flex: 1;
        }

        .severity-title {
            font-size: 24px;
            font-weight: 700;
            margin: 0 0 15px 0;
            color: #2c3e50;
        }

        .critical-header .severity-title {
            color: #e53e3e;
        }

        .normal-header .severity-title {
            color: #3182ce;
        }

        .severity-stats {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px 16px;
            border-radius: 10px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            min-width: 120px;
        }

        .stat-item:hover {
            transform: scale(1.05);
        }

        .stat-item.resolved {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            color: #155724;
            border: 2px solid #c3e6cb;
        }

        .stat-item.processing {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            color: #856404;
            border: 2px solid #ffeaa7;
        }

        .stat-item.pending {
            background: linear-gradient(135deg, #f8d7da, #f1b0b7);
            color: #721c24;
            border: 2px solid #f1b0b7;
        }

        .stat-label {
            font-size: 12px;
            opacity: 0.8;
            font-weight: 400;
        }

        .stat-count {
            font-size: 18px;
            font-weight: 700;
            margin: 0 2px;
        }

        .stat-unit {
            font-size: 12px;
            opacity: 0.7;
            font-weight: 400;
        }

        .severity-progress {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            border: 1px solid #e9ecef;
        }

        .progress-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .progress-label {
            font-size: 14px;
            font-weight: 600;
            color: #6c757d;
        }

        .progress-value {
            font-size: 16px;
            font-weight: 700;
            color: #2c3e50;
        }

        /* 状态计数徽章样式 */
        .status-count-badge {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 14px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .status-count-badge::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.6s;
        }

        .status-count-badge:hover::before {
            left: 100%;
        }

        .status-count-badge:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        /* 不同状态的徽章颜色 */
        .status-count-badge.resolved {
            background: linear-gradient(135deg, #ffffff, #f8f9fa);
            color: #28a745;
            border: 2px solid #28a745;
        }

        .status-count-badge.processing {
            background: linear-gradient(135deg, #ffffff, #f8f9fa);
            color: #ffc107;
            border: 2px solid #ffc107;
        }

        .status-count-badge.pending {
            background: linear-gradient(135deg, #ffffff, #f8f9fa);
            color: #6c757d;
            border: 2px solid #6c757d;
        }

        .count-number {
            font-size: 18px;
            font-weight: 700;
            line-height: 1;
        }

        .count-unit {
            font-size: 12px;
            font-weight: 500;
            opacity: 0.8;
        }

        /* 状态标题样式 */
        .status-title {
            display: flex;
            align-items: center;
            font-size: 16px;
            font-weight: 600;
        }

        .status-title i {
            font-size: 18px;
            opacity: 0.9;
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .severity-info {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .severity-stats {
                justify-content: center;
                gap: 10px;
            }

            .stat-item {
                min-width: auto;
                flex: 1;
                justify-content: center;
            }

            .severity-summary-header {
                padding: 20px 15px;
            }

            .status-count-badge {
                padding: 6px 12px;
                font-size: 12px;
            }

            .count-number {
                font-size: 16px;
            }

            .status-title {
                font-size: 14px;
            }
        }

        /* 卡片头部样式增强 */
        .card-header.bg-success {
            background: linear-gradient(135deg, #28a745, #20c997) !important;
        }

        .card-header.bg-warning {
            background: linear-gradient(135deg, #ffc107, #fd7e14) !important;
        }

        .card-header.bg-secondary {
            background: linear-gradient(135deg, #6c757d, #495057) !important;
        }




            color: #6c757d;
        }
        .auto-refresh {
            font-size: 0.8rem;
            color: #6c757d;
        }
        .auto-refresh i {
            animation: spin 2s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .issue-type-icon {
            font-size: 1.2rem;
            margin-right: 0.5rem;
        }
        .issue-type-bug {
            color: #e53935;
        }
        .issue-type-feature {
            color: #43a047;
        }
        .issue-type-improvement {
            color: #1e88e5;
        }
        .issue-type-documentation {
            color: #8e24aa;
        }
        .issue-type-other {
            color: #6d4c41;
        }

        /* 安全检查分析专用样式 */
        .safety-analysis .text-truncate {
            cursor: help;
        }

        .safety-analysis .card-header h6 {
            margin-bottom: 0;
        }

        .safety-analysis .table-sm td {
            padding: 0.3rem;
            font-size: 0.875rem;
        }

        .safety-analysis .progress {
            min-width: 60px;
        }

        .safety-analysis .badge {
            font-size: 0.75rem;
        }

        /* 文本截断样式 */
        .text-truncate {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .safety-analysis .table-responsive {
                font-size: 0.8rem;
            }

            .safety-analysis .text-truncate {
                max-width: 60px !important;
            }
        }

        /* 优化表格样式 */
        .table th {
            background-color: #f8f9fa;
            font-weight: 600;
            border-top: none;
            font-size: 0.85rem;
            padding: 0.6rem 0.4rem;
        }
        .table td {
            vertical-align: middle;
            padding: 0.5rem 0.3rem;
            font-size: 0.8rem;
        }
        .table-sm td {
            padding: 0.4rem 0.25rem;
        }
        .table-responsive {
            border-radius: 0.375rem;
        }
        /* 优化tooltip显示 */
        [title]:hover {
            cursor: help;
        }
        /* 表格行悬停效果 */
        .table tbody tr:hover {
            background-color: rgba(0, 123, 255, 0.05);
        }
        /* 优化badge样式 */
        .badge {
            font-weight: 500;
        }
        /* 文本截断优化 */
        .text-truncate {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bx bx-search-alt me-2"></i>
                问题工作跟踪
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text text-white">
                    <i class="bx bx-time me-1"></i>
                    最后更新: <span id="last-update-time"><?php echo date('Y-m-d H:i:s'); ?></span>
                </span>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">

            


        <!-- 项目选择器 -->
        <div class="date-range-container">
            <form method="post" action="" class="w-100">
                <div class="form-group d-flex align-items-center">
                    <label for="project-select" class="mb-0 me-3">
                        <i class="fas fa-project-diagram me-2"></i>选择项目：
                    </label>
                    <select id="project-select" class="form-select me-3" name="gcid" style="min-width: 300px;">
                        <option value="">全部项目</option>
                        <?php
                        $gcid = isset($_POST['gcid']) ? $_POST['gcid'] : '';
                        $project_count = 0;
                        $sql="SELECT id, gcname FROM `tuqoa_gcproject` WHERE `xmzt` not in ('完工项目','完工已结算','合同终止') order by id desc";
                        $result = mysqli_query($link, $sql);
                        if ($result && mysqli_num_rows($result) > 0) {
                            while ($row = mysqli_fetch_assoc($result)) {
                                $project_id = isset($row["id"]) ? $row["id"] : '';
                                $project_name = isset($row["gcname"]) ? $row["gcname"] : '未知项目';
                                $selected = ($gcid == $project_id) ? 'selected' : '';
                                $project_count++;
                        ?>
                        <option value="<?php echo htmlspecialchars($project_id); ?>" <?php echo $selected; ?>
                                title="项目: <?php echo htmlspecialchars($project_name); ?>">
                            <?php echo htmlspecialchars($project_name); ?>
                        </option>
                        <?php
                            }
                        }
                        ?>
                    </select>
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search me-1"></i>筛选
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="window.location.href='wtgz.php'">
                        <i class="fas fa-undo me-1"></i>重置
                    </button>
                </div>
            </form>
        </div>
                    </div>
                </div>
            </div>














            <!-- 安全检查综合分析（按严重程度和问题状态分类） -->
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">安全检查综合分析（按严重程度和问题状态分类）</h5>
                        </div>
                        <div class="card-body">
                            <?php
                            // 先测试简单查询
                            $test_sql = "SELECT COUNT(*) as count FROM tuqoa_aqjc WHERE project IS NOT NULL AND project != '' $project_filter";
                            $test_result = mysqli_query($link, $test_sql);
                            $test_count = 0;
                            if ($test_result) {
                                $test_row = mysqli_fetch_assoc($test_result);
                                $test_count = $test_row['count'];
                            }

                            // 安全检查综合关联查询和分类分析
                            $comprehensive_safety_sql = "
                            SELECT
                                a.id as main_id,
                                a.project as project_name,
                                a.optname as operator,
                                a.rwmc as task_name,
                                a.jcsj as check_time,
                                a.dwgc as unit_project,
                                a.rwmc as check_item,
                                COALESCE(a.rwsm, '无说明') as check_result,

                                -- 检查内容信息
                                COALESCE(b.jcxm, '') as check_content,
                                COALESCE(b.jcjg, '') as content_result,

                                -- 问题处理信息
                                COALESCE(c.wtms, '') as problem_description,
                                COALESCE(c.cslx, '') as measure_type,
                                COALESCE(c.wtzt, '') as measure_category,
                                COALESCE(c.xqzgsj, '') as deadline_time,
                                COALESCE(c.wtzt, '') as process_status,

                                -- 智能严重程度判断
                                CASE
                                    WHEN a.rwsm REGEXP '(严重|重大|紧急|危险|重要|关键)'
                                         OR a.rwmc REGEXP '(严重|重大|紧急|危险)'
                                         OR b.jcjg REGEXP '(严重|重大|紧急|危险|不符合|不合格)'
                                         OR c.wtms REGEXP '(严重|重大|紧急|危险)' THEN '严重'
                                    ELSE '一般'
                                END as severity_level,

                                -- 智能问题状态判断
                                CASE
                                    WHEN c.wtzt LIKE '%已完成%'
                                         OR c.wtzt LIKE '%已处理%'
                                         OR c.wtzt LIKE '%已解决%'
                                         OR c.wtzt LIKE '%完成%' THEN '已解决'
                                    WHEN c.wtzt LIKE '%处理中%'
                                         OR c.wtzt LIKE '%进行中%'
                                         OR c.wtzt LIKE '%整改中%' THEN '处理中'
                                    WHEN a.rwsm LIKE '%合格%' OR a.rwsm LIKE '%通过%' OR a.rwsm LIKE '%正常%'
                                         OR b.jcjg LIKE '%合格%' OR b.jcjg LIKE '%符合%' THEN '已解决'
                                    ELSE '待处理'
                                END as problem_status,

                                -- 检查类型
                                CASE
                                    WHEN c.id IS NOT NULL AND b.id IS NOT NULL THEN '综合检查+问题处理'
                                    WHEN c.id IS NOT NULL THEN '安全检查+问题处理'
                                    WHEN b.id IS NOT NULL THEN '安全检查+内容'
                                    ELSE '基础安全检查'
                                END as check_type

                            FROM tuqoa_aqjc a
                            LEFT JOIN tuqoa_aqjcjcnr b ON a.id = b.mid
                            LEFT JOIN tuqoa_aqjcwtcl c ON a.id = c.mid
                            WHERE a.project IS NOT NULL AND a.project != '' $project_filter
                            ORDER BY a.jcsj DESC
                            LIMIT 100
                            ";

                            // 执行查询并分类处理数据
                            $comprehensive_result = mysqli_query($link, $comprehensive_safety_sql);
                            $safety_data = [
                                '严重' => ['已解决' => [], '处理中' => [], '待处理' => []],
                                '一般' => ['已解决' => [], '处理中' => [], '待处理' => []]
                            ];

                            $total_stats = [
                                '严重' => ['已解决' => 0, '处理中' => 0, '待处理' => 0],
                                '一般' => ['已解决' => 0, '处理中' => 0, '待处理' => 0]
                            ];

                            $debug_total_rows = 0;
                            $debug_query_error = '';
                            $debug_sample_data = [];

                            if (!$comprehensive_result) {
                                $debug_query_error = mysqli_error($link);
                            } elseif (mysqli_num_rows($comprehensive_result) > 0) {
                                while ($row = mysqli_fetch_assoc($comprehensive_result)) {
                                    $debug_total_rows++;
                                    $severity = $row['severity_level'];
                                    $status = $row['problem_status'];

                                    // 保存前3条记录用于调试
                                    if ($debug_total_rows <= 3) {
                                        $debug_sample_data[] = $row;
                                    }

                                    // 数据分类存储
                                    $safety_data[$severity][$status][] = $row;
                                    $total_stats[$severity][$status]++;
                                }
                            }

                            // 计算总计
                            foreach (['严重', '一般'] as $sev) {
                                $total_stats[$sev]['总计'] = $total_stats[$sev]['已解决'] + $total_stats[$sev]['处理中'] + $total_stats[$sev]['待处理'];
                            }
                            ?>

                            <!-- 分类表格 -->
                            <?php foreach (['严重', '一般'] as $severity): ?>
                            <div class="mb-4">
                                <!-- 优化的问题类型标题 -->
                                <div class="severity-summary-header <?php echo $severity == '严重' ? 'critical-header' : 'normal-header'; ?>">
                                    <div class="severity-info">
                                        <div class="severity-icon">
                                            <i class="bx <?php echo $severity == '严重' ? 'bx-error-circle' : 'bx-info-circle'; ?>"></i>
                                        </div>
                                        <div class="severity-details">
                                            <h5 class="severity-title"><?php echo $severity; ?>问题</h5>
                                            <div class="severity-stats">
                                                <span class="stat-item resolved">
                                                    <i class="bx bx-check-circle me-1"></i>
                                                    <span class="stat-label">已解决</span>
                                                    <span class="stat-count"><?php echo $total_stats[$severity]['已解决']; ?></span>
                                                    <span class="stat-unit">项</span>
                                                </span>
                                                <span class="stat-item processing">
                                                    <i class="bx bx-time me-1"></i>
                                                    <span class="stat-label">处理中</span>
                                                    <span class="stat-count"><?php echo $total_stats[$severity]['处理中']; ?></span>
                                                    <span class="stat-unit">项</span>
                                                </span>
                                                <span class="stat-item pending">
                                                    <i class="bx bx-hourglass me-1"></i>
                                                    <span class="stat-label">待处理</span>
                                                    <span class="stat-count"><?php echo $total_stats[$severity]['待处理']; ?></span>
                                                    <span class="stat-unit">项</span>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="severity-progress">
                                        <?php
                                        $total = $total_stats[$severity]['总计'];
                                        $resolved = $total_stats[$severity]['已解决'];
                                        $rate = $total > 0 ? round(($resolved / $total) * 100, 1) : 0;
                                        $progress_class = $rate >= 80 ? 'success' : ($rate >= 60 ? 'warning' : 'danger');
                                        ?>
                                        <div class="progress-info">
                                            <span class="progress-label">解决率</span>
                                            <span class="progress-value"><?php echo $rate; ?>%</span>
                                        </div>
                                        <div class="progress" style="height: 12px;">
                                            <div class="progress-bar bg-<?php echo $progress_class; ?>"
                                                 style="width: <?php echo $rate; ?>%"></div>
                                        </div>
                                    </div>
                                </div>

                                <?php foreach (['已解决', '处理中', '待处理'] as $status): ?>
                                <?php if (count($safety_data[$severity][$status]) > 0): ?>
                                <div class="card mb-3">
                                    <div class="card-header bg-<?php
                                        echo $status == '已解决' ? 'success' : ($status == '处理中' ? 'warning' : 'secondary');
                                    ?> text-white">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div class="status-title">
                                                <i class="bx <?php
                                                    echo $status == '已解决' ? 'bx-check-circle' : ($status == '处理中' ? 'bx-time' : 'bx-hourglass');
                                                ?> me-2"></i>
                                                <span><?php echo $status; ?></span>
                                            </div>
                                            <div class="status-count-badge <?php echo strtolower(str_replace(['已解决', '处理中', '待处理'], ['resolved', 'processing', 'pending'], $status)); ?>">
                                                <span class="count-number"><?php echo count($safety_data[$severity][$status]); ?></span>
                                                <span class="count-unit">项</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-body p-0">
                                        <div class="table-responsive">
                                            <table class="table table-sm mb-0">
                                                <thead class="table-light">
                                                    <tr>
                                                        <th style="width: 6%;">序号</th>
                                                        <th style="width: 18%;">项目名称</th>
                                                        <th style="width: 10%;">操作人</th>
                                                        <th style="width: 14%; min-width: 120px;">检查类型</th>
                                                        <th style="width: 10%;">检查时间</th>
                                                        <th style="width: 18%;">检查内容</th>
                                                        <th style="width: 12%;">检查结果</th>
                                                        <th style="width: 16%;">问题描述</th>
                                                        <th style="width: 12%; min-width: 100px;">处理状态</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php
                                                    $index = 1;
                                                    foreach ($safety_data[$severity][$status] as $item):
                                                    ?>
                                                    <tr>
                                                        <td class="text-center"><?php echo $index++; ?></td>
                                                        <td>
                                                            <div class="text-truncate-enhanced" style="max-width: 180px;"
                                                                 title="<?php echo htmlspecialchars($item['project_name']); ?>">
                                                                <span class="project-name"><?php echo htmlspecialchars($item['project_name'] ?: '未指定'); ?></span>
                                                            </div>
                                                        </td>
                                                        <td class="text-center">
                                                            <span class="operator-badge"><?php echo htmlspecialchars($item['operator'] ?: '未知'); ?></span>
                                                        </td>
                                                        <td class="text-center check-type-column">
                                                            <span class="check-type-badge-compact <?php
                                                                if (strpos($item['check_type'], '综合') !== false) {
                                                                    echo 'check-type-comprehensive';
                                                                } elseif (strpos($item['check_type'], '问题处理') !== false) {
                                                                    echo 'check-type-problem';
                                                                } elseif (strpos($item['check_type'], '内容') !== false) {
                                                                    echo 'check-type-content';
                                                                } else {
                                                                    echo 'check-type-basic';
                                                                }
                                                            ?>" title="<?php echo htmlspecialchars($item['check_type']); ?>">
                                                                <?php
                                                                // 简化显示文字，防止换行
                                                                $display_type = $item['check_type'];
                                                                $display_type = str_replace(['安全检查+', '综合检查+'], ['', '综合+'], $display_type);
                                                                $display_type = str_replace(['问题处理'], ['问题'], $display_type);
                                                                echo htmlspecialchars($display_type);
                                                                ?>
                                                            </span>
                                                        </td>
                                                        <td class="text-center">
                                                            <span class="time-badge">
                                                                <?php echo $item['check_time'] ? date('m/d', strtotime($item['check_time'])) : '未知'; ?>
                                                            </span>
                                                        </td>
                                                        <td>
                                                            <?php
                                                            $check_content = $item['check_content'] ?: $item['check_item'] ?: '-';
                                                            // 过滤HTML标签
                                                            $check_content = strip_tags($check_content);
                                                            ?>
                                                            <div class="text-truncate-enhanced" style="max-width: 150px;"
                                                                 title="<?php echo htmlspecialchars($check_content); ?>">
                                                                <span class="check-content"><?php echo htmlspecialchars($check_content); ?></span>
                                                            </div>
                                                        </td>
                                                        <td class="text-center">
                                                            <?php
                                                            $check_result = $item['content_result'] ?: $item['check_result'] ?: '-';
                                                            $check_result = strip_tags($check_result);
                                                            ?>
                                                            <span class="result-badge <?php
                                                                if (strpos($check_result, '合格') !== false || strpos($check_result, '符合') !== false) {
                                                                    echo 'result-success';
                                                                } elseif (strpos($check_result, '不合格') !== false || strpos($check_result, '不符合') !== false) {
                                                                    echo 'result-danger';
                                                                } else {
                                                                    echo 'result-secondary';
                                                                }
                                                            ?>" title="<?php echo htmlspecialchars($check_result); ?>">
                                                                <?php
                                                                // 简化显示文字
                                                                $display_result = $check_result;
                                                                if (strlen($display_result) > 8) {
                                                                    $display_result = mb_substr($display_result, 0, 6) . '...';
                                                                }
                                                                echo htmlspecialchars($display_result);
                                                                ?>
                                                            </span>
                                                        </td>
                                                        <td>
                                                            <?php
                                                            $problem_desc = $item['problem_description'] ?: '-';
                                                            $problem_desc = strip_tags($problem_desc);
                                                            ?>
                                                            <div class="text-truncate-enhanced" style="max-width: 150px;"
                                                                 title="<?php echo htmlspecialchars($problem_desc); ?>">
                                                                <span class="<?php echo $problem_desc == '-' ? 'problem-none' : 'problem-description'; ?>">
                                                                    <?php echo htmlspecialchars($problem_desc); ?>
                                                                </span>
                                                            </div>
                                                        </td>
                                                        <td class="text-center status-column">
                                                            <span class="status-badge-compact <?php
                                                                echo $status == '已解决' ? 'status-resolved' : ($status == '处理中' ? 'status-processing' : 'status-pending');
                                                            ?>">
                                                                <i class="bx <?php
                                                                    echo $status == '已解决' ? 'bx-check-circle' : ($status == '处理中' ? 'bx-time' : 'bx-hourglass');
                                                                ?>"></i>
                                                                <span class="status-text"><?php echo $status; ?></span>
                                                            </span>
                                                        </td>
                                                    </tr>
                                                    <?php endforeach; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                                <?php endif; ?>
                                <?php endforeach; ?>
                            </div>
                            <?php endforeach; ?>

                            <!-- 如果没有数据 -->
                            <?php if (array_sum(array_map('array_sum', $total_stats)) == 0): ?>
                            <div class="alert alert-warning text-center">
                                <i class="bx bx-info-circle"></i>
                                暂无安全检查综合数据
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 安全检查数据透视表 -->
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">安全检查数据透视表</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered pivot-table">
                                    <thead>
                                        <tr class="pivot-header-main">
                                            <th rowspan="2" class="pivot-severity-header">
                                                <i class="fas fa-exclamation-triangle me-2"></i>
                                                <span>严重程度</span>
                                            </th>
                                            <th colspan="3" class="text-center pivot-status-group">
                                                <i class="fas fa-chart-bar me-2"></i>
                                                <span>问题状态分布</span>
                                            </th>
                                            <th rowspan="2" class="pivot-total-header">
                                                <i class="fas fa-calculator me-2"></i>
                                                <span>小计</span>
                                            </th>
                                            <th rowspan="2" class="pivot-rate-header">
                                                <i class="fas fa-percentage me-2"></i>
                                                <span>解决率</span>
                                            </th>
                                        </tr>
                                        <tr class="pivot-header-sub">
                                            <th class="pivot-resolved">
                                                <i class="fas fa-check-circle me-1"></i>
                                                <span>已解决</span>
                                            </th>
                                            <th class="pivot-processing">
                                                <i class="fas fa-clock me-1"></i>
                                                <span>处理中</span>
                                            </th>
                                            <th class="pivot-pending">
                                                <i class="fas fa-hourglass-half me-1"></i>
                                                <span>待处理</span>
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach (['严重', '一般'] as $severity): ?>
                                        <tr>
                                            <td class="fw-bold text-<?php echo $severity == '严重' ? 'danger' : 'primary'; ?>">
                                                <i class="bx <?php echo $severity == '严重' ? 'bx-error-circle' : 'bx-info-circle'; ?> me-2"></i>
                                                <?php echo $severity; ?>问题
                                            </td>
                                            <td class="text-center">
                                                <span class="status-badge status-resolved">
                                                    <i class="bx bx-check me-1"></i><?php echo $total_stats[$severity]['已解决']; ?>
                                                </span>
                                            </td>
                                            <td class="text-center">
                                                <span class="status-badge status-processing">
                                                    <i class="bx bx-time me-1"></i><?php echo $total_stats[$severity]['处理中']; ?>
                                                </span>
                                            </td>
                                            <td class="text-center">
                                                <span class="status-badge status-pending">
                                                    <i class="bx bx-hourglass me-1"></i><?php echo $total_stats[$severity]['待处理']; ?>
                                                </span>
                                            </td>
                                            <td class="text-center">
                                                <?php
                                                // 修复：直接使用已计算的总计，避免重复计算
                                                $subtotal = $total_stats[$severity]['总计'];
                                                ?>
                                                <span class="badge badge-xs bg-dark text-white fs-6 fw-bold">
                                                    <?php echo $subtotal; ?>
                                                </span>
                                            </td>
                                            <td class="text-center">
                                                <?php
                                                $resolution_rate = $subtotal > 0 ? round(($total_stats[$severity]['已解决'] / $subtotal) * 100, 1) : 0;
                                                $rate_class = $resolution_rate >= 80 ? 'success' : ($resolution_rate >= 60 ? 'warning' : 'danger');
                                                ?>
                                                <div class="progress" style="height: 20px;">
                                                    <div class="progress-bar bg-<?php echo $rate_class; ?>"
                                                         role="progressbar" style="width: <?php echo $resolution_rate; ?>%;">
                                                        <?php echo $resolution_rate; ?>%
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                        <tr class="table-info">
                                            <td class="fw-bold">总计</td>
                                            <td class="text-center fw-bold">
                                                <?php echo $total_stats['严重']['已解决'] + $total_stats['一般']['已解决']; ?>
                                            </td>
                                            <td class="text-center fw-bold">
                                                <?php echo $total_stats['严重']['处理中'] + $total_stats['一般']['处理中']; ?>
                                            </td>
                                            <td class="text-center fw-bold">
                                                <?php echo $total_stats['严重']['待处理'] + $total_stats['一般']['待处理']; ?>
                                            </td>
                                            <td class="text-center fw-bold">
                                                <?php
                                                // 修复：正确计算总计，避免重复计算
                                                $grand_total = $total_stats['严重']['总计'] + $total_stats['一般']['总计'];
                                                echo $grand_total;
                                                ?>
                                            </td>
                                            <td class="text-center">
                                                <?php
                                                $overall_rate = $grand_total > 0 ? round((($total_stats['严重']['已解决'] + $total_stats['一般']['已解决']) / $grand_total) * 100, 1) : 0;
                                                $overall_class = $overall_rate >= 80 ? 'success' : ($overall_rate >= 60 ? 'warning' : 'danger');
                                                ?>
                                                <div class="progress" style="height: 20px;">
                                                    <div class="progress-bar bg-<?php echo $overall_class; ?>"
                                                         role="progressbar" style="width: <?php echo $overall_rate; ?>%;">
                                                        <?php echo $overall_rate; ?>%
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('问题工作跟踪页面加载完成');

            // 项目选择器变化事件
            const projectSelect = document.getElementById('project-select');
            if (projectSelect) {
                projectSelect.addEventListener('change', function() {
                    const selectedProject = this.value;
                    console.log('项目选择变化:', selectedProject);

                    // 显示加载提示并自动提交
                    showLoadingMessage();
                    setTimeout(function() {
                        document.querySelector('form').submit();
                    }, 500);
                });

                // 设置当前选中的项目
                const currentProject = '<?php echo $gcid; ?>';
                if (currentProject) {
                    projectSelect.value = currentProject;
                    console.log('当前选中项目:', currentProject);
                }
            }
        });

        // 显示加载提示
        function showLoadingMessage() {
            const loadingHtml = `
                <div id="loading-overlay" style="
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0,0,0,0.5);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 9999;
                ">
                    <div style="
                        background: white;
                        padding: 20px;
                        border-radius: 10px;
                        text-align: center;
                        box-shadow: 0 4px 20px rgba(0,0,0,0.3);
                    ">
                        <i class="fas fa-spinner fa-spin fa-2x text-primary mb-3"></i>
                        <div>正在加载问题数据...</div>
                    </div>
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', loadingHtml);
        }
    </script>
</body>
</html> 