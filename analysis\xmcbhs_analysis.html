<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>xmcbhs.php 数据来源分析</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }
        h3 {
            color: #2980b9;
            margin-top: 20px;
        }
        .data-source {
            background: #ecf0f1;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #3498db;
        }
        .static-data {
            background: #fff3cd;
            border-left-color: #ffc107;
        }
        .dynamic-data {
            background: #d1ecf1;
            border-left-color: #17a2b8;
        }
        .table-info {
            background: #d4edda;
            border-left-color: #28a745;
        }
        .chart-info {
            background: #f8d7da;
            border-left-color: #dc3545;
        }
        .code-snippet {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        .tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            margin-right: 5px;
        }
        .tag-static { background: #ffc107; color: #212529; }
        .tag-dynamic { background: #17a2b8; color: white; }
        .tag-table { background: #28a745; color: white; }
        .tag-chart { background: #dc3545; color: white; }
        .tag-form { background: #fd7e14; color: white; }
        .tag-calc { background: #6f42c1; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <h1>xmcbhs.php 页面数据来源分析</h1>
        
        <h2>页面概述</h2>
        <p>xmcbhs.php 是一个项目成本核算页面，用于分析和监控项目的成本控制情况，包括合同总额、收费总额、实际成本等关键财务指标，并提供成本趋势分析和项目明细。</p>
        
        <h2>数据来源分析</h2>
        
        <h3>1. 用户输入数据</h3>
        
        <div class="data-source static-data">
            <h4><span class="tag tag-form">表单</span>项目选择</h4>
            <p><strong>数据内容：</strong>用户选择的具体项目</p>
            <p><strong>来源：</strong>从tuqoa_gcproject表动态生成的下拉选项</p>
            <div class="code-snippet">
SELECT * FROM `tuqoa_gcproject` 
WHERE `xmzt` not in ('完工项目','完工已结算','合同终止') 
order by id desc
            </div>
            <p><strong>说明：</strong>只显示未完工的活跃项目，支持"全部项目"选项</p>
        </div>
        
        <div class="data-source static-data">
            <h4><span class="tag tag-form">表单</span>年份选择</h4>
            <p><strong>数据内容：</strong>用户选择的查询年份</p>
            <p><strong>来源：</strong>PHP动态生成最近5年选项</p>
            <div class="code-snippet">
$currentYear = date('Y');
for ($i = 0; $i < 5; $i++) {
    $year = $currentYear - $i;
    echo '<option value="'.$year.'">'.$year.'年</option>';
}
            </div>
            <p><strong>说明：</strong>默认选择当前年份，提供最近5年的选择</p>
        </div>
        
        <h3>2. 动态数据查询</h3>
        
        <div class="data-source dynamic-data">
            <h4><span class="tag tag-dynamic">动态</span><span class="tag tag-table">tuqoa_gcproject</span>合同总额统计</h4>
            <p><strong>数据内容：</strong>项目合同造价总额</p>
            <p><strong>来源：</strong>tuqoa_gcproject表的zaojia字段</p>
            <div class="code-snippet">
SELECT COALESCE(SUM(zaojia), 0) as htzje 
FROM `tuqoa_gcproject` 
WHERE 1=1 $project_filter $year_filter

// 筛选条件
$project_filter = " AND id = '$gcid'";  // 项目筛选
$year_filter = " AND YEAR(jcsj) = '$selectedYear'";  // 年份筛选
            </div>
            <p><strong>说明：</strong>支持按项目和年份筛选，使用COALESCE处理空值</p>
        </div>
        
        <div class="data-source dynamic-data">
            <h4><span class="tag tag-dynamic">动态</span><span class="tag tag-table">tuqoa_htgl</span>收费总额统计</h4>
            <p><strong>数据内容：</strong>项目服务费总额</p>
            <p><strong>来源：</strong>tuqoa_htgl表的fwf字段</p>
            <div class="code-snippet">
SELECT COALESCE(SUM(fwf), 0) as sfzje 
FROM `tuqoa_htgl` 
WHERE 1=1 $htgl_filter $htgl_year_filter

// 筛选条件
$htgl_filter = " AND projectid = '$gcid'";  // 项目筛选
$htgl_year_filter = " AND YEAR(qdsj) = '$selectedYear'";  // 年份筛选
            </div>
            <p><strong>说明：</strong>通过projectid关联项目，按签订时间年份筛选</p>
        </div>
        
        <div class="data-source dynamic-data">
            <h4><span class="tag tag-dynamic">动态</span><span class="tag tag-table">tuqoa_xmcztjb</span>实际成本统计</h4>
            <p><strong>数据内容：</strong>项目实际成本支出总额</p>
            <p><strong>来源：</strong>tuqoa_xmcztjb表的wccz字段</p>
            <div class="code-snippet">
SELECT COALESCE(SUM(wccz), 0) as sjcbzje 
FROM `tuqoa_xmcztjb` 
WHERE 1=1 $cost_filter $cost_year_filter

// 筛选条件
$cost_filter = " AND projectid = '$gcid'";  // 项目筛选
$cost_year_filter = " AND YEAR(sbrq) = '$selectedYear'";  // 年份筛选
            </div>
            <p><strong>说明：</strong>统计完成成本，按申报日期年份筛选</p>
        </div>
        
        <h3>3. 计算数据</h3>
        
        <div class="data-source table-info">
            <h4><span class="tag tag-calc">计算</span>成本控制率</h4>
            <p><strong>数据内容：</strong>实际成本占收费总额的百分比</p>
            <p><strong>计算公式：</strong>成本控制率 = (实际成本 ÷ 收费总额) × 100%</p>
            <div class="code-snippet">
$cbkzl = ($sfzje > 0) ? round(($sjcbzje / $sfzje) * 100, 1) : 0;

// 动态样式判断
$card_class = ($cbkzl <= 80) ? 'stat-card-success' : 'stat-card-danger';
$status_text = ($cbkzl <= 80) ? '控制良好' : '需要关注';
            </div>
            <p><strong>说明：</strong>≤80%显示为良好（绿色），>80%显示为需要关注（红色）</p>
        </div>
        
        <h3>4. 图表数据</h3>
        
        <div class="data-source chart-info">
            <h4><span class="tag tag-chart">图表</span>月度成本趋势</h4>
            <p><strong>数据内容：</strong>12个月的成本支出趋势</p>
            <p><strong>来源：</strong>tuqoa_xmcztjb表按月统计</p>
            <div class="code-snippet">
// 循环生成12个月数据
for ($i = 1; $i <= 12; $i++) {
    $month = sprintf('%02d', $i);
    $monthly_filter = " AND projectid = '$gcid'";  // 项目筛选
    
    $sql_cost = "SELECT COALESCE(SUM(wccz), 0) as monthly_cost 
                 FROM tuqoa_xmcztjb 
                 WHERE DATE_FORMAT(sbrq, '%Y-%m') = '$selectedYear-$month' 
                 $monthly_filter";
}
            </div>
            <p><strong>说明：</strong>使用DATE_FORMAT按年-月分组统计</p>
        </div>
        
        <div class="data-source chart-info">
            <h4><span class="tag tag-chart">图表</span>收入vs成本对比</h4>
            <p><strong>数据内容：</strong>月度收入和成本的对比分析</p>
            <p><strong>来源：</strong>tuqoa_htgl和tuqoa_xmcztjb表</p>
            <div class="code-snippet">
// 月度收入查询
$sql_income = "SELECT COALESCE(SUM(fwf), 0) as monthly_income 
               FROM tuqoa_htgl 
               WHERE DATE_FORMAT(qdsj, '%Y-%m') = '$month' $income_filter";

// 月度成本查询
$sql_cost_compare = "SELECT COALESCE(SUM(wccz), 0) as monthly_cost 
                     FROM tuqoa_xmcztjb 
                     WHERE DATE_FORMAT(sbrq, '%Y-%m') = '$month' $cost_compare_filter";
            </div>
            <p><strong>说明：</strong>同时查询收入和成本，生成对比图表</p>
        </div>
        
        <div class="data-source chart-info">
            <h4><span class="tag tag-chart">图表</span>项目状态分布</h4>
            <p><strong>数据内容：</strong>项目按状态的分布情况</p>
            <p><strong>来源：</strong>tuqoa_gcproject表的xmzt字段</p>
            <div class="code-snippet">
$sql_status = "SELECT xmzt, COUNT(*) as count 
               FROM tuqoa_gcproject 
               WHERE 1=1 $status_filter 
               GROUP BY xmzt";
            </div>
            <p><strong>说明：</strong>按项目状态分组统计，支持项目筛选</p>
        </div>
        
        <div class="data-source chart-info">
            <h4><span class="tag tag-chart">图表</span>部门/地区分布</h4>
            <p><strong>数据内容：</strong>项目按部门或地区的分布</p>
            <p><strong>来源：</strong>tuqoa_gcproject表的bumen字段</p>
            <div class="code-snippet">
// 优先按部门分布
$sql_region = "SELECT bumen, COUNT(*) as count 
               FROM tuqoa_gcproject 
               WHERE bumen IS NOT NULL AND bumen != '' $status_filter 
               GROUP BY bumen 
               ORDER BY count DESC 
               LIMIT 6";

// 如果没有部门数据，按项目状态分布
$sql_status_dist = "SELECT xmzt, COUNT(*) as count 
                    FROM tuqoa_gcproject 
                    WHERE 1=1 $status_filter 
                    GROUP BY xmzt 
                    ORDER BY count DESC";
            </div>
            <p><strong>说明：</strong>智能选择分布维度，优先显示部门分布</p>
        </div>
        
        <h3>5. 明细表格数据</h3>
        
        <div class="data-source table-info">
            <h4><span class="tag tag-table">表格</span>项目成本明细</h4>
            <p><strong>数据内容：</strong>项目的详细成本核算信息</p>
            <p><strong>来源：</strong>多表关联查询</p>
            <div class="code-snippet">
// 主查询：项目基本信息
SELECT * FROM `tuqoa_gcproject` 
WHERE xmzt in ('新开工项目','在建项目','完工未结算') $detail_filter 
order by id desc

// 关联查询：收费额
SELECT COALESCE(SUM(fwf), 0) as sfje 
FROM `tuqoa_htgl` 
WHERE `projectid`='$project_id'

// 关联查询：实际成本
SELECT COALESCE(SUM(wccz), 0) as sjcb 
FROM `tuqoa_xmcztjb` 
WHERE `projectid`='$project_id'
            </div>
            <p><strong>说明：</strong>为每个项目查询收费和成本，计算利润率</p>
        </div>
        
        <h2>数据表结构</h2>
        
        <div class="table-info">
            <h4>主要数据表及关键字段</h4>
            <ul>
                <li><strong>tuqoa_gcproject：</strong>工程项目表
                    <ul>
                        <li>id：项目ID</li>
                        <li>gcname：项目名称</li>
                        <li>zaojia：项目造价</li>
                        <li>xmzt：项目状态</li>
                        <li>bumen：所属部门</li>
                        <li>jcsj：检查时间</li>
                    </ul>
                </li>
                <li><strong>tuqoa_htgl：</strong>合同管理表
                    <ul>
                        <li>projectid：关联项目ID</li>
                        <li>fwf：服务费</li>
                        <li>qdsj：签订时间</li>
                    </ul>
                </li>
                <li><strong>tuqoa_xmcztjb：</strong>项目成本统计表
                    <ul>
                        <li>projectid：关联项目ID</li>
                        <li>wccz：完成成本</li>
                        <li>sbrq：申报日期</li>
                    </ul>
                </li>
            </ul>
        </div>
        
        <h2>页面特点</h2>
        
        <h3>1. 多维度筛选</h3>
        <ul>
            <li>项目维度：支持单个项目或全部项目</li>
            <li>时间维度：支持年份筛选</li>
            <li>状态维度：自动过滤已完工项目</li>
        </ul>
        
        <h3>2. 智能计算</h3>
        <ul>
            <li>成本控制率自动计算</li>
            <li>动态颜色和状态提示</li>
            <li>利润率计算（收费-成本）</li>
        </ul>
        
        <h3>3. 多表关联</h3>
        <ul>
            <li>项目表提供基础信息</li>
            <li>合同表提供收费数据</li>
            <li>成本表提供支出数据</li>
            <li>通过projectid关联</li>
        </ul>
        
        <h3>4. 容错处理</h3>
        <ul>
            <li>使用COALESCE处理空值</li>
            <li>除零检查避免错误</li>
            <li>SQL错误的注释输出</li>
        </ul>
        
        <h3>5. 系统功能增强</h3>
        <ul>
            <li>自定义函数支持（getconfig、c函数）</li>
            <li>颜色转换功能</li>
            <li>错误报告和调试支持</li>
        </ul>
        
        <h2>总结</h2>
        <p>xmcbhs.php是一个功能完整的项目成本核算分析系统，数据来源包括：</p>
        <ul>
            <li><strong>用户输入：</strong>项目选择和年份筛选</li>
            <li><strong>动态数据：</strong>从3个核心表查询的实时财务数据</li>
            <li><strong>计算数据：</strong>成本控制率、利润率等关键指标</li>
            <li><strong>图表数据：</strong>多维度的趋势分析和分布统计</li>
            <li><strong>明细数据：</strong>项目级别的详细成本核算</li>
        </ul>
        <p>该页面通过多表关联查询和智能计算，提供了全面的项目成本控制和分析功能，是项目管理中的重要工具。</p>
    </div>
</body>
</html>
