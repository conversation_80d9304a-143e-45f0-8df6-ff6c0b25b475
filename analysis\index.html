<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PHP页面数据来源分析报告 - 索引</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 10px;
            font-size: 2.5rem;
        }
        .subtitle {
            text-align: center;
            color: #7f8c8d;
            margin-bottom: 30px;
            font-size: 1.1rem;
        }
        .report-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .report-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 10px;
            padding: 20px;
            border-left: 5px solid #007bff;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .report-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,123,255,0.3);
        }
        .report-card.summary {
            border-left-color: #28a745;
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        }
        .report-card.detailed {
            border-left-color: #17a2b8;
            background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
        }
        .report-card.quick {
            border-left-color: #ffc107;
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
        }
        .report-title {
            font-size: 1.3rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .report-desc {
            color: #6c757d;
            margin-bottom: 15px;
        }
        .report-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.9rem;
        }
        .tag {
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        .tag-summary { background: #28a745; color: white; }
        .tag-detailed { background: #17a2b8; color: white; }
        .tag-quick { background: #ffc107; color: #212529; }
        .stats-section {
            background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .stat-item {
            text-align: center;
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 8px;
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            color: #6c757d;
        }
        a {
            text-decoration: none;
            color: inherit;
        }
        .download-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border: 2px dashed #dee2e6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 PHP页面数据来源分析报告</h1>
        <p class="subtitle">公司数据总览系统 - 完整分析文档</p>
        
        <div class="stats-section">
            <h3>📈 分析统计概览</h3>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">26</div>
                    <div>PHP页面总数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">9</div>
                    <div>详细分析页面</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">6</div>
                    <div>核心数据表</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">3</div>
                    <div>数据类型</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">4</div>
                    <div>业务分类</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">37</div>
                    <div>分析报告</div>
                </div>
            </div>
        </div>

        <h2>📋 分析报告目录</h2>
        
        <div class="report-grid">
            <a href="总体分析汇总.html">
                <div class="report-card summary">
                    <div class="report-title">🎯 总体分析汇总</div>
                    <div class="report-desc">
                        全面概览所有PHP页面的数据来源分析，包括页面分类、数据表结构、技术特点等核心信息。
                    </div>
                    <div class="report-meta">
                        <span class="tag tag-summary">总览报告</span>
                        <span>⭐ 推荐首读</span>
                    </div>
                </div>
            </a>

            <a href="剩余页面快速分析.html">
                <div class="report-card quick">
                    <div class="report-title">⚡ 剩余页面快速分析</div>
                    <div class="report-desc">
                        基于文件名和业务模式，对22个未详细分析的页面进行快速分类和功能预测。
                    </div>
                    <div class="report-meta">
                        <span class="tag tag-quick">快速分析</span>
                        <span>22个页面</span>
                    </div>
                </div>
            </a>

            <a href="低优先级页面分析汇总.html">
                <div class="report-card quick">
                    <div class="report-title">📊 低优先级页面分析汇总</div>
                    <div class="report-desc">
                        深度分析2个重点低优先级页面，包含复杂的成本控制和经营分析功能。
                    </div>
                    <div class="report-meta">
                        <span class="tag tag-quick">重点分析</span>
                        <span>2个页面</span>
                    </div>
                </div>
            </a>

            <a href="最终完整分析报告.html">
                <div class="report-card summary">
                    <div class="report-title">🎯 最终完整分析报告</div>
                    <div class="report-desc">
                        全面总结所有分析成果，包含技术架构、业务价值、优化建议等完整内容。
                    </div>
                    <div class="report-meta">
                        <span class="tag tag-summary">完整总结</span>
                        <span>⭐ 最终报告</span>
                    </div>
                </div>
            </a>

            <a href="项目总结报告.html">
                <div class="report-card summary">
                    <div class="report-title">🏆 项目总结报告</div>
                    <div class="report-desc">
                        项目执行情况总结，包含成果清单、价值分析和后续建议。
                    </div>
                    <div class="report-meta">
                        <span class="tag tag-summary">项目总结</span>
                        <span>🎉 圆满完成</span>
                    </div>
                </div>
            </a>

            <a href="PHP页面分析总结.html">
                <div class="report-card summary">
                    <div class="report-title">🎯 PHP页面分析总结</div>
                    <div class="report-desc">
                        26个PHP页面的全面分析汇总，包含技术特点、业务价值和优化建议。
                    </div>
                    <div class="report-meta">
                        <span class="tag tag-summary">技术总结</span>
                        <span>⭐ 核心成果</span>
                    </div>
                </div>
            </a>

            <a href="最终分析总结报告.html">
                <div class="report-card summary">
                    <div class="report-title">🏆 最终分析总结报告</div>
                    <div class="report-desc">
                        PHP页面深度分析项目的圆满完成总结，包含项目成果、质量评估和价值体现。
                    </div>
                    <div class="report-meta">
                        <span class="tag tag-summary">项目完成</span>
                        <span>🎉 圆满成功</span>
                    </div>
                </div>
            </a>
        </div>

        <h3>🔧 专项分析报告</h3>

        <div class="report-grid">
            <a href="明细页面专项分析.html">
                <div class="report-card detailed">
                    <div class="report-title">📋 明细页面专项分析</div>
                    <div class="report-desc">
                        深度分析费用明细、预决算等专业页面的数据处理逻辑和业务价值。
                    </div>
                    <div class="report-meta">
                        <span class="tag tag-detailed">专项分析</span>
                        <span>明细页面</span>
                    </div>
                </div>
            </a>

            <a href="xmhtdzmx_analysis.html">
                <div class="report-card detailed">
                    <div class="report-title">💰 项目合同到账明细分析</div>
                    <div class="report-desc">
                        项目收款情况详细分析，包含月度收款对比、收款进度监控等功能。
                    </div>
                    <div class="report-meta">
                        <span class="tag tag-detailed">财务分析</span>
                        <span>收款管理</span>
                    </div>
                </div>
            </a>

            <a href="ydjysjfx_analysis.html">
                <div class="report-card detailed">
                    <div class="report-title">📈 月度经营数据分析</div>
                    <div class="report-desc">
                        企业月度经营状况综合分析，包含12个月趋势分析和经营健康度评估。
                    </div>
                    <div class="report-meta">
                        <span class="tag tag-detailed">经营分析</span>
                        <span>决策支持</span>
                    </div>
                </div>
            </a>

            <a href="xmcbkzhzb_analysis.html">
                <div class="report-card detailed">
                    <div class="report-title">📊 项目成本控制汇总表分析</div>
                    <div class="report-desc">
                        企业项目成本全面监控和控制中心，包含多维度成本统计和预警机制。
                    </div>
                    <div class="report-meta">
                        <span class="tag tag-detailed">成本控制</span>
                        <span>核心功能</span>
                    </div>
                </div>
            </a>

            <a href="myxmcbmx_analysis.html">
                <div class="report-card detailed">
                    <div class="report-title">📋 某月项目成本明细分析</div>
                    <div class="report-desc">
                        单项目月度成本详细分解和分析，包含成本结构分析和收支平衡分析。
                    </div>
                    <div class="report-meta">
                        <span class="tag tag-detailed">成本明细</span>
                        <span>精细管理</span>
                    </div>
                </div>
            </a>

            <a href="wtgz_analysis.html">
                <div class="report-card detailed">
                    <div class="report-title">🔧 问题工作汇总分析</div>
                    <div class="report-desc">
                        工程项目问题跟踪和解决情况综合分析，包含多源数据整合和智能问题识别。
                    </div>
                    <div class="report-meta">
                        <span class="tag tag-detailed">质量管理</span>
                        <span>问题跟踪</span>
                    </div>
                </div>
            </a>

            <a href="myxmfymx_analysis.html">
                <div class="report-card detailed">
                    <div class="report-title">💸 某月项目费用明细分析</div>
                    <div class="report-desc">
                        单项目月度费用详细分解和成本分析，包含人工费、福利费等各项费用统计。
                    </div>
                    <div class="report-meta">
                        <span class="tag tag-detailed">费用管理</span>
                        <span>精细分析</span>
                    </div>
                </div>
            </a>

            <a href="jytj_detailed_analysis.html">
                <div class="report-card detailed">
                    <div class="report-title">📊 经营统计详细分析</div>
                    <div class="report-desc">
                        企业经营状况综合统计和趋势分析，包含季度月度双重维度和经营健康度评估。
                    </div>
                    <div class="report-meta">
                        <span class="tag tag-detailed">经营统计</span>
                        <span>趋势分析</span>
                    </div>
                </div>
            </a>

            <a href="ygdt_analysis.html">
                <div class="report-card detailed">
                    <div class="report-title">👥 员工动态分析</div>
                    <div class="report-desc">
                        企业人力资源动态监控和分析中心，包含人员变动趋势和部门分布分析。
                    </div>
                    <div class="report-meta">
                        <span class="tag tag-detailed">人力资源</span>
                        <span>动态监控</span>
                    </div>
                </div>
            </a>

            <a href="数据库优化建议.html">
                <div class="report-card summary">
                    <div class="report-title">🚀 数据库优化建议</div>
                    <div class="report-desc">
                        基于页面分析结果的数据库性能优化方案，包含索引优化、查询重构等。
                    </div>
                    <div class="report-meta">
                        <span class="tag tag-summary">优化方案</span>
                        <span>性能提升</span>
                    </div>
                </div>
            </a>

            <a href="系统架构改进建议.html">
                <div class="report-card summary">
                    <div class="report-title">🏗️ 系统架构改进建议</div>
                    <div class="report-desc">
                        现代化架构改进方案，包含MVC重构、安全性提升、缓存机制等。
                    </div>
                    <div class="report-meta">
                        <span class="tag tag-summary">架构升级</span>
                        <span>现代化改造</span>
                    </div>
                </div>
            </a>
        </div>

        <h3>🛠️ 开发工具文档</h3>

        <div class="report-grid">
            <a href="API接口设计文档.html">
                <div class="report-card detailed">
                    <div class="report-title">🔌 API接口设计文档</div>
                    <div class="report-desc">
                        基于现有页面功能设计的RESTful API接口规范，包含完整的接口定义。
                    </div>
                    <div class="report-meta">
                        <span class="tag tag-detailed">接口设计</span>
                        <span>RESTful API</span>
                    </div>
                </div>
            </a>

            <a href="数据字典文档.html">
                <div class="report-card detailed">
                    <div class="report-title">📚 数据字典文档</div>
                    <div class="report-desc">
                        完整的数据库表结构说明，包含字段定义、关系图和索引建议。
                    </div>
                    <div class="report-meta">
                        <span class="tag tag-detailed">数据库</span>
                        <span>25+表结构</span>
                    </div>
                </div>
            </a>

            <a href="测试用例文档.html">
                <div class="report-card detailed">
                    <div class="report-title">🧪 测试用例文档</div>
                    <div class="report-desc">
                        系统功能测试用例和测试策略，包含功能、性能、安全等测试。
                    </div>
                    <div class="report-meta">
                        <span class="tag tag-detailed">测试策略</span>
                        <span>全面测试</span>
                    </div>
                </div>
            </a>
        </div>

        <h3>🚀 运维管理文档</h3>

        <div class="report-grid">
            <a href="部署运维指南.html">
                <div class="report-card summary">
                    <div class="report-title">🚀 部署运维指南</div>
                    <div class="report-desc">
                        系统部署、监控和维护的完整指南，包含环境配置和故障排除。
                    </div>
                    <div class="report-meta">
                        <span class="tag tag-summary">运维指南</span>
                        <span>生产部署</span>
                    </div>
                </div>
            </a>

            <a href="用户使用手册.html">
                <div class="report-card quick">
                    <div class="report-title">📖 用户使用手册</div>
                    <div class="report-desc">
                        面向最终用户的系统使用指南，包含功能介绍和操作步骤。
                    </div>
                    <div class="report-meta">
                        <span class="tag tag-quick">用户指南</span>
                        <span>操作手册</span>
                    </div>
                </div>
            </a>
        </div>

        <h3>🔧 系统诊断专题</h3>

        <div class="report-grid">
            <a href="系统诊断功能增强建议.html">
                <div class="report-card detailed">
                    <div class="report-title">🔧 系统诊断功能增强建议</div>
                    <div class="report-desc">
                        基于现有diagnose.php的深度分析和功能增强方案，包含性能监控、实时更新等。
                    </div>
                    <div class="report-meta">
                        <span class="tag tag-detailed">功能增强</span>
                        <span>诊断升级</span>
                    </div>
                </div>
            </a>

            <a href="实用监控脚本示例.html">
                <div class="report-card detailed">
                    <div class="report-title">📊 实用监控脚本示例</div>
                    <div class="report-desc">
                        完整的系统监控脚本集合，包含PHP后端、JavaScript前端和API接口。
                    </div>
                    <div class="report-meta">
                        <span class="tag tag-detailed">代码示例</span>
                        <span>即用脚本</span>
                    </div>
                </div>
            </a>
        </div>

        <h3>🔍 详细分析报告</h3>
        
        <div class="report-grid">
            <a href="diagnose_analysis.html">
                <div class="report-card detailed">
                    <div class="report-title">🔧 diagnose.php</div>
                    <div class="report-desc">
                        系统诊断页面分析 - 混合数据类型，包含系统检查和数据库验证功能。
                    </div>
                    <div class="report-meta">
                        <span class="tag tag-detailed">详细分析</span>
                        <span>系统诊断</span>
                    </div>
                </div>
            </a>

            <a href="dkzbcx_analysis.html">
                <div class="report-card detailed">
                    <div class="report-title">🛡️ dkzbcx.php</div>
                    <div class="report-desc">
                        代打卡监测系统分析 - 动态数据查询，复杂的异常检测算法实现。
                    </div>
                    <div class="report-meta">
                        <span class="tag tag-detailed">详细分析</span>
                        <span>考勤监测</span>
                    </div>
                </div>
            </a>

            <a href="dzetj_analysis.html">
                <div class="report-card detailed">
                    <div class="report-title">💰 dzetj.php</div>
                    <div class="report-desc">
                        到账额统计页面分析 - 财务数据统计，包含趋势图和分布图表。
                    </div>
                    <div class="report-meta">
                        <span class="tag tag-detailed">详细分析</span>
                        <span>财务统计</span>
                    </div>
                </div>
            </a>

            <a href="fgbmxmhzb_analysis.html">
                <div class="report-card detailed">
                    <div class="report-title">📊 fgbmxmhzb.php</div>
                    <div class="report-desc">
                        分管部门项目汇总报表分析 - 静态数据展示，丰富的图表设计。
                    </div>
                    <div class="report-meta">
                        <span class="tag tag-detailed">详细分析</span>
                        <span>部门报表</span>
                    </div>
                </div>
            </a>

            <a href="gsxmsjhz_analysis.html">
                <div class="report-card detailed">
                    <div class="report-title">🏢 gsxmsjhz.php</div>
                    <div class="report-desc">
                        公司项目数据汇总分析 - 复杂的多维度数据分析和可视化展示。
                    </div>
                    <div class="report-meta">
                        <span class="tag tag-detailed">详细分析</span>
                        <span>项目汇总</span>
                    </div>
                </div>
            </a>

            <a href="ssjdgzhz_analysis.html">
                <div class="report-card detailed">
                    <div class="report-title">⚡ ssjdgzhz.php</div>
                    <div class="report-desc">
                        实时进度工作汇总分析 - 多业务模块整合的复杂系统。
                    </div>
                    <div class="report-meta">
                        <span class="tag tag-detailed">详细分析</span>
                        <span>进度监控</span>
                    </div>
                </div>
            </a>

            <a href="xmcbhs_analysis.html">
                <div class="report-card detailed">
                    <div class="report-title">💼 xmcbhs.php</div>
                    <div class="report-desc">
                        项目成本核算分析 - 完整的成本控制和分析系统。
                    </div>
                    <div class="report-meta">
                        <span class="tag tag-detailed">详细分析</span>
                        <span>成本核算</span>
                    </div>
                </div>
            </a>

            <a href="jydt_analysis.html">
                <div class="report-card detailed">
                    <div class="report-title">📈 jydt.php</div>
                    <div class="report-desc">
                        经营动态监控分析 - 实时经营状况监控系统。
                    </div>
                    <div class="report-meta">
                        <span class="tag tag-detailed">详细分析</span>
                        <span>经营监控</span>
                    </div>
                </div>
            </a>

            <a href="jytj_analysis.html">
                <div class="report-card detailed">
                    <div class="report-title">📊 jytj.php</div>
                    <div class="report-desc">
                        经营统计分析 - 双时间维度的财务统计分析。
                    </div>
                    <div class="report-meta">
                        <span class="tag tag-detailed">详细分析</span>
                        <span>经营统计</span>
                    </div>
                </div>
            </a>

            <a href="gztz_analysis.html">
                <div class="report-card detailed">
                    <div class="report-title">📋 gztz.php</div>
                    <div class="report-desc">
                        工作台账管理分析 - 11个业务表的工作记录系统。
                    </div>
                    <div class="report-meta">
                        <span class="tag tag-detailed">详细分析</span>
                        <span>工作台账</span>
                    </div>
                </div>
            </a>

            <a href="中优先级页面分析汇总.html">
                <div class="report-card quick">
                    <div class="report-title">📑 中优先级页面汇总</div>
                    <div class="report-desc">
                        中优先级页面的快速分析汇总，包含4个重要功能页面。
                    </div>
                    <div class="report-meta">
                        <span class="tag tag-quick">汇总分析</span>
                        <span>4个页面</span>
                    </div>
                </div>
            </a>
        </div>

        <div class="download-section">
            <h3>📥 使用说明</h3>
            <ul>
                <li><strong>总体分析汇总：</strong>建议首先阅读，了解整体情况</li>
                <li><strong>详细分析报告：</strong>针对具体页面的深入分析</li>
                <li><strong>快速分析：</strong>了解未详细分析页面的预测信息</li>
                <li><strong>技术要点：</strong>每个报告都包含数据源、图表类型、SQL查询等技术细节</li>
            </ul>
        </div>

        <h3>🎯 主要发现</h3>
        
        <div class="report-grid">
            <div class="report-card">
                <div class="report-title">📊 数据类型分布</div>
                <div class="report-desc">
                    <strong>动态数据：</strong>大部分页面使用数据库实时查询<br>
                    <strong>静态数据：</strong>部分页面使用硬编码展示数据<br>
                    <strong>混合数据：</strong>系统功能页面结合两种方式
                </div>
            </div>

            <div class="report-card">
                <div class="report-title">🗄️ 核心数据表</div>
                <div class="report-desc">
                    <strong>tuqoa_gcproject：</strong>工程项目表<br>
                    <strong>tuqoa_htgl：</strong>合同管理表<br>
                    <strong>tuqoa_xmcztjb：</strong>项目成本统计表<br>
                    <strong>tuqoa_htsf：</strong>合同收费表
                </div>
            </div>

            <div class="report-card">
                <div class="report-title">📈 图表类型</div>
                <div class="report-desc">
                    <strong>统计卡片：</strong>关键指标展示<br>
                    <strong>趋势图表：</strong>时间序列分析<br>
                    <strong>分布图表：</strong>分类占比分析<br>
                    <strong>明细表格：</strong>详细数据展示
                </div>
            </div>

            <div class="report-card">
                <div class="report-title">🔧 技术特点</div>
                <div class="report-desc">
                    <strong>前端：</strong>Bootstrap + Chart.js<br>
                    <strong>后端：</strong>PHP + MySQL<br>
                    <strong>查询：</strong>复杂SQL + 数据处理<br>
                    <strong>交互：</strong>日期选择 + 参数筛选
                </div>
            </div>
        </div>

        <div class="footer">
            <p>📅 分析完成时间：2025年8月7日</p>
            <p>🔍 分析范围：f:\phpEnv\www\localhost\reportforms\jyfx\ 目录下所有PHP文件</p>
            <p>📝 分析方法：代码审查 + 数据库结构分析 + 功能推测</p>
            <p>⚠️ 注意：部分页面基于文件名和常见模式进行预测分析，实际功能可能有差异</p>
        </div>
    </div>

    <script>
        // 添加点击统计
        document.querySelectorAll('.report-card').forEach(card => {
            card.addEventListener('click', function() {
                console.log('访问报告:', this.querySelector('.report-title').textContent);
            });
        });

        // 添加加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.report-card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';
                    card.style.transition = 'all 0.5s ease';
                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 100);
            });
        });
    </script>
</body>
</html>
