<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>经营统计 - 公司数据总览系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/boxicons@2.0.7/css/boxicons.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="styles/main.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        /* 页面特定样式 */
        .chart-container {
            height: 350px;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bx bx-line-chart me-2"></i>
                经营统计分析
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text text-white">
                    <i class="bx bx-time me-1"></i>
                    最后更新: <span id="last-update-time"><?php echo date('Y-m-d H:i:s'); ?></span>
                </span>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4"><?php
include '../config.php';
$firstDayOfMonth = date('Y-m-01');
$lastDayOfMonth = date('Y-m-t');
$startDate = isset($_POST['start-date']) ? $_POST['start-date'] : $firstDayOfMonth;
$endDate = isset($_POST['end-date']) ? $_POST['end-date'] : $lastDayOfMonth;
// 初始化日期变量
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
        // 验证日期
    if (strtotime($startDate) > strtotime($endDate)) {
        echo '<div class="result" style="background-color: #fde8e8;">错误：开始日期不能晚于结束日期</div>';
    } else {
        // 格式化日期用于显示
        $displayStart = date('Y年m月d日', strtotime($startDate));
        $displayEnd = date('Y年m月d日', strtotime($endDate));
        $daysDiff = (strtotime($endDate) - strtotime($startDate)) / (60 * 60 * 24) + 1;
        
       
        
        // 添加本月信息
        $currentMonth = date('Y年m月');
        $monthDays = date('t', strtotime($firstDayOfMonth));
       
    }
}
    ?>    
        <!-- 日期区间选择器 -->
        <div class="date-range-container">
            <form method="post" action="">
                <div class="form-group">
                    <label for="start-date">开始日期:</label>
                    <input type="date" id="start-date" name="start-date" 
                           value="<?php echo htmlspecialchars($startDate); ?>">
                    <label for="end-date">结束日期:</label>
                    <input type="date" id="end-date" name="end-date" 
                           value="<?php echo htmlspecialchars($endDate); ?>">
                    <button type="submit" id="query-btn">
                        <i class="fas fa-search me-1"></i>查询数据
                    </button>
                </div>
            </form>
        </div>
        
        <!-- 统计卡片 -->
        <div class="row">
            <?php
            //计算服务费合计
            $sql="SELECT sum(fwf) fwfhj FROM `tuqoa_htgl` WHERE `qdsj`>='$startDate' and `qdsj`<='$endDate'";
            $result = mysqli_query($link, $sql);
            $fwfhj = 0;
            if ($result) {
                while ($row = mysqli_fetch_assoc($result)) {
                    $fwfhj=$row["fwfhj"];
                }
            } else {
                echo "查询错误: " . mysqli_error($link);
            }
            ?>
            <div class="col-md-3">
                <div class="card stat-card stat-card-primary">
                    <div class="card-body">
                        <i class="fas fa-file-contract stat-icon"></i>
                        <h5 class="card-title">合同额合计</h5>
                        <h2 class="card-text">¥<?php echo number_format($fwfhj); ?></h2>
                        <p class="stat-info">万元</p>
                    </div>
                </div>
            </div>
            
            <?php
            //计算预计收费合计
            $sql="SELECT sum(yjje) as yjjehj FROM `tuqoa_htsf` WHERE `yjsj`>='$startDate' and `yjsj`<='$endDate'";
            $result = mysqli_query($link, $sql);
            $yjjehj = 0;
            if ($result) {
                while ($row = mysqli_fetch_assoc($result)) {
                    $yjjehj=$row["yjjehj"];
                }
            } else {
                echo "查询错误: " . mysqli_error($link);
            }
            ?>
            <div class="col-md-3">
                <div class="card stat-card stat-card-success">
                    <div class="card-body">
                        <i class="fas fa-chart-line stat-icon"></i>
                        <h5 class="card-title">预计营收额</h5>
                        <h2 class="card-text">¥<?php echo number_format($yjjehj); ?></h2>
                        <p class="stat-info">万元</p>
                    </div>
                </div>
            </div>
            
            <?php
            //计算回款合计
            $sql="SELECT sum(ysje) as ysjehj FROM `tuqoa_htsf` WHERE `sksj`>='$startDate' and `sksj`<='$endDate'";
            $result = mysqli_query($link, $sql);
            $ysjehj = 0;
            if ($result) {
                while ($row = mysqli_fetch_assoc($result)) {
                    $ysjehj=$row["ysjehj"];
                }
            } else {
                echo "查询错误: " . mysqli_error($link);
            }
            ?>
            <div class="col-md-3">
                <div class="card stat-card stat-card-warning">
                    <div class="card-body">
                        <i class="fas fa-hand-holding-usd stat-icon"></i>
                        <h5 class="card-title">实际回款额</h5>
                        <h2 class="card-text">¥<?php echo number_format($ysjehj); ?></h2>
                        <p class="stat-info">万元</p>
                    </div>
                </div>
            </div>
            
            <?php
            $lrhj=$yjjehj-$ysjehj;
            $profit_class = $lrhj >= 0 ? 'stat-card-success' : 'stat-card-danger';
            ?>
            <div class="col-md-3">
                <div class="card stat-card <?php echo $profit_class; ?>">
                    <div class="card-body">
                        <i class="fas fa-balance-scale stat-icon"></i>
                        <h5 class="card-title">收支差额</h5>
                        <h2 class="card-text"><?php echo $lrhj >= 0 ? '+' : ''; ?>¥<?php echo number_format($lrhj); ?></h2>
                        <p class="stat-info">万元</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 图表区域 -->
        <div class="row mt-4">
                <?php
                // 基于用户选择的时间段生成经营指标趋势数据
                $startTimestamp = strtotime($startDate);
                $endTimestamp = strtotime($endDate);
                $totalDays = ($endTimestamp - $startTimestamp) / (24 * 60 * 60) + 1;

                // 根据时间跨度决定分组方式
                $months = [];
                $total_service_fee = [];
                $yjjeData = [];
                $ysjeData = [];

                if ($totalDays <= 31) {
                    // 一个月内：按周分组
                    $intervals = 4;
                    $intervalDays = ceil($totalDays / $intervals);

                    for ($i = 0; $i < $intervals; $i++) {
                        $periodStart = date('Y-m-d', $startTimestamp + ($i * $intervalDays * 24 * 60 * 60));
                        $periodEnd = date('Y-m-d', min($endTimestamp, $startTimestamp + (($i + 1) * $intervalDays * 24 * 60 * 60) - 1));

                        if ($i == $intervals - 1) {
                            $periodEnd = $endDate; // 确保最后一个区间包含结束日期
                        }

                        $months[] = date('m/d', strtotime($periodEnd));

                        // 查询合同额
                        $sql = "SELECT IFNULL(SUM(fwf), 0) as total FROM tuqoa_htgl WHERE qdsj >= '$periodStart' AND qdsj <= '$periodEnd'";
                        $result = mysqli_query($link, $sql);
                        $row = $result ? mysqli_fetch_assoc($result) : ['total' => 0];
                        $total_service_fee[] = (float)$row['total'];

                        // 查询预计收费
                        $sql_yj = "SELECT IFNULL(SUM(yjje), 0) as total FROM tuqoa_htsf WHERE yjsj >= '$periodStart' AND yjsj <= '$periodEnd'";
                        $result_yj = mysqli_query($link, $sql_yj);
                        $yj_row = $result_yj ? mysqli_fetch_assoc($result_yj) : ['total' => 0];
                        $yjjeData[] = (float)$yj_row['total'];

                        // 查询实际收费
                        $sql_ys = "SELECT IFNULL(SUM(ysje), 0) as total FROM tuqoa_htsf WHERE sksj >= '$periodStart' AND sksj <= '$periodEnd'";
                        $result_ys = mysqli_query($link, $sql_ys);
                        $ys_row = $result_ys ? mysqli_fetch_assoc($result_ys) : ['total' => 0];
                        $ysjeData[] = (float)$ys_row['total'];
                    }
                } else {
                    // 超过一个月：按月分组
                    $currentDate = $startTimestamp;
                    while ($currentDate <= $endTimestamp) {
                        $monthStart = date('Y-m-01', $currentDate);
                        $monthEnd = date('Y-m-t', $currentDate);

                        // 调整为实际的开始和结束日期
                        if ($monthStart < $startDate) $monthStart = $startDate;
                        if ($monthEnd > $endDate) $monthEnd = $endDate;

                        $months[] = date('Y-m', $currentDate);

                        // 查询合同额
                        $sql = "SELECT IFNULL(SUM(fwf), 0) as total FROM tuqoa_htgl WHERE qdsj >= '$monthStart' AND qdsj <= '$monthEnd'";
                        $result = mysqli_query($link, $sql);
                        $row = $result ? mysqli_fetch_assoc($result) : ['total' => 0];
                        $total_service_fee[] = (float)$row['total'];

                        // 查询预计收费
                        $sql_yj = "SELECT IFNULL(SUM(yjje), 0) as total FROM tuqoa_htsf WHERE yjsj >= '$monthStart' AND yjsj <= '$monthEnd'";
                        $result_yj = mysqli_query($link, $sql_yj);
                        $yj_row = $result_yj ? mysqli_fetch_assoc($result_yj) : ['total' => 0];
                        $yjjeData[] = (float)$yj_row['total'];

                        // 查询实际收费
                        $sql_ys = "SELECT IFNULL(SUM(ysje), 0) as total FROM tuqoa_htsf WHERE sksj >= '$monthStart' AND sksj <= '$monthEnd'";
                        $result_ys = mysqli_query($link, $sql_ys);
                        $ys_row = $result_ys ? mysqli_fetch_assoc($result_ys) : ['total' => 0];
                        $ysjeData[] = (float)$ys_row['total'];

                        // 移动到下个月
                        $currentDate = strtotime('+1 month', $currentDate);
                    }
                }
                ?>
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-chart-line me-2"></i>经营指标趋势
                            <span class="badge bg-success ms-2">真实数据</span>
                        </h5>

                    </div>
                    <div class="card-body">
                        <?php
                        // 计算统计信息
                        $totalContract = array_sum($total_service_fee);
                        $totalExpected = array_sum($yjjeData);
                        $totalReceived = array_sum($ysjeData);
                        $dataPoints = count($months);
                        ?>
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <small class="text-muted">合同总额</small>
                                <div class="fw-bold text-primary">
                                    ¥<?php echo $totalContract >= 10000 ? number_format($totalContract/10000, 1).'万' : number_format($totalContract); ?>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted">预计收款</small>
                                <div class="fw-bold text-success">
                                    ¥<?php echo $totalExpected >= 10000 ? number_format($totalExpected/10000, 1).'万' : number_format($totalExpected); ?>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted">实际收款</small>
                                <div class="fw-bold text-danger">
                                    ¥<?php echo $totalReceived >= 10000 ? number_format($totalReceived/10000, 1).'万' : number_format($totalReceived); ?>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted">数据点数</small>
                                <div class="fw-bold text-info"><?php echo $dataPoints; ?> 个时间段</div>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="businessTrendChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <?php
            // 查询时间段内各项目的金额分布
            $sql = "SELECT htmc AS 'project_name', SUM(fwf) AS 'amount'
                    FROM tuqoa_htgl
                    WHERE qdsj >= '$startDate' AND qdsj <= '$endDate'
                    GROUP BY htmc
                    ORDER BY SUM(fwf) DESC
                    LIMIT 10"; // 限制显示前10个项目

            $result = mysqli_query($link, $sql);
            $projectData = [
                'labels' => [],
                'data' => [],
                'total' => 0
            ];

            $otherAmount = 0;
            $projectCount = 0;

            if ($result) {
                while ($row = mysqli_fetch_assoc($result)) {
                    $projectCount++;
                    if ($projectCount <= 7) {
                        // 显示前7个项目
                        $projectName = $row['project_name'];
                        // 如果项目名称过长，进行截断
                        if (mb_strlen($projectName) > 15) {
                            $projectName = mb_substr($projectName, 0, 12) . '...';
                        }
                        $projectData['labels'][] = $projectName;
                        $projectData['data'][] = (float)$row['amount'];
                    } else {
                        // 其余项目合并为"其他"
                        $otherAmount += (float)$row['amount'];
                    }
                    $projectData['total'] += (float)$row['amount'];
                }

                // 如果有其他项目，添加到数据中
                if ($otherAmount > 0) {
                    $projectData['labels'][] = '其他项目';
                    $projectData['data'][] = $otherAmount;
                }
            } else {
                echo "查询错误: " . mysqli_error($link);
            }

            // 为了兼容原有的JavaScript代码，保持deptData变量名
            $deptData = $projectData;
            ?>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-project-diagram me-2"></i>项目金额分布
                        </h5>

                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="businessTypeChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 经营指标明细 -->
        <div class="row mt-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">经营指标明细</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info" role="alert">
                            <i class="fas fa-info-circle me-2"></i>
                            经营数据总览
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php
    mysqli_close($link);
    ?>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 更新最后更新时间
        function updateLastUpdateTime() {
            const now = new Date();
            const formattedDate = now.getFullYear() + '-' +
                                 String(now.getMonth() + 1).padStart(2, '0') + '-' +
                                 String(now.getDate()).padStart(2, '0') + ' ' +
                                 String(now.getHours()).padStart(2, '0') + ':' +
                                 String(now.getMinutes()).padStart(2, '0') + ':' +
                                 String(now.getSeconds()).padStart(2, '0');
            document.getElementById('last-update-time').textContent = formattedDate;
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化图表
            initCharts();

            // 更新最后更新时间
            updateLastUpdateTime();

            // 每30秒更新一次时间
            setInterval(updateLastUpdateTime, 30000);

            // 查询按钮点击事件
            document.getElementById('query-btn').addEventListener('click', function(e) {
                const startDate = document.getElementById('start-date').value;
                const endDate = document.getElementById('end-date').value;
                
                if (!startDate || !endDate) {
                    e.preventDefault();
                    alert('请选择完整的日期范围');
                    return;
                }
                
                if (new Date(startDate) > new Date(endDate)) {
                    e.preventDefault();
                    alert('开始日期不能大于结束日期');
                    return;
                }
            });
        });
        
        // 初始化图表
        function initCharts() {
            // 经营指标趋势图表
            const trendCtx = document.getElementById('businessTrendChart').getContext('2d');
            const labels = <?php echo json_encode($months); ?>;
            new Chart(trendCtx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: '合同额',
                        data: <?php echo json_encode($total_service_fee); ?>,
                        borderColor: '#007bff',
                        backgroundColor: 'rgba(0, 123, 255, 0.1)',
                        tension: 0.4,
                        fill: true,
                        pointBackgroundColor: '#007bff',
                        pointBorderColor: '#ffffff',
                        pointBorderWidth: 2,
                        pointRadius: 6
                    }, {
                        label: '预计收款',
                        data: <?php echo json_encode($yjjeData); ?>,
                        borderColor: '#28a745',
                        backgroundColor: 'rgba(40, 167, 69, 0.1)',
                        tension: 0.4,
                        fill: true,
                        pointBackgroundColor: '#28a745',
                        pointBorderColor: '#ffffff',
                        pointBorderWidth: 2,
                        pointRadius: 6
                    }, {
                        label: '实际收款',
                        data: <?php echo json_encode($ysjeData); ?>,
                        borderColor: '#dc3545',
                        backgroundColor: 'rgba(220, 53, 69, 0.1)',
                        tension: 0.4,
                        fill: true,
                        pointBackgroundColor: '#dc3545',
                        pointBorderColor: '#ffffff',
                        pointBorderWidth: 2,
                        pointRadius: 6
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top'
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false,
                            backgroundColor: 'rgba(0,0,0,0.8)',
                            titleColor: 'white',
                            bodyColor: 'white',
                            borderColor: '#007bff',
                            borderWidth: 1,
                            callbacks: {
                                title: function(context) {
                                    return '时间: ' + context[0].label;
                                },
                                label: function(context) {
                                    const value = context.parsed.y;
                                    let formattedValue;
                                    if (value >= 10000) {
                                        formattedValue = '¥' + (value / 10000).toFixed(1) + '万';
                                    } else {
                                        formattedValue = '¥' + value.toLocaleString();
                                    }
                                    return context.dataset.label + ': ' + formattedValue;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '金额（元）'
                            },
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            },
                            ticks: {
                                callback: function(value) {
                                    if (value >= 10000) {
                                        return '¥' + (value / 10000).toFixed(1) + '万';
                                    }
                                    return '¥' + value.toLocaleString();
                                }
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: <?php echo $totalDays <= 31 ? "'时间段'" : "'月份'"; ?>
                            },
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            }
                        }
                    },
                    interaction: {
                        mode: 'index',
                        intersect: false
                    }
                }
            });

            // 项目金额分布图表
            const typeCtx = document.getElementById('businessTypeChart').getContext('2d');
            const projectData = <?php echo json_encode($deptData); ?>;

            if (projectData && projectData.labels && projectData.labels.length > 0) {
                new Chart(typeCtx, {
                    type: 'doughnut',
                    data: {
                        labels: projectData.labels,
                        datasets: [{
                            data: projectData.data,
                            backgroundColor: [
                                '#007bff', '#28a745', '#dc3545', '#ffc107', '#6f42c1',
                                '#fd7e14', '#20c997', '#6c757d', '#e83e8c', '#17a2b8'
                            ],
                            borderWidth: 2,
                            borderColor: '#fff',
                            hoverBorderWidth: 3
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    padding: 15,
                                    usePointStyle: true,
                                    font: {
                                        size: 11
                                    },
                                    generateLabels: function(chart) {
                                        const data = chart.data;
                                        if (data.labels.length && data.datasets.length) {
                                            const total = data.datasets[0].data.reduce((a, b) => a + b, 0);
                                            return data.labels.map((label, i) => {
                                                const value = data.datasets[0].data[i];
                                                const percentage = ((value / total) * 100).toFixed(1);
                                                return {
                                                    text: `${label} (${percentage}%)`,
                                                    fillStyle: data.datasets[0].backgroundColor[i],
                                                    strokeStyle: data.datasets[0].backgroundColor[i],
                                                    lineWidth: 0,
                                                    pointStyle: 'circle'
                                                };
                                            });
                                        }
                                        return [];
                                    }
                                }
                            },
                            tooltip: {
                                backgroundColor: 'rgba(0,0,0,0.8)',
                                titleColor: 'white',
                                bodyColor: 'white',
                                borderColor: '#007bff',
                                borderWidth: 1,
                                callbacks: {
                                    title: function(context) {
                                        return '项目金额统计';
                                    },
                                    label: function(context) {
                                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                        const percentage = ((context.parsed / total) * 100).toFixed(1);
                                        const value = context.parsed;
                                        let formattedValue;
                                        if (value >= 10000) {
                                            formattedValue = '¥' + (value / 10000).toFixed(1) + '万';
                                        } else {
                                            formattedValue = '¥' + value.toLocaleString();
                                        }
                                        return context.label + ': ' + formattedValue + ' (' + percentage + '%)';
                                    }
                                }
                            }
                        },
                        cutout: '50%'
                    }
                });
            } else {
                console.error('没有可用的项目数据');
                typeCtx.canvas.parentNode.innerHTML = '<div class="text-center text-muted p-4"><i class="fas fa-chart-pie fa-2x mb-2"></i><br>当前时间段内暂无项目数据</div>';
            }
        }
    </script>
</body>
</html> 