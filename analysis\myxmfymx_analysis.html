<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>某月项目费用明细分析</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 10px;
            font-size: 2.5rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .subtitle {
            text-align: center;
            color: #7f8c8d;
            margin-bottom: 30px;
            font-size: 1.1rem;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }
        h3 {
            color: #2980b9;
            margin-top: 20px;
        }
        .analysis-card {
            background: #f8f9fa;
            padding: 20px;
            margin: 15px 0;
            border-radius: 10px;
            border-left: 4px solid #3498db;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .data-source { border-left-color: #28a745; }
        .feature { border-left-color: #ffc107; }
        .chart { border-left-color: #dc3545; }
        .business { border-left-color: #6f42c1; }
        .code-snippet {
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 10px 0;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .table th, .table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
            font-size: 14px;
        }
        .table th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            font-weight: 600;
        }
        .highlight-box {
            background: #e7f3ff;
            border: 1px solid #b8daff;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .tag {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            margin-right: 5px;
        }
        .tag-query { background: #28a745; color: white; }
        .tag-calc { background: #ffc107; color: #212529; }
        .tag-expense { background: #dc3545; color: white; }
        .tag-detail { background: #6f42c1; color: white; }
        .expense-breakdown {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .expense-item {
            background: white;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }
        .metric-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>💸 某月项目费用明细分析</h1>
        <p class="subtitle">myxmfymx.php - 单项目月度费用详细分解和成本分析</p>
        
        <h2>📋 页面功能概述</h2>
        
        <div class="analysis-card business">
            <h3>核心功能</h3>
            <p>某月项目费用明细页面是项目财务精细化管理的重要工具，提供全面的费用分析：</p>
            <ul>
                <li><strong>费用详细分解：</strong>人工费、福利费、管理费等各项费用的详细分解</li>
                <li><strong>时间范围分析：</strong>支持指定月份的费用统计和分析</li>
                <li><strong>成本结构分析：</strong>各类费用占比和成本结构分析</li>
                <li><strong>人员费用统计：</strong>项目人员的工资、福利等费用统计</li>
            </ul>
            
            <div class="highlight-box">
                <strong>💡 管理价值：</strong>
                <ul>
                    <li>为项目经理提供精确的费用控制数据</li>
                    <li>帮助财务部门进行成本核算</li>
                    <li>支持项目盈利能力分析</li>
                    <li>为费用预算制定提供历史数据</li>
                </ul>
            </div>
        </div>

        <h2>🗄️ 复杂费用数据来源</h2>

        <div class="analysis-card data-source">
            <h3>多表关联费用统计</h3>
            
            <h4><span class="tag tag-query">项目</span>tuqoa_gcproject - 工程项目表</h4>
            <div class="code-snippet">
-- 获取项目基本信息
SELECT * FROM `tuqoa_gcproject` WHERE id=$projectid
            </div>
            <p><strong>用途：</strong>获取项目基本信息，作为费用分析的基础</p>
            
            <h4><span class="tag tag-query">人员</span>tuqoa_rydp - 人员配置表</h4>
            <div class="code-snippet">
-- 获取项目全职人员
SELECT * FROM `tuqoa_rydp` 
WHERE `drxmid`=$projectid and `sfqz`='全职' and `state`='在职'
            </div>
            <p><strong>用途：</strong>获取项目人员配置，为人工费用计算提供基础</p>
            
            <h4><span class="tag tag-query">工资</span>tuqoa_hrsalary - 人力资源工资表</h4>
            <div class="code-snippet">
-- 查询员工指定月份工资
SELECT * FROM `tuqoa_hrsalary` 
WHERE `uname`='$employeeName' 
and `month`>='$startDate' and `month`<='$endDate'
            </div>
            <p><strong>用途：</strong>获取员工工资详情，计算人工费用</p>
            
            <h4><span class="tag tag-query">社保</span>tuqoa_xmsjbxmx - 项目社保明细表</h4>
            <div class="code-snippet">
-- 社保费用统计
SELECT ifnull(sum(ylxj),0) ylhj, ifnull(sum(yiliaobxxj),0) yiliaobxxjhj 
FROM `tuqoa_xmsjbxmx` 
WHERE `projectid`=$projectid 
and `ys`>='$startDate-01' and `ys`<='$lastDayOfMonth'
            </div>
            <p><strong>用途：</strong>统计养老、医疗等社保费用</p>
            
            <h4><span class="tag tag-query">核算</span>tuqoa_xmhstjzl - 项目核算统计资料表</h4>
            <div class="code-snippet">
-- 管理费用和其他费用
SELECT * FROM `tuqoa_xmhstjzl` 
WHERE `projectid`=$projectid 
and `sbrq`>='$startDate-01' and `sbrq`<='$lastDayOfMonth'
            </div>
            <p><strong>用途：</strong>获取福利费、管理费、业务费等间接费用</p>
        </div>

        <h2>💰 费用计算体系</h2>

        <div class="analysis-card feature">
            <h3>详细费用分解逻辑</h3>
            
            <div class="expense-breakdown">
                <h4>费用计算流程</h4>
                
                <div class="expense-item">
                    <h5><span class="tag tag-calc">人工费</span>直接人工费用计算</h5>
                    <div class="code-snippet">
// 人工费用统计
$实发工资合计 = 0;
$应发工资合计 = 0;
$伙食费合计 = 0;
$采暖费合计 = 0;

// 获取项目全职人员
$sql1 = "SELECT * FROM `tuqoa_rydp` 
         WHERE `drxmid`=$projectid and `sfqz`='全职' and `state`='在职'";

while ($row1 = mysqli_fetch_assoc($result1)) {
    // 查询每个员工的工资
    $sql2 = "SELECT * FROM `tuqoa_hrsalary` 
             WHERE `uname`='{$row1['dpryxm']}' 
             and `month`>='$startDate' and `month`<='$endDate'";
    
    while ($row2 = mysqli_fetch_assoc($result2)) {
        $实发工资合计 += $row2["sfgz"];    // 实发工资
        $应发工资合计 += $row2["yfgz"];    // 应发工资
        $伙食费合计 += $row2["hsf"];       // 伙食费
        $采暖费合计 += $row2["cnf"];       // 采暖费
    }
}
                    </div>
                </div>
                
                <div class="expense-item">
                    <h5><span class="tag tag-calc">社保费</span>社会保险费用计算</h5>
                    <div class="code-snippet">
// 社保费用统计
$养老合计 = 0;
$医疗报销合计 = 0;

$sql1 = "SELECT ifnull(sum(ylxj),0) ylhj, ifnull(sum(yiliaobxxj),0) yiliaobxxjhj 
         FROM `tuqoa_xmsjbxmx` 
         WHERE `projectid`=$projectid 
         and `ys`>='$startDate-01' and `ys`<='$lastDayOfMonth'";

while ($row1 = mysqli_fetch_assoc($result1)) {
    $养老合计 = $row1["ylhj"];           // 养老保险
    $医疗报销合计 = $row1["yiliaobxxjhj"]; // 医疗保险
}

// 总社保费用
$社保费用合计 = $养老合计 + $医疗报销合计;
                    </div>
                </div>
                
                <div class="expense-item">
                    <h5><span class="tag tag-calc">管理费</span>管理费用和其他费用</h5>
                    <div class="code-snippet">
// 管理费用和其他费用
$福利费合计 = 0;
$管理费合计 = 0;
$业务费合计 = 0;
$利润 = 0;
$税金 = 0;

$sql1 = "SELECT * FROM `tuqoa_xmhstjzl` 
         WHERE `projectid`=$projectid 
         and `sbrq`>='$startDate-01' and `sbrq`<='$lastDayOfMonth'";

while ($row1 = mysqli_fetch_assoc($result1)) {
    $福利费合计 += $row1["flf"];    // 福利费
    $管理费合计 += $row1["glf"];    // 管理费
    $业务费合计 += $row1["ywf"];    // 业务费
    $利润 += $row1["lr"];          // 利润
    $税金 += $row1["sj"];          // 税金
}
                    </div>
                </div>
                
                <div class="expense-item">
                    <h5><span class="tag tag-calc">汇总</span>费用汇总计算</h5>
                    <div class="code-snippet">
// 费用分类汇总
$直接人工费 = $实发工资合计;
$人工福利费 = $伙食费合计 + $采暖费合计 + $福利费合计;
$社会保险费 = $养老合计 + $医疗报销合计;
$管理费用 = $管理费合计;
$业务费用 = $业务费合计;

// 总费用计算
$总费用 = $直接人工费 + $人工福利费 + $社会保险费 + $管理费用 + $业务费用;

// 费用占比计算
$人工费占比 = ($总费用 > 0) ? round(($直接人工费 / $总费用) * 100, 2) : 0;
$福利费占比 = ($总费用 > 0) ? round(($人工福利费 / $总费用) * 100, 2) : 0;
$社保费占比 = ($总费用 > 0) ? round(($社会保险费 / $总费用) * 100, 2) : 0;
                    </div>
                </div>
            </div>
        </div>

        <h2>📊 费用分析指标</h2>

        <div class="analysis-card chart">
            <h3>关键费用指标</h3>
            
            <div class="metric-grid">
                <div class="metric-card">
                    <h4>直接人工费</h4>
                    <div class="metric-value">¥X.XX万</div>
                    <p>员工工资总额</p>
                </div>
                <div class="metric-card">
                    <h4>福利费用</h4>
                    <div class="metric-value">¥X.XX万</div>
                    <p>伙食费+采暖费+福利费</p>
                </div>
                <div class="metric-card">
                    <h4>社保费用</h4>
                    <div class="metric-value">¥X.XX万</div>
                    <p>养老+医疗保险</p>
                </div>
                <div class="metric-card">
                    <h4>管理费用</h4>
                    <div class="metric-value">¥X.XX万</div>
                    <p>管理费+业务费</p>
                </div>
            </div>
            
            <table class="table">
                <thead>
                    <tr>
                        <th>费用类别</th>
                        <th>具体项目</th>
                        <th>数据来源</th>
                        <th>计算方式</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td rowspan="2">直接人工费</td>
                        <td>实发工资</td>
                        <td>tuqoa_hrsalary.sfgz</td>
                        <td>按人员累加</td>
                    </tr>
                    <tr>
                        <td>应发工资</td>
                        <td>tuqoa_hrsalary.yfgz</td>
                        <td>按人员累加</td>
                    </tr>
                    <tr>
                        <td rowspan="3">福利费用</td>
                        <td>伙食费</td>
                        <td>tuqoa_hrsalary.hsf</td>
                        <td>按人员累加</td>
                    </tr>
                    <tr>
                        <td>采暖费</td>
                        <td>tuqoa_hrsalary.cnf</td>
                        <td>按人员累加</td>
                    </tr>
                    <tr>
                        <td>福利费</td>
                        <td>tuqoa_xmhstjzl.flf</td>
                        <td>项目汇总</td>
                    </tr>
                    <tr>
                        <td rowspan="2">社会保险</td>
                        <td>养老保险</td>
                        <td>tuqoa_xmsjbxmx.ylxj</td>
                        <td>项目汇总</td>
                    </tr>
                    <tr>
                        <td>医疗保险</td>
                        <td>tuqoa_xmsjbxmx.yiliaobxxj</td>
                        <td>项目汇总</td>
                    </tr>
                    <tr>
                        <td rowspan="2">管理费用</td>
                        <td>管理费</td>
                        <td>tuqoa_xmhstjzl.glf</td>
                        <td>项目汇总</td>
                    </tr>
                    <tr>
                        <td>业务费</td>
                        <td>tuqoa_xmhstjzl.ywf</td>
                        <td>项目汇总</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <h2>📈 费用分析特色</h2>

        <div class="analysis-card business">
            <h3>精细化费用管理</h3>
            
            <h4><span class="tag tag-detail">特色</span>多维度费用分析</h4>
            <ul>
                <li><strong>人员维度：</strong>按项目人员统计个人费用贡献</li>
                <li><strong>时间维度：</strong>支持指定月份的费用分析</li>
                <li><strong>类别维度：</strong>按费用类型进行详细分解</li>
                <li><strong>占比分析：</strong>各类费用占总费用的比例分析</li>
            </ul>
            
            <h4><span class="tag tag-expense">控制</span>费用控制要点</h4>
            <div class="code-snippet">
// 费用控制分析
function analyzeCostControl($expenseData) {
    $analysis = [];
    
    // 人工费占比分析
    $laborRatio = ($expenseData['labor_cost'] / $expenseData['total_cost']) * 100;
    if ($laborRatio > 70) {
        $analysis[] = [
            'type' => '人工费占比过高',
            'value' => $laborRatio . '%',
            'suggestion' => '考虑优化人员配置或提高工作效率'
        ];
    }
    
    // 福利费占比分析
    $welfareRatio = ($expenseData['welfare_cost'] / $expenseData['total_cost']) * 100;
    if ($welfareRatio > 15) {
        $analysis[] = [
            'type' => '福利费占比较高',
            'value' => $welfareRatio . '%',
            'suggestion' => '检查福利费用的合理性'
        ];
    }
    
    // 管理费占比分析
    $mgmtRatio = ($expenseData['management_cost'] / $expenseData['total_cost']) * 100;
    if ($mgmtRatio > 12) {
        $analysis[] = [
            'type' => '管理费占比过高',
            'value' => $mgmtRatio . '%',
            'suggestion' => '优化管理费用分摊方式'
        ];
    }
    
    return $analysis;
}
            </div>
            
            <h4>费用预算对比</h4>
            <ul>
                <li><strong>预算执行率：</strong>实际费用与预算费用的对比</li>
                <li><strong>费用趋势：</strong>月度费用变化趋势分析</li>
                <li><strong>异常识别：</strong>费用异常波动的识别和分析</li>
                <li><strong>成本效益：</strong>费用投入与产出的效益分析</li>
            </ul>
        </div>

        <h2>🎯 技术实现特点</h2>

        <div class="analysis-card">
            <h3>复杂查询处理</h3>
            
            <h4>查询特点</h4>
            <ul>
                <li><strong>多层嵌套：</strong>项目-人员-工资的三层嵌套查询</li>
                <li><strong>时间筛选：</strong>灵活的月份范围查询</li>
                <li><strong>聚合计算：</strong>多个SUM聚合操作</li>
                <li><strong>中文变量：</strong>使用中文变量名增强可读性</li>
            </ul>
            
            <h4>数据处理特色</h4>
            <ul>
                <li><strong>逐步累加：</strong>通过循环逐步累加各项费用</li>
                <li><strong>分类统计：</strong>按费用类型进行分类统计</li>
                <li><strong>空值处理：</strong>使用IFNULL确保计算准确性</li>
                <li><strong>实时计算：</strong>页面加载时实时计算所有费用</li>
            </ul>
            
            <h4>用户体验特色</h4>
            <ul>
                <li><strong>详细展示：</strong>完整的费用明细展示</li>
                <li><strong>分类清晰：</strong>按费用类型清晰分类</li>
                <li><strong>数据准确：</strong>精确到小数点的计算</li>
                <li><strong>时间灵活：</strong>支持任意月份查询</li>
            </ul>
            
            <div class="warning-box">
                <strong>⚠️ 技术注意事项：</strong>
                <ul>
                    <li>嵌套查询较多，可能影响页面加载速度</li>
                    <li>中文变量名可能在某些环境下有兼容性问题</li>
                    <li>时间范围查询需要注意月份边界处理</li>
                    <li>需要确保所有相关表的数据完整性</li>
                </ul>
            </div>
        </div>

        <h2>🚀 优化建议</h2>

        <div class="analysis-card">
            <h3>性能优化</h3>
            <ul>
                <li>优化嵌套查询，使用JOIN减少查询次数</li>
                <li>为时间字段（month、ys、sbrq）添加索引</li>
                <li>实现费用数据的预计算和缓存</li>
                <li>添加查询结果缓存机制</li>
            </ul>
            
            <h3>功能增强</h3>
            <ul>
                <li>添加费用趋势图表展示</li>
                <li>支持费用数据的导出功能</li>
                <li>增加费用预算对比功能</li>
                <li>添加费用异常预警机制</li>
            </ul>
            
            <h3>用户体验</h3>
            <ul>
                <li>添加数据加载进度指示</li>
                <li>支持费用数据的打印格式</li>
                <li>增加费用占比的可视化展示</li>
                <li>添加费用分析的帮助说明</li>
            </ul>
        </div>

        <div style="text-align: center; margin-top: 40px; color: #7f8c8d;">
            <p>📅 分析日期：2025年8月7日</p>
            <p>📊 页面重要性：⭐⭐⭐⭐ (费用管理核心)</p>
            <p>🔄 建议更新频率：月度</p>
        </div>
    </div>
</body>
</html>
