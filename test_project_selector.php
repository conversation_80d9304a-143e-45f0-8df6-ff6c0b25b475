<?php
include '../config.php';
error_reporting(E_ALL);
ini_set('display_errors', 1);
session_start();

// 设置测试用的session变量
$_SESSION['xinhu_adminid'] = 1;
$_SESSION['xinhu_projectid'] = 1;
$_SESSION['xinhu_project'] = '测试项目';

// 获取项目列表
$projectListSql = "SELECT id, gcname FROM tuqoa_gcproject WHERE xmzt NOT IN ('完工项目','完工已结算','合同终止') ORDER BY id DESC LIMIT 10";
$projectListResult = mysqli_query($link, $projectListSql);
$projectList = [];
if ($projectListResult) {
    while ($projectRow = mysqli_fetch_assoc($projectListResult)) {
        $projectList[] = $projectRow;
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目选择功能测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/boxicons@2.0.7/css/boxicons.min.css" rel="stylesheet">
    <style>
        .test-card {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: white;
        }
        .test-link {
            display: inline-block;
            margin: 10px;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: all 0.3s ease;
        }
        .test-link:hover {
            background: #0056b3;
            color: white;
            text-decoration: none;
            transform: translateY(-2px);
        }
        .status-ok {
            color: #28a745;
            font-weight: bold;
        }
        .status-error {
            color: #dc3545;
            font-weight: bold;
        }
        .project-selector-demo {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .form-select {
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            padding: 8px 12px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        .form-select:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            outline: none;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>项目选择功能测试</h1>
        
        <div class="test-card">
            <h3>数据库连接测试</h3>
            <?php if ($link): ?>
                <p class="status-ok">✓ 数据库连接成功</p>
            <?php else: ?>
                <p class="status-error">✗ 数据库连接失败</p>
            <?php endif; ?>
        </div>
        
        <div class="test-card">
            <h3>项目列表测试</h3>
            <?php if (count($projectList) > 0): ?>
                <p class="status-ok">✓ 找到 <?php echo count($projectList); ?> 个活跃项目</p>
                <div class="project-selector-demo">
                    <label for="demo-project-select">
                        <i class="bx bx-building me-1"></i>项目选择器演示:
                    </label>
                    <select id="demo-project-select" class="form-select" onchange="demoProjectChange()">
                        <option value="">请选择项目</option>
                        <?php foreach ($projectList as $proj): ?>
                            <option value="<?php echo $proj['id']; ?>">
                                <?php echo htmlspecialchars($proj['gcname']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <div id="demo-result" class="mt-3"></div>
                </div>
            <?php else: ?>
                <p class="status-error">✗ 未找到活跃项目</p>
            <?php endif; ?>
        </div>
        
        <div class="test-card">
            <h3>页面链接测试</h3>
            <?php if (count($projectList) > 0): ?>
                <?php $firstProject = $projectList[0]; ?>
                <p>使用第一个项目进行测试：<strong><?php echo htmlspecialchars($firstProject['gcname']); ?></strong></p>
                <a href="myxmfymx.php?projectid=<?php echo $firstProject['id']; ?>" class="test-link" target="_blank">
                    <i class="bx bx-money me-1"></i>测试项目费用明细页面
                </a>
                <a href="myxmcbmx.php?projectid=<?php echo $firstProject['id']; ?>" class="test-link" target="_blank">
                    <i class="bx bx-calculator me-1"></i>测试项目成本明细页面
                </a>
            <?php else: ?>
                <p class="status-error">无法生成测试链接，缺少测试项目</p>
            <?php endif; ?>
        </div>
        
        <div class="test-card">
            <h3>功能特性说明</h3>
            <h5>新增功能：</h5>
            <ul class="list-group list-group-flush">
                <li class="list-group-item">
                    <i class="bx bx-check text-success me-2"></i>
                    <strong>项目选择下拉框：</strong>显示所有活跃项目，支持快速切换
                </li>
                <li class="list-group-item">
                    <i class="bx bx-check text-success me-2"></i>
                    <strong>自动页面刷新：</strong>选择项目后自动跳转并传递项目ID参数
                </li>
                <li class="list-group-item">
                    <i class="bx bx-check text-success me-2"></i>
                    <strong>状态保持：</strong>页面刷新后保持当前选择的项目
                </li>
                <li class="list-group-item">
                    <i class="bx bx-check text-success me-2"></i>
                    <strong>响应式设计：</strong>适配不同屏幕尺寸的设备
                </li>
                <li class="list-group-item">
                    <i class="bx bx-check text-success me-2"></i>
                    <strong>美观界面：</strong>现代化的UI设计和交互效果
                </li>
            </ul>
        </div>
        
        <div class="test-card">
            <h3>使用说明</h3>
            <ol>
                <li><strong>项目选择：</strong>在下拉框中选择要查看的项目</li>
                <li><strong>自动跳转：</strong>选择后页面会自动刷新并加载该项目的数据</li>
                <li><strong>月份查询：</strong>可以选择不同月份查看历史数据</li>
                <li><strong>数据导出：</strong>支持将数据导出为Excel格式</li>
            </ol>
        </div>
        
        <div class="test-card">
            <h3>技术实现</h3>
            <h5>前端技术：</h5>
            <ul>
                <li>Bootstrap 5.1.3 - 响应式UI框架</li>
                <li>Boxicons - 图标库</li>
                <li>JavaScript - 项目切换逻辑</li>
                <li>CSS3 - 现代化样式和动画效果</li>
            </ul>
            
            <h5>后端技术：</h5>
            <ul>
                <li>PHP - 服务器端逻辑</li>
                <li>MySQL - 数据库查询</li>
                <li>Session管理 - 用户状态保持</li>
                <li>参数验证 - 安全性保障</li>
            </ul>
        </div>
        
        <div class="test-card">
            <h3>项目列表详情</h3>
            <?php if (count($projectList) > 0): ?>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>项目ID</th>
                                <th>项目名称</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($projectList as $proj): ?>
                                <tr>
                                    <td><?php echo $proj['id']; ?></td>
                                    <td><?php echo htmlspecialchars($proj['gcname']); ?></td>
                                    <td>
                                        <a href="myxmfymx.php?projectid=<?php echo $proj['id']; ?>" 
                                           class="btn btn-sm btn-primary me-1" target="_blank">费用明细</a>
                                        <a href="myxmcbmx.php?projectid=<?php echo $proj['id']; ?>" 
                                           class="btn btn-sm btn-success" target="_blank">成本明细</a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <p class="text-muted">暂无项目数据</p>
            <?php endif; ?>
        </div>
    </div>
    
    <script>
        function demoProjectChange() {
            const select = document.getElementById('demo-project-select');
            const result = document.getElementById('demo-result');
            const selectedValue = select.value;
            const selectedText = select.options[select.selectedIndex].text;
            
            if (selectedValue) {
                result.innerHTML = `
                    <div class="alert alert-info">
                        <strong>选择的项目：</strong>${selectedText} (ID: ${selectedValue})<br>
                        <strong>生成的URL：</strong><br>
                        <code>myxmfymx.php?projectid=${selectedValue}</code><br>
                        <code>myxmcbmx.php?projectid=${selectedValue}</code>
                    </div>
                `;
            } else {
                result.innerHTML = '';
            }
        }
    </script>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
