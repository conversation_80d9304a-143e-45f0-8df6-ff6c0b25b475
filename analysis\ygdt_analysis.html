<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>员工动态分析</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 10px;
            font-size: 2.5rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .subtitle {
            text-align: center;
            color: #7f8c8d;
            margin-bottom: 30px;
            font-size: 1.1rem;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }
        h3 {
            color: #2980b9;
            margin-top: 20px;
        }
        .analysis-card {
            background: #f8f9fa;
            padding: 20px;
            margin: 15px 0;
            border-radius: 10px;
            border-left: 4px solid #3498db;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .data-source { border-left-color: #28a745; }
        .feature { border-left-color: #ffc107; }
        .chart { border-left-color: #dc3545; }
        .business { border-left-color: #6f42c1; }
        .code-snippet {
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 10px 0;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .table th, .table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
            font-size: 14px;
        }
        .table th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            font-weight: 600;
        }
        .highlight-box {
            background: #e7f3ff;
            border: 1px solid #b8daff;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .tag {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            margin-right: 5px;
        }
        .tag-query { background: #28a745; color: white; }
        .tag-calc { background: #ffc107; color: #212529; }
        .tag-hr { background: #dc3545; color: white; }
        .tag-trend { background: #6f42c1; color: white; }
        .metric-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            margin: 10px 0;
        }
        .hr-flow {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .flow-step {
            background: white;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>👥 员工动态分析</h1>
        <p class="subtitle">ygdt.php - 企业人力资源动态监控和分析中心</p>
        
        <h2>📋 页面功能概述</h2>
        
        <div class="analysis-card business">
            <h3>核心功能</h3>
            <p>员工动态页面是企业人力资源管理的核心工具，提供全面的人员变动分析：</p>
            <ul>
                <li><strong>人员统计分析：</strong>员工总数、入职、转正、晋升等关键指标</li>
                <li><strong>动态趋势监控：</strong>6个月的入职离职趋势分析</li>
                <li><strong>部门人员分布：</strong>各部门人员数量和结构分析</li>
                <li><strong>人员变动明细：</strong>详细的人员变动记录和跟踪</li>
            </ul>
            
            <div class="highlight-box">
                <strong>💡 管理价值：</strong>
                <ul>
                    <li>为HR部门提供人员管理决策依据</li>
                    <li>监控企业人员流动和稳定性</li>
                    <li>支持人力资源规划和配置</li>
                    <li>识别人员流失风险和招聘需求</li>
                </ul>
            </div>
        </div>

        <h2>🗄️ 核心数据来源分析</h2>

        <div class="analysis-card data-source">
            <h3>主要数据表</h3>
            
            <h4><span class="tag tag-query">核心</span>tuqoa_userinfo - 用户信息表</h4>
            <div class="code-snippet">
-- 查询员工总数
SELECT COUNT(*) as zrs FROM `tuqoa_userinfo` where state<>5

-- 查询本月入职人数
SELECT count(*) as byrz FROM `tuqoa_userinfo` 
WHERE `workdate`>='$startDate' and `workdate`<='$endDate'

-- 查询本月转正人数
SELECT count(*) as byzz FROM `tuqoa_userinfo` 
WHERE `positivedt`>='$startDate' and `positivedt`<='$endDate'

-- 查询部门人员分布
SELECT deptname AS bmmc, COUNT(*) AS rshj FROM tuqoa_userinfo 
where state=1 GROUP BY deptname ORDER BY rshj DESC
            </div>
            <p><strong>用途：</strong>员工信息的唯一数据源，包含入职、转正、离职等关键信息</p>
        </div>

        <h2>📊 人员动态统计逻辑</h2>

        <div class="analysis-card feature">
            <h3>多维度人员统计</h3>
            
            <div class="hr-flow">
                <h4>人员统计流程</h4>
                
                <div class="flow-step">
                    <h5><span class="tag tag-calc">基础</span>基础人员统计</h5>
                    <div class="code-snippet">
// 员工总数统计
$zrs = 0;
$sql = "SELECT COUNT(*) as zrs FROM `tuqoa_userinfo` where state<>5";
$result = mysqli_query($link, $sql);
if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        $zrs = $row["zrs"];
    }
}

// 本月入职人数
$byrz = 0;
$sql = "SELECT count(*) as byrz FROM `tuqoa_userinfo` 
        WHERE `workdate`>='$startDate' and `workdate`<='$endDate'";

// 本月转正人数
$byzz = 0;
$sql = "SELECT count(*) as byzz FROM `tuqoa_userinfo` 
        WHERE `positivedt`>='$startDate' and `positivedt`<='$endDate'";
                    </div>
                </div>
                
                <div class="flow-step">
                    <h5><span class="tag tag-trend">趋势</span>6个月人员变动趋势</h5>
                    <div class="code-snippet">
// 复杂的UNION查询获取入职和离职趋势
$sql = "SELECT DATE_FORMAT(workdate, '%Y-%m') AS month, 
               COUNT(*) AS new_employees, 
               0 AS quit_employees 
        FROM tuqoa_userinfo 
        WHERE workdate >= DATE_SUB(CURRENT_DATE(), INTERVAL 6 MONTH) 
        GROUP BY DATE_FORMAT(workdate, '%Y-%m') 
        
        UNION ALL 
        
        SELECT DATE_FORMAT(quitdt, '%Y-%m') AS month, 
               0 AS new_employees, 
               COUNT(*) AS quit_employees 
        FROM tuqoa_userinfo 
        WHERE quitdt >= DATE_SUB(CURRENT_DATE(), INTERVAL 6 MONTH) 
        AND quitdt IS NOT NULL 
        GROUP BY DATE_FORMAT(quitdt, '%Y-%m') 
        ORDER BY month";

// 数据处理逻辑
$monthlyData = [];
while ($row = mysqli_fetch_assoc($result)) {
    $month = $row['month'];
    if (!isset($monthlyData[$month])) {
        $monthlyData[$month] = ['new' => 0, 'quit' => 0];
    }
    $monthlyData[$month]['new'] += $row['new_employees'];
    $monthlyData[$month]['quit'] += $row['quit_employees'];
}
                    </div>
                </div>
                
                <div class="flow-step">
                    <h5><span class="tag tag-hr">部门</span>部门人员分布统计</h5>
                    <div class="code-snippet">
// 部门人员分布查询
$bmmchj = [];
$bmrshj = [];
$sql = "SELECT deptname AS bmmc, COUNT(*) AS rshj FROM tuqoa_userinfo 
        where state=1 GROUP BY deptname ORDER BY rshj DESC";

$result = mysqli_query($link, $sql);
while ($row = mysqli_fetch_assoc($result)) {
    $bmmchj[] = $row["bmmc"];    // 部门名称数组
    $bmrshj[] = $row["rshj"];    // 部门人数数组
}

// 用于Chart.js的数据格式
$departmentLabels = json_encode($bmmchj);
$departmentData = json_encode($bmrshj);
                    </div>
                </div>
                
                <div class="flow-step">
                    <h5><span class="tag tag-hr">明细</span>人员变动明细查询</h5>
                    <div class="code-snippet">
// 查询本月人员变动明细
$sql = "SELECT * FROM `tuqoa_userinfo` 
        WHERE (`workdate`>='$startDate' and `workdate`<='$endDate') 
        or (`quitdt`>='$startDate' and `quitdt`<='$endDate' and `quitdt` IS NOT NULL)";

$result = mysqli_query($link, $sql);

// 显示逻辑
if ($result && mysqli_num_rows($result) > 0) {
    while ($row = mysqli_fetch_assoc($result)) {
        // 判断是入职还是离职
        $action = '';
        if (!empty($row['workdate']) && 
            $row['workdate'] >= $startDate && 
            $row['workdate'] <= $endDate) {
            $action = '入职';
        } elseif (!empty($row['quitdt']) && 
                  $row['quitdt'] >= $startDate && 
                  $row['quitdt'] <= $endDate) {
            $action = '离职';
        }
        
        // 显示员工信息和变动类型
    }
}
                    </div>
                </div>
            </div>
        </div>

        <h2>📈 关键人力资源指标</h2>

        <div class="analysis-card chart">
            <h3>核心HR指标体系</h3>
            
            <div class="metric-grid">
                <div class="metric-card">
                    <h4>员工总数</h4>
                    <div class="metric-value">XXX人</div>
                    <p>在职员工总数</p>
                </div>
                <div class="metric-card">
                    <h4>本月入职</h4>
                    <div class="metric-value">XX人</div>
                    <p>新增员工数量</p>
                </div>
                <div class="metric-card">
                    <h4>本月转正</h4>
                    <div class="metric-value">XX人</div>
                    <p>试用期转正</p>
                </div>
                <div class="metric-card">
                    <h4>本月晋升</h4>
                    <div class="metric-value">XX人</div>
                    <p>职位晋升人数</p>
                </div>
            </div>
            
            <table class="table">
                <thead>
                    <tr>
                        <th>指标类别</th>
                        <th>计算方式</th>
                        <th>数据来源</th>
                        <th>业务意义</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>员工总数</td>
                        <td>COUNT(*) WHERE state<>5</td>
                        <td>tuqoa_userinfo.state</td>
                        <td>企业人力规模</td>
                    </tr>
                    <tr>
                        <td>入职人数</td>
                        <td>按workdate统计</td>
                        <td>tuqoa_userinfo.workdate</td>
                        <td>人员增长情况</td>
                    </tr>
                    <tr>
                        <td>转正人数</td>
                        <td>按positivedt统计</td>
                        <td>tuqoa_userinfo.positivedt</td>
                        <td>试用期通过率</td>
                    </tr>
                    <tr>
                        <td>离职人数</td>
                        <td>按quitdt统计</td>
                        <td>tuqoa_userinfo.quitdt</td>
                        <td>人员流失情况</td>
                    </tr>
                    <tr>
                        <td>部门分布</td>
                        <td>按deptname分组统计</td>
                        <td>tuqoa_userinfo.deptname</td>
                        <td>组织结构分析</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <h2>📊 数据可视化特色</h2>

        <div class="analysis-card chart">
            <h3>多维度图表展示</h3>
            
            <h4><span class="tag tag-trend">图表</span>6个月人员变动趋势</h4>
            <div class="code-snippet">
// 人员变动趋势线图
var trendChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: monthLabels,
        datasets: [{
            label: '入职人数',
            data: newEmployeesData,
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1
        }, {
            label: '离职人数',
            data: quitEmployeesData,
            borderColor: 'rgb(255, 99, 132)',
            backgroundColor: 'rgba(255, 99, 132, 0.2)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        interaction: {
            mode: 'index',
            intersect: false,
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    stepSize: 1
                }
            }
        }
    }
});
            </div>
            
            <h4><span class="tag tag-hr">图表</span>部门人员分布图</h4>
            <div class="code-snippet">
// 部门人员分布饼图
var departmentChart = new Chart(ctx, {
    type: 'pie',
    data: {
        labels: departmentLabels,
        datasets: [{
            data: departmentData,
            backgroundColor: [
                'rgba(255, 99, 132, 0.8)',
                'rgba(54, 162, 235, 0.8)',
                'rgba(255, 205, 86, 0.8)',
                'rgba(75, 192, 192, 0.8)',
                'rgba(153, 102, 255, 0.8)',
                'rgba(255, 159, 64, 0.8)'
            ]
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        var percentage = ((context.parsed / totalEmployees) * 100).toFixed(1);
                        return context.label + ': ' + context.parsed + 
                               '人 (' + percentage + '%)';
                    }
                }
            }
        }
    }
});
            </div>
        </div>

        <h2>💼 业务逻辑特色</h2>

        <div class="analysis-card business">
            <h3>智能人员分析</h3>
            
            <h4><span class="tag tag-hr">分析</span>人员流动性分析</h4>
            <div class="code-snippet">
// 人员流动性分析逻辑
function analyzeStaffTurnover($monthlyData) {
    $analysis = [];
    
    foreach ($monthlyData as $month => $data) {
        $newCount = $data['new'];
        $quitCount = $data['quit'];
        $netChange = $newCount - $quitCount;
        
        // 流动率计算
        $turnoverRate = ($quitCount / $totalEmployees) * 100;
        
        // 净增长率
        $netGrowthRate = ($netChange / $totalEmployees) * 100;
        
        $analysis[$month] = [
            'new_employees' => $newCount,
            'quit_employees' => $quitCount,
            'net_change' => $netChange,
            'turnover_rate' => round($turnoverRate, 2),
            'net_growth_rate' => round($netGrowthRate, 2)
        ];
        
        // 预警判断
        if ($turnoverRate > 5) {
            $analysis[$month]['warning'] = '离职率偏高';
        }
        if ($netChange < -3) {
            $analysis[$month]['warning'] = '人员净流失严重';
        }
    }
    
    return $analysis;
}
            </div>
            
            <h4>人员管理预警机制</h4>
            <ul>
                <li><strong>🟢 正常：</strong>月度离职率 < 3%，人员稳定</li>
                <li><strong>🟡 注意：</strong>月度离职率 3-5%，需要关注</li>
                <li><strong>🟠 警告：</strong>月度离职率 5-8%，需要分析原因</li>
                <li><strong>🔴 严重：</strong>月度离职率 > 8%，需要立即处理</li>
            </ul>
        </div>

        <h2>🎯 技术实现特点</h2>

        <div class="analysis-card">
            <h3>代码架构特色</h3>
            
            <h4>查询优化特点</h4>
            <ul>
                <li><strong>复杂UNION查询：</strong>巧妙使用UNION合并入职离职数据</li>
                <li><strong>时间函数应用：</strong>使用DATE_FORMAT进行月份分组</li>
                <li><strong>状态筛选：</strong>通过state字段筛选有效员工</li>
                <li><strong>聚合统计：</strong>使用COUNT和GROUP BY进行统计</li>
            </ul>
            
            <h4>数据处理特色</h4>
            <ul>
                <li><strong>数组处理：</strong>将查询结果转换为JavaScript数组</li>
                <li><strong>JSON编码：</strong>使用json_encode传递数据到前端</li>
                <li><strong>条件判断：</strong>智能判断员工变动类型</li>
                <li><strong>实时计算：</strong>基于当前月份动态计算统计</li>
            </ul>
            
            <h4>用户体验特色</h4>
            <ul>
                <li><strong>多图表展示：</strong>趋势图和饼图结合</li>
                <li><strong>明细表格：</strong>详细的人员变动记录</li>
                <li><strong>响应式设计：</strong>适配不同设备</li>
                <li><strong>实时更新：</strong>数据实时反映最新状况</li>
            </ul>
            
            <div class="warning-box">
                <strong>⚠️ 技术注意事项：</strong>
                <ul>
                    <li>UNION查询可能影响性能，建议优化</li>
                    <li>DATE_FORMAT函数需要注意索引优化</li>
                    <li>大量员工数据时建议添加分页</li>
                    <li>需要确保日期字段的数据质量</li>
                </ul>
            </div>
        </div>

        <h2>🚀 优化建议</h2>

        <div class="analysis-card">
            <h3>性能优化</h3>
            <ul>
                <li>为时间字段（workdate、positivedt、quitdt）添加索引</li>
                <li>优化UNION查询，考虑使用临时表</li>
                <li>实现人员统计数据的缓存</li>
                <li>添加数据分页功能</li>
            </ul>
            
            <h3>功能增强</h3>
            <ul>
                <li>添加人员流失原因分析</li>
                <li>支持自定义时间范围分析</li>
                <li>增加人员绩效关联分析</li>
                <li>添加人员成本分析功能</li>
            </ul>
            
            <h3>用户体验</h3>
            <ul>
                <li>添加人员搜索和筛选功能</li>
                <li>支持数据导出功能</li>
                <li>增加人员详情钻取</li>
                <li>添加移动端适配</li>
            </ul>
        </div>

        <div style="text-align: center; margin-top: 40px; color: #7f8c8d;">
            <p>📅 分析日期：2025年8月7日</p>
            <p>📊 页面重要性：⭐⭐⭐⭐ (人力资源核心)</p>
            <p>🔄 建议更新频率：实时</p>
        </div>
    </div>
</body>
</html>
