<!DOCTYPE html>
<html lang="zh-CN">
<?php
// 开启错误报告以便调试
error_reporting(E_ALL);
ini_set('display_errors', 1);

include '../config.php';

// 检查数据库连接
if (!$link) {
    die("数据库连接失败: " . mysqli_connect_error());
}

// 定义必要的函数，避免页面空白
if (!function_exists('getconfig')) {
    function getconfig($key, $default = '') {
        // 简化的配置函数，返回默认值
        $configs = array(
            'apptheme' => '#1389D3'
        );
        return isset($configs[$key]) ? $configs[$key] : $default;
    }
}

if (!function_exists('c')) {
    function c($name) {
        // 简化的插件调用函数
        if ($name == 'image') {
            return new SimpleImageHelper();
        }
        return null;
    }
}

// 简化的图像助手类
class SimpleImageHelper {
    public function colorTorgb($color) {
        if (!empty($color) && (strlen($color) == 7)) {
            $r = hexdec(substr($color, 1, 2));
            $g = hexdec(substr($color, 3, 2));
            $b = hexdec(substr($color, 5));
        } else {
            $r = $g = $b = 0;
        }
        return array($r, $g, $b);
    }
}

$firstDayOfMonth = date('Y-m-01');
$lastDayOfMonth = date('Y-m-t');
$startDate = isset($_POST['start-date']) ? $_POST['start-date'] : $firstDayOfMonth;
$endDate = isset($_POST['end-date']) ? $_POST['end-date'] : $lastDayOfMonth;
$gcid = isset($_POST['gcid']) ? $_POST['gcid'] : '';
$selectedYear = isset($_POST['year-select']) ? $_POST['year-select'] : date('Y');
// 初始化日期变量
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
        // 验证日期
    if (strtotime($startDate) > strtotime($endDate)) {
        echo '<div class="result" style="background-color: #fde8e8;">错误：开始日期不能晚于结束日期</div>';
    } else {
        // 格式化日期用于显示
        $displayStart = date('Y年m月d日', strtotime($startDate));
        $displayEnd = date('Y年m月d日', strtotime($endDate));
        $daysDiff = (strtotime($endDate) - strtotime($startDate)) / (60 * 60 * 24) + 1;
        
       
        
        // 添加本月信息
        $currentMonth = date('Y年m月');
        $monthDays = date('t', strtotime($firstDayOfMonth));
       
    }
}
?>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目成本核算 - 公司数据总览系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/boxicons@2.0.7/css/boxicons.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="styles/main.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        /* 页面特定样式 - 与test_styles.html风格一致 */
        .chart-container {
            height: 300px;
            position: relative;
            margin: 1rem 0;
        }

        /* 成本趋势指示器 */
        .cost-trend-up {
            color: #dc3545;
        }

        .cost-trend-down {
            color: #28a745;
        }

        .cost-trend-stable {
            color: #007bff;
        }

        /* 进度条样式 */
        .progress {
            height: 1.5rem;
            border-radius: 0.75rem;
            background-color: #e9ecef;
        }

        .progress-bar {
            font-size: 0.75rem;
            font-weight: 600;
            border-radius: 0.75rem;
        }

        /* 刷新时间样式 */
        .refresh-time {
            font-size: 0.8rem;
            color: #6c757d;
        }

        /* 紧凑布局样式 */
        .compact-chart {
            height: 220px;
        }

        .mini-chart {
            height: 180px;
        }

        /* 优化的筛选器样式 - 同行布局 */
        .filter-row-container {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border: 2px solid #dee2e6;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 25px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
            position: relative;
            overflow: hidden;
        }

        .filter-row-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(135deg, #667eea, #764ba2);
        }

        .filter-inline-group {
            display: flex;
            align-items: center;
            gap: 25px;
            flex-wrap: wrap;
        }

        .filter-item-inline {
            display: flex;
            align-items: center;
            gap: 10px;
            white-space: nowrap;
        }

        .filter-item-inline label {
            color: #495057;
            font-weight: 600;
            font-size: 14px;
            margin: 0;
            white-space: nowrap;
            min-width: fit-content;
        }

        .filter-item-inline .form-select {
            padding: 8px 12px;
            border: 2px solid #ced4da;
            border-radius: 8px;
            font-family: inherit;
            transition: all 0.3s ease;
            background: white;
            color: #495057;
            font-weight: 500;
            min-width: 160px;
            font-size: 14px;
        }

        .filter-item-inline .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            outline: none;
        }

        .filter-item-inline .form-select:hover {
            border-color: #667eea;
        }

        .filter-actions {
            display: flex;
            gap: 12px;
            align-items: center;
            margin-left: auto;
        }

        .filter-actions .btn {
            padding: 8px 16px;
            font-weight: 600;
            border-radius: 8px;
            transition: all 0.3s ease;
            white-space: nowrap;
            font-size: 14px;
        }

        .filter-actions .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .filter-actions .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
        }

        .filter-actions .btn-primary:hover {
            background: linear-gradient(135deg, #764ba2, #667eea);
        }

        .filter-actions .btn-secondary {
            background: linear-gradient(135deg, #6c757d, #495057);
            border: none;
        }

        .filter-actions .btn-secondary:hover {
            background: linear-gradient(135deg, #495057, #6c757d);
        }

        /* 状态列不换行样式 */
        .status-column-nowrap {
            white-space: nowrap !important;
            text-align: center;
            padding: 8px 4px !important;
            min-width: 100px;
        }

        .status-badge-compact {
            font-size: 0.7rem;
            padding: 0.3em 0.6em;
            border-radius: 0.3rem;
            font-weight: 600;
            white-space: nowrap;
            display: inline-block;
            transition: all 0.2s ease;
        }

        .status-badge-compact:hover {
            transform: scale(1.05);
        }

        /* 状态徽章颜色 */
        .status-badge-compact.status-normal,
        .status-badge-compact.status-success {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }

        .status-badge-compact.status-warning {
            background: linear-gradient(135deg, #ffc107, #fd7e14);
            color: #212529;
        }

        .status-badge-compact.status-danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
        }

        /* 表格样式优化 */
        .table th {
            background-color: #f8f9fa;
            font-size: 0.875rem;
            padding: 0.75rem 0.5rem;
            white-space: nowrap;
        }

        .table td {
            padding: 0.75rem 0.5rem;
            font-size: 0.875rem;
            vertical-align: middle;
        }

        .table-hover tbody tr:hover {
            background-color: rgba(0, 123, 255, 0.05);
        }

        /* 响应式调整 */
        @media (max-width: 992px) {
            .filter-inline-group {
                flex-direction: column;
                align-items: stretch;
                gap: 20px;
            }

            .filter-item-inline {
                justify-content: space-between;
                width: 100%;
            }

            .filter-item-inline .form-select {
                min-width: 200px;
                flex: 1;
                max-width: 300px;
            }

            .filter-actions {
                justify-content: center;
                margin-left: 0;
                width: 100%;
            }

            .filter-actions .btn {
                flex: 1;
                max-width: 150px;
            }
        }

        @media (max-width: 576px) {
            .filter-row-container {
                padding: 15px;
            }

            .filter-item-inline {
                flex-direction: column;
                align-items: stretch;
                gap: 8px;
            }

            .filter-item-inline label {
                text-align: center;
            }

            .filter-item-inline .form-select {
                min-width: auto;
                width: 100%;
                max-width: none;
            }

            .filter-actions {
                flex-direction: column;
                gap: 10px;
            }

            .filter-actions .btn {
                max-width: none;
                width: 100%;
            }

            .status-column-nowrap {
                min-width: 80px;
                padding: 6px 2px !important;
            }

            .status-badge-compact {
                font-size: 0.6rem;
                padding: 0.25em 0.4em;
            }

            .status-column-nowrap {
                min-width: 80px;
                padding: 6px 2px !important;
            }

            .status-badge-compact {
                font-size: 0.6rem;
                padding: 0.25em 0.4em;
            }
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bx bx-calculator me-2"></i>
                项目成本核算
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text text-white">
                    <i class="bx bx-time me-1"></i>
                    最后更新: <span id="last-update-time"><?php echo date('Y-m-d H:i:s'); ?></span>
                </span>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- 优化的筛选器容器 - 同行布局 -->
        <form method="post" action="">
            <div class="filter-row-container">
                <div class="filter-inline-group">
                    <div class="filter-item-inline">
                        <label for="project-select">
                            <i class="fas fa-project-diagram me-2"></i>
                            项目:
                        </label>
                        <select id="project-select" class="form-select" name="gcid">
                            <option value="">全部项目</option>
                            <?php
                            $gcid = isset($_POST['gcid']) ? $_POST['gcid'] : '';
                            $sql="SELECT * FROM `tuqoa_gcproject` WHERE `xmzt` not in ('完工项目','完工已结算','合同终止') order by id desc";
                            $result = mysqli_query($link, $sql);
                            if ($result) {
                                while ($row = mysqli_fetch_assoc($result)) {
                                    $selected = ($gcid == $row["id"]) ? 'selected' : '';
                                    echo '<option value="'.$row["id"].'" '.$selected.'>'.$row["gcname"].'</option>';
                                }
                            }
                            ?>
                        </select>
                    </div>

                    <div class="filter-item-inline">
                        <label for="year-select">
                            <i class="fas fa-calendar-alt me-2"></i>
                            年份:
                        </label>
                        <select name="year-select" id="year-select" class="form-select">
                            <?php
                            $currentYear = date('Y');
                            $selectedYear = isset($_POST['year-select']) ? $_POST['year-select'] : $currentYear;
                            for ($i = 0; $i < 5; $i++) {
                                $year = $currentYear - $i;
                                $selected = ($selectedYear == $year) ? 'selected' : '';
                                echo '<option value="'.$year.'" '.$selected.'>'.$year.'年</option>';
                            }
                            ?>
                        </select>
                    </div>

                    <div class="filter-actions">
                        <button type="submit" id="query-btn" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>
                            查询数据
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="resetFilters()">
                            <i class="fas fa-undo me-2"></i>
                            重置
                        </button>
                    </div>
                </div>
            </div>
        </form>
            
            
            <div class="row">
                <?php
                // 添加项目筛选条件
                $project_filter = '';
                if (!empty($gcid)) {
                    $project_filter = " AND id = '$gcid'";
                }

                // 添加年份筛选条件
                $year_filter = '';
                if (!empty($selectedYear)) {
                    $year_filter = " AND YEAR(jcsj) = '$selectedYear'";
                }

                // 查询合同总额 (基于选择的年份和项目)
                $sql="SELECT COALESCE(SUM(zaojia), 0) as htzje FROM `tuqoa_gcproject` WHERE 1=1 $project_filter $year_filter";
                $result = mysqli_query($link, $sql);
                $htzje = 0;
                if ($result) {
                    $row = mysqli_fetch_assoc($result);
                    $htzje = round($row["htzje"], 2);
                } else {
                    echo "<!-- 查询错误: " . mysqli_error($link) . " -->";
                }
                ?>
                <div class="col-md-3">
                    <div class="card stat-card stat-card-primary">
                        <div class="card-body">
                            <i class="fas fa-file-contract stat-icon"></i>
                            <h5 class="card-title">合同总额</h5>
                            <h2 class="card-text">¥<?php echo $htzje; ?>万</h2>
                            <p class="stat-info">当期合同总额</p>
                        </div>
                    </div>
                </div>

                <?php
                // 查询收费总额
                $htgl_filter = '';
                if (!empty($gcid)) {
                    $htgl_filter = " AND projectid = '$gcid'";
                }

                // 添加年份筛选
                $htgl_year_filter = '';
                if (!empty($selectedYear)) {
                    $htgl_year_filter = " AND YEAR(qdsj) = '$selectedYear'";
                }

                $sql="SELECT COALESCE(SUM(fwf), 0) as sfzje FROM `tuqoa_htgl` WHERE 1=1 $htgl_filter $htgl_year_filter";
                $result = mysqli_query($link, $sql);
                $sfzje = 0;
                if ($result) {
                    $row = mysqli_fetch_assoc($result);
                    $sfzje = round($row["sfzje"], 2);
                } else {
                    echo "<!-- 查询错误: " . mysqli_error($link) . " -->";
                }
                ?>
                <div class="col-md-3">
                    <div class="card stat-card stat-card-success">
                        <div class="card-body">
                            <i class="fas fa-money-bill-wave stat-icon"></i>
                            <h5 class="card-title">收费总额</h5>
                            <h2 class="card-text">¥<?php echo $sfzje; ?>万</h2>
                            <p class="stat-info">当期收费总额</p>
                        </div>
                    </div>
                </div>

                <?php
                // 查询实际成本总额
                $cost_filter = '';
                if (!empty($gcid)) {
                    $cost_filter = " AND projectid = '$gcid'";
                }

                // 添加年份筛选
                $cost_year_filter = '';
                if (!empty($selectedYear)) {
                    $cost_year_filter = " AND YEAR(sbrq) = '$selectedYear'";
                }

                $sql="SELECT COALESCE(SUM(wccz), 0) as sjcbzje FROM `tuqoa_xmcztjb` WHERE 1=1 $cost_filter $cost_year_filter";
                $result = mysqli_query($link, $sql);
                $sjcbzje = 0;
                if ($result) {
                    $row = mysqli_fetch_assoc($result);
                    $sjcbzje = round($row["sjcbzje"], 2);
                } else {
                    echo "<!-- 查询错误: " . mysqli_error($link) . " -->";
                }

                // 计算成本控制率 (实际成本/收费总额*100%)
                $cbkzl = ($sfzje > 0) ? round(($sjcbzje / $sfzje) * 100, 1) : 0;
                ?>
                <div class="col-md-3">
                    <div class="card stat-card stat-card-warning">
                        <div class="card-body">
                            <i class="fas fa-calculator stat-icon"></i>
                            <h5 class="card-title">实际成本总额</h5>
                            <h2 class="card-text">¥<?php echo $sjcbzje; ?>万</h2>
                            <p class="stat-info">当期实际成本</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card stat-card <?php echo ($cbkzl <= 80) ? 'stat-card-success' : 'stat-card-danger'; ?>">
                        <div class="card-body">
                            <i class="fas fa-chart-line stat-icon"></i>
                            <h5 class="card-title">成本控制率</h5>
                            <h2 class="card-text"><?php echo $cbkzl; ?>%</h2>
                            <p class="stat-info">
                                <?php echo ($cbkzl <= 80) ? '控制良好' : '需要关注'; ?>
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">月度成本趋势</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="monthlyCostChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">月度收入vs成本对比</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="incomeVsCostChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">项目成本明细表</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>项目名称</th>
                                            <th>项目识别号</th>
                                            <th>财务识别号</th>
                                            <th>合同额(万)</th>
                                            <th>收费额(万)</th>
                                            <th>成本系数</th>
                                            <th>计划成本(万)</th>
                                            <th>实际成本(万)</th>
                                            <th>工资总额(万)</th>
                                            <th>工资占比</th>
                                            <th>报销比例</th>
                                            <th>成本控制率</th>
                                            <th>月平均计划成本(万)</th>
                                            <th style="width: 100px; min-width: 100px;">状态</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        $detail_filter = '';
                                        if (!empty($gcid)) {
                                            $detail_filter = " AND id = '$gcid'";
                                        }
                                        $sql="SELECT * FROM `tuqoa_gcproject` WHERE xmzt in ('新开工项目','在建项目','完工未结算') $detail_filter order by id desc";
                                        $result = mysqli_query($link, $sql);
                                        if ($result) {
                                            while ($row = mysqli_fetch_assoc($result)) {
                                                // 计算成本相关数据
                                                $htje = $row["zaojia"]; // 合同额
                                                $sfje = 0; // 收费额
                                                $sjcb = 0; // 实际成本
                                                $gzze = 0; // 工资总额

                                                // 查询收费额
                                                $sql1="SELECT COALESCE(SUM(fwf), 0) as sfje FROM `tuqoa_htgl` WHERE `projectid`='".$row["id"]."'";
                                                $result1 = mysqli_query($link, $sql1);
                                                if ($result1) {
                                                    $row1 = mysqli_fetch_assoc($result1);
                                                    $sfje = $row1["sfje"];
                                                }

                                                // 查询实际成本
                                                $sql2="SELECT COALESCE(SUM(wccz), 0) as sjcb FROM `tuqoa_xmcztjb` WHERE `projectid`='".$row["id"]."'";
                                                $result2 = mysqli_query($link, $sql2);
                                                if ($result2) {
                                                    $row2 = mysqli_fetch_assoc($result2);
                                                    $sjcb = $row2["sjcb"];
                                                }

                                                // 计算各种比率
                                                $cbxs = ($sfje > 0) ? round($sjcb / $sfje, 2) : 0; // 成本系数
                                                $jhcb = $sfje * 0.8; // 假设计划成本为收费额的80%
                                                $gzzb = ($sjcb > 0) ? round(($gzze / $sjcb) * 100, 1) : 0; // 工资占比
                                                $cbkzl = ($jhcb > 0) ? round(($sjcb / $jhcb) * 100, 1) : 0; // 成本控制率
                                                $ypjjhcb = ($jhcb > 0) ? $jhcb / 12 : 0; // 月平均计划成本

                                                // 状态判断
                                                $status = "正常";
                                                $statusClass = "status-normal";
                                                if ($cbkzl > 100) {
                                                    $status = "超支";
                                                    $statusClass = "status-danger";
                                                } elseif ($cbkzl > 90) {
                                                    $status = "预警";
                                                    $statusClass = "status-warning";
                                                }
                                        ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($row["gcname"]); ?></td>
                                            <td>PRJ-<?php echo $row["id"]; ?></td>
                                            <td>FIN-<?php echo $row["id"]; ?></td>
                                            <td><?php echo number_format($htje, 2); ?></td>
                                            <td><?php echo number_format($sfje, 2); ?></td>
                                            <td><?php echo $cbxs; ?></td>
                                            <td><?php echo number_format($jhcb, 2); ?></td>
                                            <td><?php echo number_format($sjcb, 2); ?></td>
                                            <td><?php echo number_format($gzze, 2); ?></td>
                                            <td><?php echo $gzzb; ?>%</td>
                                            <td>--</td>
                                            <td><?php echo $cbkzl; ?>%</td>
                                            <td><?php echo number_format($ypjjhcb, 2); ?></td>
                                            <td class="status-column-nowrap"><span class="status-badge-compact <?php echo $statusClass; ?>"><?php echo $status; ?></span></td>
                                        </tr>
                                        <?php
                                            }
                                        } else {
                                            echo "<tr><td colspan='14'>查询错误: " . mysqli_error($link) . "</td></tr>";
                                        }
                                        ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">成本控制率趋势</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="costControlChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">项目状态分布</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="projectStatusChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">项目分布图表</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="projectDistributionChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">项目成本对比分析</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="costSummaryChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <?php
    // 生成图表数据
    $monthlyLabels = [];
    $monthlyCostData = [];
    $monthlyPlanData = [];

    // 生成最近12个月的数据
    for ($i = 11; $i >= 0; $i--) {
        $month = date('Y-m', strtotime("-$i months"));
        $monthLabel = date('n', strtotime("-$i months")) . '月';
        $monthlyLabels[] = $monthLabel;

        // 查询该月实际成本
        $monthly_filter = '';
        if (!empty($gcid)) {
            $monthly_filter = " AND projectid = '$gcid'";
        }
        $sql_cost = "SELECT COALESCE(SUM(wccz), 0) as monthly_cost FROM tuqoa_xmcztjb WHERE DATE_FORMAT(sbrq, '%Y-%m') = '$month' $monthly_filter";
        $result_cost = mysqli_query($link, $sql_cost);
        $monthly_cost = 0;
        if ($result_cost) {
            $row_cost = mysqli_fetch_assoc($result_cost);
            $monthly_cost = (float)$row_cost['monthly_cost'];
        }
        $monthlyCostData[] = $monthly_cost;

        // 计划成本（假设为实际成本的1.2倍）
        $monthlyPlanData[] = $monthly_cost * 1.2;
    }

    // 项目状态分布数据
    $projectStatusData = [];
    $projectStatusLabels = [];
    $status_filter = '';
    if (!empty($gcid)) {
        $status_filter = " AND id = '$gcid'";
    }
    $sql_status = "SELECT xmzt, COUNT(*) as count FROM tuqoa_gcproject WHERE 1=1 $status_filter GROUP BY xmzt";
    $result_status = mysqli_query($link, $sql_status);
    if ($result_status) {
        while ($row_status = mysqli_fetch_assoc($result_status)) {
            $projectStatusLabels[] = $row_status['xmzt'];
            $projectStatusData[] = (int)$row_status['count'];
        }
    }

    // 项目分布数据 (按地区或部门分布)
    $projectDistributionData = [];
    $projectDistributionLabels = [];

    // 先尝试按地区分布
    $sql_region = "SELECT bumen, COUNT(*) as count FROM tuqoa_gcproject WHERE bumen IS NOT NULL AND bumen != '' $status_filter GROUP BY bumen ORDER BY count DESC LIMIT 6";
    $result_region = mysqli_query($link, $sql_region);
    if ($result_region && mysqli_num_rows($result_region) > 0) {
        while ($row_region = mysqli_fetch_assoc($result_region)) {
            $projectDistributionLabels[] = $row_region['bumen'];
            $projectDistributionData[] = (int)$row_region['count'];
        }
    } else {
        // 如果没有部门数据，按项目状态分布
        $sql_status_dist = "SELECT xmzt, COUNT(*) as count FROM tuqoa_gcproject WHERE 1=1 $status_filter GROUP BY xmzt ORDER BY count DESC";
        $result_status_dist = mysqli_query($link, $sql_status_dist);
        if ($result_status_dist) {
            while ($row_status_dist = mysqli_fetch_assoc($result_status_dist)) {
                $projectDistributionLabels[] = $row_status_dist['xmzt'];
                $projectDistributionData[] = (int)$row_status_dist['count'];
            }
        }
    }

    // 如果仍然没有数据，使用默认数据
    if (empty($projectDistributionData)) {
        $projectDistributionLabels = ['在建项目', '新开工项目', '完工未结算', '暂停项目'];
        $projectDistributionData = [45, 30, 20, 5];
    }

    // 月度收入vs成本对比数据
    $monthlyIncomeData = [];
    $monthlyCostCompareData = [];
    $monthlyCompareLabels = [];

    // 生成最近6个月的收入vs成本对比数据
    for ($i = 5; $i >= 0; $i--) {
        $month = date('Y-m', strtotime("-$i months"));
        $monthLabel = date('n', strtotime("-$i months")) . '月';
        $monthlyCompareLabels[] = $monthLabel;

        // 查询该月收入
        $income_filter = '';
        if (!empty($gcid)) {
            $income_filter = " AND projectid = '$gcid'";
        }
        $sql_income = "SELECT COALESCE(SUM(fwf), 0) as monthly_income FROM tuqoa_htgl WHERE DATE_FORMAT(qdsj, '%Y-%m') = '$month' $income_filter";
        $result_income = mysqli_query($link, $sql_income);
        $monthly_income = 0;
        if ($result_income) {
            $row_income = mysqli_fetch_assoc($result_income);
            $monthly_income = (float)$row_income['monthly_income'];
        }
        $monthlyIncomeData[] = $monthly_income;

        // 查询该月成本
        $cost_compare_filter = '';
        if (!empty($gcid)) {
            $cost_compare_filter = " AND projectid = '$gcid'";
        }
        $sql_cost_compare = "SELECT COALESCE(SUM(wccz), 0) as monthly_cost FROM tuqoa_xmcztjb WHERE DATE_FORMAT(sbrq, '%Y-%m') = '$month' $cost_compare_filter";
        $result_cost_compare = mysqli_query($link, $sql_cost_compare);
        $monthly_cost_compare = 0;
        if ($result_cost_compare) {
            $row_cost_compare = mysqli_fetch_assoc($result_cost_compare);
            $monthly_cost_compare = (float)$row_cost_compare['monthly_cost'];
        }
        $monthlyCostCompareData[] = $monthly_cost_compare;
    }

    // 项目成本对比数据（前5个项目）
    $projectCostLabels = [];
    $projectPlanCosts = [];
    $projectActualCosts = [];
    $project_cost_filter = '';
    if (!empty($gcid)) {
        $project_cost_filter = " AND p.id = '$gcid'";
    }
    $sql_project_cost = "SELECT p.gcname, p.zaojia,
                         COALESCE(SUM(c.wccz), 0) as actual_cost,
                         COALESCE(SUM(h.fwf), 0) * 0.8 as plan_cost
                         FROM tuqoa_gcproject p
                         LEFT JOIN tuqoa_xmcztjb c ON p.id = c.projectid
                         LEFT JOIN tuqoa_htgl h ON p.id = h.projectid
                         WHERE p.xmzt in ('新开工项目','在建项目','完工未结算') $project_cost_filter
                         GROUP BY p.id, p.gcname, p.zaojia
                         ORDER BY actual_cost DESC
                         LIMIT 5";
    $result_project_cost = mysqli_query($link, $sql_project_cost);
    if ($result_project_cost) {
        while ($row_pc = mysqli_fetch_assoc($result_project_cost)) {
            $projectCostLabels[] = mb_substr($row_pc['gcname'], 0, 10) . '...';
            $projectPlanCosts[] = (float)$row_pc['plan_cost'];
            $projectActualCosts[] = (float)$row_pc['actual_cost'];
        }
    }

    // 月度成本控制率数据
    $controlRateLabels = [];
    $controlRateData = [];
    for ($i = 5; $i >= 0; $i--) {
        $month = date('Y-m', strtotime("-$i months"));
        $monthLabel = date('n', strtotime("-$i months")) . '月';
        $controlRateLabels[] = $monthLabel;

        // 计算该月的成本控制率
        $rate_filter = '';
        if (!empty($gcid)) {
            $rate_filter = " AND c.projectid = '$gcid'";
        }
        $sql_rate = "SELECT
                     COALESCE(SUM(c.wccz), 0) as actual_cost,
                     COALESCE(SUM(h.fwf), 0) * 0.8 as plan_cost
                     FROM tuqoa_xmcztjb c
                     LEFT JOIN tuqoa_htgl h ON c.projectid = h.projectid
                     WHERE DATE_FORMAT(c.sbrq, '%Y-%m') = '$month' $rate_filter";
        $result_rate = mysqli_query($link, $sql_rate);
        $rate = 75; // 默认值
        if ($result_rate) {
            $row_rate = mysqli_fetch_assoc($result_rate);
            $actual = (float)$row_rate['actual_cost'];
            $plan = (float)$row_rate['plan_cost'];
            if ($plan > 0) {
                $rate = round(($actual / $plan) * 100, 1);
                $rate = min($rate, 120); // 限制最大值
            }
        }
        $controlRateData[] = $rate;
    }
    ?>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 项目选择器变化事件
            const projectSelect = document.getElementById('project-select');
            if (projectSelect) {
                projectSelect.addEventListener('change', function() {
                    const selectedProject = this.value;

                    // 这里可以添加查询逻辑，例如AJAX请求获取数据
                    console.log('查询项目:', selectedProject);
                });
            }

            // 年份选择器变化事件
            const yearSelect = document.getElementById('year-select');
            if (yearSelect) {
                yearSelect.addEventListener('change', function() {
                    const selectedYear = this.value;

                    // 这里可以添加查询逻辑，例如AJAX请求获取数据
                    console.log('查询年份:', selectedYear);
                });
            }
            
            // 查询按钮点击事件
            const queryBtn = document.getElementById('query-btn');
            if (queryBtn) {
                queryBtn.addEventListener('click', function() {
                    const selectedProject = document.getElementById('project-select').value;
                    const selectedYear = document.getElementById('year-select').value;

                    // 这里可以添加查询逻辑，例如AJAX请求获取数据
                    console.log('查询项目:', selectedProject, '年份:', selectedYear);
                    // 模拟数据刷新
                    updateLastUpdateTime();
                    //alert('已更新数据，项目: ' + selectedProject + ', 年份: ' + selectedYear);
                });
            }
            
            // 初始化图表
            initCharts();
        });
        
        // 更新最后更新时间
        function updateLastUpdateTime() {
            const now = new Date();
            const formattedDate = now.getFullYear() + '-' + 
                                 String(now.getMonth() + 1).padStart(2, '0') + '-' + 
                                 String(now.getDate()).padStart(2, '0') + ' ' + 
                                 String(now.getHours()).padStart(2, '0') + ':' + 
                                 String(now.getMinutes()).padStart(2, '0') + ':' + 
                                 String(now.getSeconds()).padStart(2, '0');
            document.getElementById('last-update-time').textContent = formattedDate;
        }
        
        // 初始化图表
        function initCharts() {
            // 月度成本趋势图表
            const monthlyCostCtx = document.getElementById('monthlyCostChart').getContext('2d');
            new Chart(monthlyCostCtx, {
                type: 'line',
                data: {
                    labels: <?php echo json_encode($monthlyLabels); ?>,
                    datasets: [
                        {
                            label: '计划成本',
                            data: <?php echo json_encode($monthlyPlanData); ?>,
                            borderColor: '#2196f3',
                            backgroundColor: 'rgba(33, 150, 243, 0.1)',
                            fill: true,
                            tension: 0.4
                        },
                        {
                            label: '实际成本',
                            data: <?php echo json_encode($monthlyCostData); ?>,
                            borderColor: '#4caf50',
                            backgroundColor: 'rgba(76, 175, 80, 0.1)',
                            fill: true,
                            tension: 0.4
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '成本 (万元)'
                            }
                        }
                    }
                }
            });

            // 月度收入vs成本对比图表
            const incomeVsCostCtx = document.getElementById('incomeVsCostChart').getContext('2d');
            new Chart(incomeVsCostCtx, {
                type: 'bar',
                data: {
                    labels: <?php echo json_encode($monthlyCompareLabels); ?>,
                    datasets: [
                        {
                            label: '月度收入',
                            data: <?php echo json_encode($monthlyIncomeData); ?>,
                            backgroundColor: '#4caf50',
                            borderColor: '#388e3c',
                            borderWidth: 1,
                            borderRadius: 4
                        },
                        {
                            label: '月度成本',
                            data: <?php echo json_encode($monthlyCostCompareData); ?>,
                            backgroundColor: '#f44336',
                            borderColor: '#d32f2f',
                            borderWidth: 1,
                            borderRadius: 4
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '金额 (万元)'
                            },
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top'
                        }
                    }
                }
            });

            // 成本控制率趋势图表
            const costControlCtx = document.getElementById('costControlChart').getContext('2d');
            new Chart(costControlCtx, {
                type: 'line',
                data: {
                    labels: <?php echo json_encode($controlRateLabels); ?>,
                    datasets: [{
                        label: '成本控制率',
                        data: <?php echo json_encode($controlRateData); ?>,
                        borderColor: '#4caf50',
                        backgroundColor: 'rgba(76, 175, 80, 0.1)',
                        fill: true,
                        tension: 0.4,
                        pointBackgroundColor: '#4caf50',
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2,
                        pointRadius: 5
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 120,
                            title: {
                                display: true,
                                text: '控制率 (%)'
                            },
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            }
                        },
                        x: {
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top'
                        }
                    }
                }
            });

            // 项目状态分布图表
            const projectStatusCtx = document.getElementById('projectStatusChart').getContext('2d');
            new Chart(projectStatusCtx, {
                type: 'bar',
                data: {
                    labels: <?php echo json_encode($projectStatusLabels); ?>,
                    datasets: [{
                        label: '项目数量',
                        data: <?php echo json_encode($projectStatusData); ?>,
                        backgroundColor: [
                            '#4caf50', // 在建项目 - 绿色
                            '#2196f3', // 新开工项目 - 蓝色
                            '#ff9800', // 完工未结算 - 橙色
                            '#9c27b0', // 完工项目 - 紫色
                            '#f44336', // 延期项目 - 红色
                            '#607d8b'  // 暂停项目 - 灰色
                        ],
                        borderColor: '#fff',
                        borderWidth: 2,
                        borderRadius: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '项目数量'
                            },
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });

            // 项目分布图表
            const projectDistributionCtx = document.getElementById('projectDistributionChart').getContext('2d');
            new Chart(projectDistributionCtx, {
                type: 'doughnut',
                data: {
                    labels: <?php echo json_encode($projectDistributionLabels); ?>,
                    datasets: [{
                        data: <?php echo json_encode($projectDistributionData); ?>,
                        backgroundColor: [
                            '#2196f3',
                            '#4caf50',
                            '#ff9800',
                            '#9c27b0',
                            '#f44336',
                            '#607d8b',
                            '#795548',
                            '#009688'
                        ],
                        borderColor: '#fff',
                        borderWidth: 3
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 15,
                                usePointStyle: true,
                                font: {
                                    size: 12
                                }
                            }
                        }
                    }
                }
            });

            // 项目成本对比分析图表
            const costSummaryCtx = document.getElementById('costSummaryChart').getContext('2d');
            new Chart(costSummaryCtx, {
                type: 'bar',
                data: {
                    labels: <?php echo json_encode($projectCostLabels); ?>,
                    datasets: [
                        {
                            label: '计划成本',
                            data: <?php echo json_encode($projectPlanCosts); ?>,
                            backgroundColor: '#2196f3',
                            borderColor: '#1976d2',
                            borderWidth: 1,
                            borderRadius: 4
                        },
                        {
                            label: '实际成本',
                            data: <?php echo json_encode($projectActualCosts); ?>,
                            backgroundColor: '#4caf50',
                            borderColor: '#388e3c',
                            borderWidth: 1,
                            borderRadius: 4
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '成本 (万元)'
                            },
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top'
                        }
                    }
                }
            });
        }

        // 重置筛选器函数
        function resetFilters() {
            const projectSelect = document.getElementById('project-select');
            const yearSelect = document.getElementById('year-select');
            const form = document.querySelector('form');

            if (projectSelect) projectSelect.value = '';
            if (yearSelect) yearSelect.value = '<?php echo date('Y'); ?>';

            // 提交表单以重置数据
            if (form) form.submit();
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化图表
            initCharts();

            // 为筛选器添加变化监听
            const projectSelect = document.getElementById('project-select');
            const yearSelect = document.getElementById('year-select');

            // 可以添加实时筛选功能（可选）
            // if (projectSelect) {
            //     projectSelect.addEventListener('change', function() {
            //         // 实时筛选逻辑
            //     });
            // }

            // 更新最后更新时间
            function updateTime() {
                const now = new Date();
                const timeString = now.getFullYear() + '-' +
                    String(now.getMonth() + 1).padStart(2, '0') + '-' +
                    String(now.getDate()).padStart(2, '0') + ' ' +
                    String(now.getHours()).padStart(2, '0') + ':' +
                    String(now.getMinutes()).padStart(2, '0') + ':' +
                    String(now.getSeconds()).padStart(2, '0');

                const timeElement = document.getElementById('last-update-time');
                if (timeElement) {
                    timeElement.textContent = timeString;
                }
            }

            // 立即更新一次时间
            updateTime();

            // 每30秒更新一次时间
            setInterval(updateTime, 30000);
        });
    </script>
</body>
</html>