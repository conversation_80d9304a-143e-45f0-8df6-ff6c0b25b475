<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试用例文档</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 10px;
            font-size: 2.5rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .subtitle {
            text-align: center;
            color: #7f8c8d;
            margin-bottom: 30px;
            font-size: 1.1rem;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }
        h3 {
            color: #2980b9;
            margin-top: 20px;
        }
        .test-case {
            background: #f8f9fa;
            padding: 20px;
            margin: 15px 0;
            border-radius: 10px;
            border-left: 4px solid #3498db;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .functional { border-left-color: #28a745; }
        .performance { border-left-color: #ffc107; }
        .security { border-left-color: #dc3545; }
        .integration { border-left-color: #6f42c1; }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .table th, .table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
            font-size: 14px;
        }
        .table th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            font-weight: 600;
        }
        .priority {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: bold;
        }
        .priority-high { background: #dc3545; color: white; }
        .priority-medium { background: #ffc107; color: #212529; }
        .priority-low { background: #28a745; color: white; }
        .test-steps {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .expected-result {
            background: #d4edda;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 3px solid #28a745;
        }
        .test-data {
            background: #fff3cd;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 3px solid #ffc107;
        }
        .code-snippet {
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 5px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            overflow-x: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 测试用例文档</h1>
        <p class="subtitle">系统功能测试用例和测试策略</p>
        
        <h2>📋 测试策略概述</h2>
        
        <table class="table">
            <thead>
                <tr>
                    <th>测试类型</th>
                    <th>测试范围</th>
                    <th>优先级</th>
                    <th>测试工具</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>功能测试</td>
                    <td>所有业务功能</td>
                    <td><span class="priority priority-high">高</span></td>
                    <td>手动测试 + Selenium</td>
                </tr>
                <tr>
                    <td>性能测试</td>
                    <td>核心页面加载</td>
                    <td><span class="priority priority-medium">中</span></td>
                    <td>JMeter + LoadRunner</td>
                </tr>
                <tr>
                    <td>安全测试</td>
                    <td>SQL注入、XSS</td>
                    <td><span class="priority priority-high">高</span></td>
                    <td>OWASP ZAP + 手动</td>
                </tr>
                <tr>
                    <td>兼容性测试</td>
                    <td>浏览器兼容</td>
                    <td><span class="priority priority-low">低</span></td>
                    <td>BrowserStack</td>
                </tr>
            </tbody>
        </table>

        <h2>🔧 功能测试用例</h2>

        <div class="test-case functional">
            <h3>TC001 - 项目数据汇总页面测试</h3>
            <p><strong>测试页面：</strong>gsxmsjhz.php</p>
            <p><strong>优先级：</strong><span class="priority priority-high">高</span></p>
            <p><strong>测试目标：</strong>验证项目数据汇总功能的正确性</p>
            
            <div class="test-data">
                <h4>测试数据</h4>
                <ul>
                    <li>项目数据：至少包含3个不同状态的项目</li>
                    <li>时间范围：2024-01-01 到 2024-12-31</li>
                    <li>合同数据：每个项目至少1个合同</li>
                    <li>成本数据：每个项目至少3个月的成本记录</li>
                </ul>
            </div>
            
            <div class="test-steps">
                <h4>测试步骤</h4>
                <ol>
                    <li>访问 gsxmsjhz.php 页面</li>
                    <li>选择日期范围：2024-01-01 到 2024-06-30</li>
                    <li>点击"查询"按钮</li>
                    <li>验证统计卡片数据显示</li>
                    <li>验证项目状态分布图表</li>
                    <li>验证月度趋势图表</li>
                    <li>验证项目明细表格</li>
                </ol>
            </div>
            
            <div class="expected-result">
                <h4>预期结果</h4>
                <ul>
                    <li>页面在3秒内加载完成</li>
                    <li>统计卡片显示正确的项目总数、合同总额、成本总额</li>
                    <li>图表正确显示数据分布和趋势</li>
                    <li>表格显示符合筛选条件的项目列表</li>
                    <li>所有数值计算准确无误</li>
                </ul>
            </div>
        </div>

        <div class="test-case functional">
            <h3>TC002 - 经营动态监控测试</h3>
            <p><strong>测试页面：</strong>jydt.php</p>
            <p><strong>优先级：</strong><span class="priority priority-high">高</span></p>
            <p><strong>测试目标：</strong>验证经营动态实时监控功能</p>
            
            <div class="test-steps">
                <h4>测试步骤</h4>
                <ol>
                    <li>访问 jydt.php 页面</li>
                    <li>验证默认显示当月数据</li>
                    <li>修改日期范围为上个月</li>
                    <li>点击"查询"按钮</li>
                    <li>验证合同统计数据更新</li>
                    <li>验证员工统计数据</li>
                    <li>验证最新合同列表</li>
                </ol>
            </div>
            
            <div class="expected-result">
                <h4>预期结果</h4>
                <ul>
                    <li>页面默认显示当月数据</li>
                    <li>日期筛选功能正常工作</li>
                    <li>统计数据与数据库一致</li>
                    <li>合同列表按时间倒序排列</li>
                    <li>员工统计数据准确</li>
                </ul>
            </div>
        </div>

        <div class="test-case functional">
            <h3>TC003 - 项目成本核算测试</h3>
            <p><strong>测试页面：</strong>xmcbhs.php</p>
            <p><strong>优先级：</strong><span class="priority priority-high">高</span></p>
            <p><strong>测试目标：</strong>验证成本核算计算的准确性</p>
            
            <div class="test-data">
                <h4>测试数据</h4>
                <div class="code-snippet">
项目ID: 1001
项目名称: 测试项目A
合同金额: 1000000.00
实际成本: 800000.00
预期成本控制率: 80%
                </div>
            </div>
            
            <div class="test-steps">
                <h4>测试步骤</h4>
                <ol>
                    <li>选择测试项目A</li>
                    <li>选择年份：2024</li>
                    <li>点击"分析"按钮</li>
                    <li>验证成本控制率计算</li>
                    <li>验证月度成本趋势图</li>
                    <li>验证收入vs成本对比图</li>
                </ol>
            </div>
            
            <div class="expected-result">
                <h4>预期结果</h4>
                <ul>
                    <li>成本控制率显示为80%</li>
                    <li>状态显示为"控制良好"（绿色）</li>
                    <li>月度趋势图正确显示12个月数据</li>
                    <li>对比图显示收入和成本的关系</li>
                </ul>
            </div>
        </div>

        <h2>⚡ 性能测试用例</h2>

        <div class="test-case performance">
            <h3>TC101 - 页面加载性能测试</h3>
            <p><strong>测试目标：</strong>验证核心页面的加载性能</p>
            <p><strong>优先级：</strong><span class="priority priority-medium">中</span></p>
            
            <table class="table">
                <thead>
                    <tr>
                        <th>页面</th>
                        <th>期望加载时间</th>
                        <th>并发用户数</th>
                        <th>测试工具</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>gsxmsjhz.php</td>
                        <td>&lt; 3秒</td>
                        <td>50</td>
                        <td>JMeter</td>
                    </tr>
                    <tr>
                        <td>jydt.php</td>
                        <td>&lt; 2秒</td>
                        <td>100</td>
                        <td>JMeter</td>
                    </tr>
                    <tr>
                        <td>xmcbhs.php</td>
                        <td>&lt; 4秒</td>
                        <td>30</td>
                        <td>JMeter</td>
                    </tr>
                </tbody>
            </table>
            
            <div class="test-steps">
                <h4>测试配置</h4>
                <div class="code-snippet">
# JMeter测试配置
Thread Group:
  - Number of Threads: 50
  - Ramp-up Period: 60s
  - Loop Count: 10

HTTP Request:
  - Server: localhost
  - Port: 80
  - Path: /reportforms/jyfx/gsxmsjhz.php
  - Method: GET
                </div>
            </div>
        </div>

        <div class="test-case performance">
            <h3>TC102 - 数据库查询性能测试</h3>
            <p><strong>测试目标：</strong>验证复杂查询的执行性能</p>
            
            <div class="test-steps">
                <h4>测试查询</h4>
                <div class="code-snippet">
-- 测试查询1：项目汇总统计
SELECT COUNT(*) as total, 
       SUM(zaojia) as total_amount,
       AVG(zaojia) as avg_amount
FROM tuqoa_gcproject 
WHERE xmzt IN ('新开工项目','在建项目','完工未结算')
AND qdsj >= '2024-01-01' AND qdsj <= '2024-12-31';

-- 测试查询2：多表关联查询
SELECT p.gcname, h.fwf, c.wccz
FROM tuqoa_gcproject p
LEFT JOIN tuqoa_htgl h ON p.id = h.projectid
LEFT JOIN tuqoa_xmcztjb c ON p.id = c.projectid
WHERE p.xmzt = '在建项目';
                </div>
            </div>
            
            <div class="expected-result">
                <h4>性能要求</h4>
                <ul>
                    <li>单表查询：&lt; 100ms</li>
                    <li>多表关联查询：&lt; 500ms</li>
                    <li>聚合统计查询：&lt; 200ms</li>
                    <li>分页查询：&lt; 150ms</li>
                </ul>
            </div>
        </div>

        <h2>🔒 安全测试用例</h2>

        <div class="test-case security">
            <h3>TC201 - SQL注入测试</h3>
            <p><strong>测试目标：</strong>验证系统对SQL注入攻击的防护</p>
            <p><strong>优先级：</strong><span class="priority priority-high">高</span></p>
            
            <div class="test-steps">
                <h4>测试向量</h4>
                <div class="code-snippet">
# 测试用例1：基本SQL注入
start_date = "2024-01-01' OR '1'='1"
end_date = "2024-12-31' OR '1'='1"

# 测试用例2：联合查询注入
project_id = "1 UNION SELECT username,password FROM users--"

# 测试用例3：时间盲注
start_date = "2024-01-01' AND (SELECT SLEEP(5))--"

# 测试用例4：布尔盲注
project_id = "1' AND (SELECT COUNT(*) FROM tuqoa_userinfo)>0--"
                </div>
            </div>
            
            <div class="expected-result">
                <h4>预期结果</h4>
                <ul>
                    <li>所有SQL注入尝试都被阻止</li>
                    <li>系统返回错误信息而不是敏感数据</li>
                    <li>日志记录攻击尝试</li>
                    <li>页面正常显示错误提示</li>
                </ul>
            </div>
        </div>

        <div class="test-case security">
            <h3>TC202 - XSS跨站脚本测试</h3>
            <p><strong>测试目标：</strong>验证输入输出的XSS防护</p>
            
            <div class="test-steps">
                <h4>测试向量</h4>
                <div class="code-snippet">
# 反射型XSS
&lt;script&gt;alert('XSS')&lt;/script&gt;
&lt;img src=x onerror=alert('XSS')&gt;
javascript:alert('XSS')

# 存储型XSS（如果有输入功能）
&lt;svg onload=alert('XSS')&gt;
&lt;iframe src=javascript:alert('XSS')&gt;&lt;/iframe&gt;
                </div>
            </div>
        </div>

        <h2>🔗 集成测试用例</h2>

        <div class="test-case integration">
            <h3>TC301 - 数据一致性测试</h3>
            <p><strong>测试目标：</strong>验证不同页面间数据的一致性</p>
            
            <div class="test-steps">
                <h4>测试步骤</h4>
                <ol>
                    <li>在gsxmsjhz.php查看项目总数</li>
                    <li>在jydt.php查看相同时间范围的项目数据</li>
                    <li>在xmcbhs.php查看具体项目的成本数据</li>
                    <li>对比各页面显示的数据是否一致</li>
                </ol>
            </div>
            
            <div class="expected-result">
                <h4>预期结果</h4>
                <ul>
                    <li>相同查询条件下，各页面显示的基础数据一致</li>
                    <li>统计数据的计算结果一致</li>
                    <li>时间筛选的逻辑一致</li>
                </ul>
            </div>
        </div>

        <h2>📊 测试执行计划</h2>

        <table class="table">
            <thead>
                <tr>
                    <th>测试阶段</th>
                    <th>测试内容</th>
                    <th>执行时间</th>
                    <th>负责人</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>第一阶段</td>
                    <td>核心功能测试</td>
                    <td>3天</td>
                    <td>测试工程师</td>
                </tr>
                <tr>
                    <td>第二阶段</td>
                    <td>性能和安全测试</td>
                    <td>2天</td>
                    <td>高级测试工程师</td>
                </tr>
                <tr>
                    <td>第三阶段</td>
                    <td>集成和回归测试</td>
                    <td>2天</td>
                    <td>测试团队</td>
                </tr>
                <tr>
                    <td>第四阶段</td>
                    <td>用户验收测试</td>
                    <td>1天</td>
                    <td>业务用户</td>
                </tr>
            </tbody>
        </table>

        <h2>🛠️ 自动化测试脚本示例</h2>

        <div class="test-case">
            <h3>Selenium自动化测试脚本</h3>
            <div class="code-snippet">
from selenium import webdriver
from selenium.webdriver.common.by import By
import time

class ProjectDataTest:
    def setUp(self):
        self.driver = webdriver.Chrome()
        self.base_url = "http://localhost/reportforms/jyfx/"
    
    def test_project_summary(self):
        driver = self.driver
        driver.get(self.base_url + "gsxmsjhz.php")
        
        # 设置日期范围
        start_date = driver.find_element(By.NAME, "start-date")
        start_date.clear()
        start_date.send_keys("2024-01-01")
        
        end_date = driver.find_element(By.NAME, "end-date")
        end_date.clear()
        end_date.send_keys("2024-06-30")
        
        # 点击查询按钮
        query_btn = driver.find_element(By.ID, "query-btn")
        query_btn.click()
        
        # 等待页面加载
        time.sleep(3)
        
        # 验证结果
        total_projects = driver.find_element(By.ID, "total-projects").text
        assert int(total_projects) > 0, "项目总数应该大于0"
        
        print("测试通过：项目数据汇总功能正常")
    
    def tearDown(self):
        self.driver.quit()
            </div>
        </div>

        <div style="text-align: center; margin-top: 40px; color: #7f8c8d;">
            <p>📅 文档版本：v1.0</p>
            <p>🔄 最后更新：2025年8月7日</p>
            <p>📧 测试问题反馈：<EMAIL></p>
        </div>
    </div>
</body>
</html>
